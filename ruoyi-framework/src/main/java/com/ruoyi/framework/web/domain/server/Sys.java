package com.ruoyi.framework.web.domain.server;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 系统相关信息
 *
 * <AUTHOR>
 */

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Sys {
    /**
     * 服务器名称
     */

    private String computerName;

    /**
     * 服务器Ip
     */

    private String computerIp;

    /**
     * 项目路径
     */

    private String userDir;

    /**
     * 操作系统
     */

    private String osName;

    /**
     * 系统架构
     */

    private String osArch;

}

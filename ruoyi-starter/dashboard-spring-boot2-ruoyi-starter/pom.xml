<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
        <artifactId>ruoyi-starter</artifactId>
        <version>dfe-zh-ruoyi-lowcode-boot2-1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>dashboard-spring-boot2-ruoyi-starter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.gccloud</groupId>
            <artifactId>dashboard-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>ant</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>ant-launcher</artifactId>
                    <groupId>org.apache.ant</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-cli</artifactId>
                    <groupId>commons-cli</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>error_prone_annotations</artifactId>
                    <groupId>com.google.errorprone</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis</artifactId>
                    <groupId>org.mybatis</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.github.xiaoymin</groupId>
                    <artifactId>swagger-bootstrap-ui</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.swagger</groupId>
                    <artifactId>swagger-annotations</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>
    </dependencies>

</project>

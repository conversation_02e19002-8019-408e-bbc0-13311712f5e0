package com.ruoyi.rocketmq.annotation;

import org.springframework.stereotype.Repository;

import java.lang.annotation.*;

/**
 * 消息处理类标注（根据topic和tag区分不同消息处理类）
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE})
@Repository
public @interface RocketMQHandlerActualizer {
    /**
     * 消息主题
     */
    String topic() default "";

    /**
     * 消息标签,如果是该主题下所有的标签，使用“*”
     */
    String[] tags() default "*";

    /**
     * 消息处理类备注
     **/
    String remark() default "";
}

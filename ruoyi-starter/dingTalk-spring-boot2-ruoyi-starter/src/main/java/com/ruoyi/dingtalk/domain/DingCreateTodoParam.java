package com.ruoyi.dingtalk.domain;


import com.ruoyi.common.enums.DDTodoPriorityStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class DingCreateTodoParam {

    private String operatorMobile;

    private List<String> executorMobiles;

    private String subject;

    private String description;

    private DDTodoPriorityStatus status;

    private String sourceId;

    private String appUrl;

    private String pcUrl;
}

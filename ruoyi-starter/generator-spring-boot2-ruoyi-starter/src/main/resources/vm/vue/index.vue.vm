<template>
  <div class="app-container search-table-box aidex-table">
    <el-card shadow="never" ref="queryRef" style="margin-bottom: 12px;" class="search_card" v-show="showSearch">
      <div class="filter-container">
        <div class="search_box">
          <el-form :model="queryParams" ref="queryForm" label-width="80px">
            <el-row :gutter="16">
                #set($queryCount=0)
                #foreach($column in $columns)
                    #if($column.query)
                        #set($dictType=$column.dictType)
                        #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                        #set($parentheseIndex=$column.columnComment.indexOf("（"))
                        #if($parentheseIndex != -1)
                            #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                        #else
                            #set($comment=$column.columnComment)
                        #end
                        #if($column.htmlType == "input")
                            #set($queryCount=$queryCount + 1)
                          <el-col :md="6" #if( $queryCount > 3 )v-if="advanced"#end>
                            <el-form-item label="${comment}" prop="${column.javaField}">
                              <el-input v-model="queryParams.${column.javaField}" placeholder="请输入${comment}" clearable @keyup.enter.native="handleQuery"/>
                            </el-form-item>
                          </el-col>
                        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && "" != $dictType)
                            #set($queryCount=$queryCount + 1)
                          <el-col :md="6" #if( $queryCount > 3 )v-if="advanced"#end>
                            <el-form-item label="${comment}" prop="${column.javaField}">
                              <el-select v-model="queryParams.${column.javaField}" placeholder="请选择${comment}" @change="handleQuery" clearable filterable style="width: 100%;">
                                <el-option v-for="dict in dict.type.${dictType}" :key="dict.value" :label="dict.label" :value="dict.value"/>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        #elseif(($column.htmlType == "select" || $column.htmlType == "radio") && $dictType)
                            #set($queryCount=$queryCount + 1)
                          <el-col :md="6" #if( $queryCount > 3 )v-if="advanced"#end>
                            <el-form-item label="${comment}" prop="${column.javaField}">
                              <el-select v-model="queryParams.${column.javaField}" style="width: 100%" placeholder="请选择${comment}" @change="handleQuery" clearable filterable style="width: 100%;">
                                <el-option label="请选择字典生成" value=""/>
                              </el-select>
                            </el-form-item>
                          </el-col>
                        #elseif($column.htmlType == "datetime" && $column.queryType != "BETWEEN")
                            #set($queryCount=$queryCount + 1)
                          <el-col :md="6" #if( $queryCount > 3 )v-if="advanced"#end>
                            <el-form-item label="${comment}" prop="${column.javaField}">
                              <el-date-picker clearable v-model="queryParams.${column.javaField}" type="date" style="width: 100%" value-format="yyyy-MM-dd" placeholder="请选择${comment}"/>
                            </el-form-item>
                          </el-col>
                        #elseif($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                            #set($queryCount=$queryCount + 1)
                          <el-col :md="6" #if( $queryCount > 3 )v-if="advanced"#end>
                            <el-form-item label="${comment}">
                              <el-date-picker v-model="daterange${AttrName}" style="width: 100%" value-format="yyyy-MM-dd" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"/>
                            </el-form-item>
                          </el-col>
                        #end
                    #end
                #end
              <el-col :md="6" align="right" style="float: right;">
                <el-form-item>
                  <el-button class="filter-item" type="primary" @click="handleQuery">搜索</el-button>
                  <el-button class="filter-item" style="margin-left: 8px" @click="resetQuery">重置</el-button>
                    #if($queryCount > 3)
                      <a @click="toggleAdvanced" style="margin:0 4px 0 8px ;vertical-align: middle;">{{ advanced ? '收起' : '展开' }}
                        <i :class="advanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                      </a>
                    #end
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-card>

    <el-card shadow="never">
      <template #header>
        <el-row>
          <el-col :span="8">
            <div class="card-header">
              <el-button disabled type="text">${functionName}信息</el-button>
            </div>
          </el-col>
          <el-col :span="16">
            <div class="btn_box" align="right" style="float: right;">
              <el-button class="filter-item" style="margin-left: 8px;" type="primary" icon="el-icon-plus" @click="handleAdd" v-hasPermi="['${moduleName}:${businessName}:add']">新增</el-button>
              <el-button class="filter-item" style="margin-left: 8px;" icon="el-icon-delete" type="danger" :disabled="multiple" @click="handleDelete" v-if="!multiple" v-hasPermi="['${moduleName}:${businessName}:remove']">删除</el-button>
              <el-button class="filter-item" style="margin-left: 8px;" icon="el-icon-download" @click="handleExport" v-hasPermi="['${moduleName}:${businessName}:export']">导出</el-button>
              <right-toolbar :showSearch.sync="showSearch" @showSearchFun="showSearchFun" @queryTable="getList"/>
            </div>
          </el-col>
        </el-row>
      </template>

      <el-table stripe ref="tableRef" v-loading="loading" :data="${businessName}List" @selection-change="handleSelectionChange" highlight-current-row style="width: 100%;" :height="tableHeight">
        <el-table-column type="selection" width="45" align="center"/>
          #foreach($column in $columns)
              #set($javaField=$column.javaField)
              #set($parentheseIndex=$column.columnComment.indexOf("（"))
              #if($parentheseIndex != -1)
                  #set($comment=$column.columnComment.substring(0, $parentheseIndex))
              #else
                  #set($comment=$column.columnComment)
              #end
              #if($column.pk)
                <el-table-column label="${comment}" align="center" prop="${javaField}"/>
              #elseif($column.list && $column.htmlType == "datetime")
                <el-table-column label="${comment}" align="center" prop="${javaField}" width="180">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.${javaField}, '{y}-{m}-{d}') }}</span>
                  </template>
                </el-table-column>
              #elseif($column.list && $column.htmlType == "imageUpload")
                <el-table-column label="${comment}" align="center" prop="${javaField}" width="100">
                  <template slot-scope="scope">
                    <image-preview :src="scope.row.${javaField}" :width="50" :height="50"/>
                  </template>
                </el-table-column>
              #elseif($column.list && "" != $column.dictType)
                <el-table-column label="${comment}" align="center" prop="${javaField}">
                  <template slot-scope="scope">
                      #if($column.htmlType == "checkbox")
                        <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.${javaField} ? scope.row.${javaField}.split(',') : []"/>
                      #else
                        <dict-tag :options="dict.type.${column.dictType}" :value="scope.row.${javaField}"/>
                      #end
                  </template>
                </el-table-column>
              #elseif($column.list && "" != $javaField)
                <el-table-column label="${comment}" align="center" prop="${javaField}"/>
              #end
          #end
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="220">
          <template slot-scope="scope">
              #if($table.isAct=='Y')
                <apply-before v-if="!scope.row.instanceId || scope.row.status == 0" :row="scope.row" :handleUpdate="handleUpdate" :handle-view="handleView" :handleDelete="handleDelete" :requestMapping="requestMapping" @getList="getList"/>
                <el-divider direction="vertical"/>
                <apply-after v-if="scope.row.instanceId && scope.row.status != 0" :row="scope.row" :taskId="scope.row.taskId" :type="scope.row.type" @getList="getList"/>
              #else
                <el-button type="text" @click="handleView(scope.row)" v-hasPermi="['${moduleName}:${businessName}:edit']">查看</el-button>
                <el-divider direction="vertical" v-hasPermi="['${moduleName}:${businessName}:edit']"/>
                <el-button type="text" @click="handleUpdate(scope.row)" v-hasPermi="['${moduleName}:${businessName}:edit']">修改</el-button>
                <el-divider direction="vertical" v-hasPermi="['${moduleName}:${businessName}:remove']"/>
                <el-button type="text" style="color: red;" @click="handleDelete(scope.row)" v-hasPermi="['${moduleName}:${businessName}:remove']">删除</el-button>
              #end
          </template>
        </el-table-column>
        <div slot="empty">
          <svg-icon icon-class="search-none" style="font-size: 64px;"></svg-icon>
          <p>暂无数据</p>
        </div>
      </el-table>
      <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList"/>
      <!-- 添加或修改${functionName}对话框 -->
      <el-dialog :title="title" :visible.sync="open" width="720px" top="10vh" append-to-body>
        <div style="height: 490px;overflow: auto; padding: 12px 24px;">
          <el-form ref="form" :model="form" :rules="rules" label-position="top" :disabled="!isUpdate">
            <el-row :gutter="24">
                #foreach($column in $columns)
                    #set($field=$column.javaField)
                    #if($column.insert && !$column.pk)
                        #if(($column.usableColumn) || (!$column.superColumn))
                            #set($parentheseIndex=$column.columnComment.indexOf("（"))
                            #if($parentheseIndex != -1)
                                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                            #else
                                #set($comment=$column.columnComment)
                            #end
                            #set($dictType=$column.dictType)
                            #if($column.htmlType == "input")
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-input v-model="form.${field}" placeholder="请输入${comment}"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "imageUpload")
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <image-upload v-model="form.${field}"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "fileUpload")
                              <el-col :span="24">
                                <el-form-item label="${comment}" prop="${field}">
                                  <file-upload v-model="form.${field}"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "editor")
                              <el-col :span="24">
                                <el-form-item label="${comment}">
                                  <editor v-model="form.${field}" :min-height="192"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "select" && "" != $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-select v-model="form.${field}" placeholder="请选择${comment}" clearable filterable style="width: 100%;">
                                    <el-option v-for="dict in dict.type.${dictType}" :key="dict.value" :label="dict.label"#if($column.javaType == "Integer" || $column.javaType =="Long"):value="parseInt(dict.value)"#else:value="dict.value"#end/>
                                  </el-select>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "select" && $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-select v-model="form.${field}" placeholder="请选择${comment}" style="width: 100%;" clearable filterable>
                                    <el-option label="请选择字典生成" value=""></el-option>
                                  </el-select>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "deptSelectTree")
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <!--todo 多选 :multiple="true"（单选没有）-->
                                  <treeselect v-model="form.${field}" :options="outerData.deptIdTreeOptions" placeholder="${comment}" style="max-width: 220px; float:left" :multiple="true"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "userSelectTree")
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <user-list v-model="form.${field}" placeholder="请输入${comment}" :multiple="true"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "checkbox" && "" != $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox v-for="dict in dict.type.${dictType}" :key="dict.value" :label="dict.value">{{dict.label}}</el-checkbox>
                                  </el-checkbox-group>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "checkbox" && $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-checkbox-group v-model="form.${field}">
                                    <el-checkbox>请选择字典生成</el-checkbox>
                                  </el-checkbox-group>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "radio" && "" != $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-radio-group v-model="form.${field}">
                                    <el-radio-button v-for="dict in dict.type.${dictType}" :key="dict.value"#if($column.javaType == "Integer" || $column.javaType =="Long"):label="parseInt(dict.value)"#else:label="dict.value"#end>{{dict.label}}</el-radio-button>
                                  </el-radio-group>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "radio" && $dictType)
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-radio-group v-model="form.${field}">
                                    <el-radio label="1">请选择字典生成</el-radio>
                                  </el-radio-group>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "datetime")
                              <el-col :span="12">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-date-picker clearable style="width: 100%;" v-model="form.${field}" type="date" value-format="yyyy-MM-dd" placeholder="请选择${comment}"/>
                                </el-form-item>
                              </el-col>
                            #elseif($column.htmlType == "textarea")
                              <el-col :span="24">
                                <el-form-item label="${comment}" prop="${field}">
                                  <el-input v-model="form.${field}" type="textarea" placeholder="请输入内容"/>
                                </el-form-item>
                              </el-col>
                            #end
                        #end
                    #end
                #end
                #if($table.sub)
                  <el-col :span="24">
                    <el-divider content-position="center">${subTable.functionName}信息</el-divider>
                  </el-col>
                  <el-col :span="24">
                    <el-button type="primary" icon="el-icon-plus" @click="handleAdd${subClassName}">添加</el-button>
                    <el-button type="danger" icon="el-icon-delete" @click="handleDelete${subClassName}">删除</el-button>
                  </el-col>
                  <el-col :span="24">
                    <el-table :data="${subclassName}List" :row-class-name="row${subClassName}Index" @selection-change="handle${subClassName}SelectionChange" style="width: 100%" height="300px" ref="${subclassName}">
                      <el-table-column type="selection" width="50" align="center"></el-table-column>
                      <el-table-column label="序号" align="center" prop="index" width="50"/>
                        #foreach($column in $subTable.columns)
                            #set($javaField=$column.javaField)
                            #set($parentheseIndex=$column.columnComment.indexOf("（"))
                            #if($parentheseIndex != -1)
                                #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                            #else
                                #set($comment=$column.columnComment)
                            #end
                            #if($column.pk || $javaField == ${subTableFkclassName})
                            #elseif($column.list && $column.htmlType == "imageUpload")
                              <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template slot-scope="scope">
                                  <image-upload v-model="scope.row.$javaField"/>
                                </template>
                              </el-table-column>
                            #elseif($column.list && $column.htmlType == "fileUpload")
                              <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template slot-scope="scope">
                                  <file-upload v-model="scope.row.$javaField"/>
                                </template>
                              </el-table-column>
                            #elseif($column.list && $column.htmlType == "input")
                              <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template slot-scope="scope">
                                  <el-input v-model="scope.row.$javaField" placeholder="请输入$comment"/>
                                </template>
                              </el-table-column>
                            #elseif($column.list && $column.htmlType == "datetime")
                              <el-table-column label="$comment" prop="${javaField}" width="240">
                                <template slot-scope="scope">
                                  <el-date-picker clearable v-model="scope.row.$javaField" type="date" value-format="yyyy-MM-dd" placeholder="请选择$comment"/>
                                </template>
                              </el-table-column>
                            #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") &&"" != $column.dictType)
                              <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template slot-scope="scope">
                                  <el-select v-model="scope.row.$javaField" placeholder="请选择$comment" clearable filterable>
                                    <el-option v-for="dict in dict.type.$column.dictType" :key="dict.value" :label="dict.label" :value="dict.value"/>
                                  </el-select>
                                </template>
                              </el-table-column>
                            #elseif($column.list && ($column.htmlType == "select" || $column.htmlType == "radio") &&"" == $column.dictType)
                              <el-table-column label="$comment" prop="${javaField}" width="150">
                                <template slot-scope="scope">
                                  <el-select v-model="scope.row.$javaField" placeholder="请选择$comment" clearable filterable>
                                    <el-option label="请选择字典生成" value=""/>
                                  </el-select>
                                </template>
                              </el-table-column>
                            #end
                        #end
                    </el-table>
                  </el-col>
                #end
            </el-row>
          </el-form>
        </div>
        <div v-if="isUpdate" slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </el-dialog>
    </el-card>
  </div>
</template>

<script>
        #if($table.isAct=='Y')
        import ApplyBefore from "@/components/Activiti/ApplyBefore/index";
        import ApplyAfter from "@/components/Activiti/ApplyAfter/index";
        import Treeselect from "@riophae/vue-treeselect";
        import "@riophae/vue-treeselect/dist/vue-treeselect.css";
        import {deptTreeSelect} from "@/api/system/user";
        import userList from "@/components/custom/userList/index";
        import {get${BusinessName}, list${BusinessName}} from "@/api/";
        #end
        #foreach($column in $columns)
            #if($column.htmlType == "deptSelectTree" || $column.htmlType == "userSelectTree")
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.htmlType == "deptSelectTree")
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.htmlType == "userSelectTree")
                #break
            #end
        #end

  export default {
    name: "${BusinessName}",
      #if(${dicts} != '')
        dicts: [${dicts}],
      #end
      #if($table.isAct=='Y')
        components: {
          ApplyBefore,
          ApplyAfter,
            #foreach($column in $columns)
                #if($column.htmlType == "deptSelectTree")
                  Treeselect,
                    #break
                #end
            #end
            #foreach($column in $columns)
                #if($column.htmlType == "userSelectTree")
                  userList,
                    #break
                #end
            #end
        },
      #end
    data() {
      return {
          #if( $queryCount > 3 )
            advanced: false,
          #end
        tableHeight: this.getInitTableHeight(),
        // 遮罩层
          #if($table.isAct=='Y')
            requestMapping: '/workflow/${businessName}',
          #end
        loading: true,
        isUpdate: true,
        // 选中数组
        ids: [],
          #if($table.sub)
            // 子表选中数据
            checked${subClassName}: [],
          #end
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 显示搜索条件
        showSearch: true,
        // 总条数
        total: 0,
        //${functionName}表格数据
              ${businessName}List: [],
          #if($table.sub)
            // ${subTable.functionName}表格数据
                  ${subclassName}List: [],
          #end
        // 弹出层标题
        title: "",
        // 是否显示弹出层
        open: false,
          #foreach ($column in $columns)
              #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                  #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                // $comment时间范围
                daterange${AttrName}: [],
              #end
          #end
        // 查询参数
        queryParams: {
          pageNum: 1,
          pageSize: this.defaultPageSize,
            #foreach ($column in $columns)
                #if($column.query)
                        $column.javaField: null#if($foreach.count != $columns.size()),#end
                #end
            #end
        },
          #foreach($column in $columns)
              #if($column.htmlType == "deptSelectTree")
                outerData: {
                  deptIdTreeOptions: [],
                },
                  #break
              #end
          #end
        // 表单参数
        form: {},
        // 表单校验
        rules: {
            #foreach ($column in $columns)
                #if($column.required)
                    #set($parentheseIndex=$column.columnComment.indexOf("（"))
                    #if($parentheseIndex != -1)
                        #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                    #else
                        #set($comment=$column.columnComment)
                    #end
                        $column.javaField: [
                    {
                      required: true, message: "$comment不能为空", trigger: #if($column.htmlType ==
                        "select" || $column.htmlType == "radio")"change"#else"blur"#end}
                  ]#if($foreach.count != $columns.size()),#end
                #end
            #end
        }
      };
    },
    watch: {},
    beforeCreate() {
    },
    created() {
      this.getList();
        #foreach($column in $columns)
            #if($column.htmlType == "deptSelectTree")
              //TODO 若重复引用请删除
              this.initOuterData();
                #break
            #end
        #end
    },
    beforeMount() {
    },
    mounted() {
    },
    beforeUpdate() {
    },
    updated() {
    },
    beforeUnmount() {
    },
    unmounted() {
    },
    errorCaptured() {
    },
    renderTracked() {
    },
    renderTriggered() {
    },
    activated() {
      this.$refs.tableRef.doLayout()
    },
    deactivated() {
    },
    beforeDestroy() {
    },
    destroyed() {
    },
    methods: {
      /** 隐藏搜索按钮操作 */
      showSearchFun(isShowSearch) {
        this.showSearch = isShowSearch
        let oldHeight = this.$refs.queryRef.$el.offsetHeight
        if (!isShowSearch) {
          //当前是显示状态
          oldHeight = oldHeight + 12
        } else {
          oldHeight = oldHeight - 12
        }
        this.$nextTick(() => (
            this.tableHeight = this.$refs.tableRef.$el.offsetHeight - (this.$refs.queryRef.$el.offsetHeight - oldHeight)
        ))
      },
        #if( $queryCount > 3 )
          /** 展开按钮操作 */
          toggleAdvanced() {
            const oldHeight = this.$refs.queryRef.$el.offsetHeight
            this.advanced = !this.advanced
            this.$nextTick(() => (
                this.tableHeight = this.$refs.tableRef.$el.offsetHeight - (this.$refs.queryRef.$el.offsetHeight - oldHeight)
            ))
          },
        #end
      /** 查询${functionName}列表 */
      async getList() {
        this.loading = true;
          #foreach ($column in $columns)
              #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                this.queryParams.params = {};
                  #break
              #end
          #end
          #foreach ($column in $columns)
              #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                  #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                if (null != this.daterange${AttrName} && '' != this.daterange${AttrName}) {
                  this.queryParams.params["begin${AttrName}"] = this.daterange${AttrName}[0];
                  this.queryParams.params["end${AttrName}"] = this.daterange${AttrName}[1];
                }
              #end
          #end
        const response = await list${BusinessName}(this.queryParams)
        let pageInfo = response.data;
        this.${businessName}List = pageInfo.list;
        this.total = pageInfo.total;
        this.loading = false;
      },
        #foreach($column in $columns)
            #if($column.htmlType == "deptSelectTree")
              initOuterData() {
                this.initDeptIdTreeOptions();
              },
              async initDeptIdTreeOptions() {
                this.outerData.deptIdTreeOptions = (await deptTreeSelect()).data
              },
                #break
            #end
        #end
        #foreach($column in $columns)
            #if($column.htmlType == "userSelectTree")
              setUserId([userIdKey, userId]) {
                this.form[userIdKey] = userId;
              },
                #break
            #end
        #end
      // 取消按钮
      cancel() {
        this.open = false;
        this.reset();
      },
      // 表单重置
      reset() {
        this.form = {
            #foreach ($column in $columns)
                #if($column.htmlType == "checkbox")
                        $column.javaField: []#if($foreach.count != $columns.size()),#end
                #else
                        $column.javaField: null#if($foreach.count != $columns.size()),#end
                #end
            #end
        };
          #if($table.sub)
            this.${subclassName}List = [];
          #end
        this.resetForm("form");
      },
      /** 搜索按钮操作 */
      handleQuery() {
        this.queryParams.pageNum = 1;
        this.getList();
      },
      /** 重置按钮操作 */
      resetQuery() {
          #foreach ($column in $columns)
              #if($column.htmlType == "datetime" && $column.queryType == "BETWEEN")
                  #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                this.daterange${AttrName} = [];
              #end
          #end
        this.resetForm("queryForm");
        this.handleQuery();
      },
      // 多选框选中数据
      handleSelectionChange(selection) {
        this.ids = selection.map(item => item.${pkColumn.javaField})
        this.single = selection.length !== 1
        this.multiple = !selection.length
      },
      /** 新增按钮操作 */
      handleAdd() {
        this.isUpdate = true
        this.reset();
        this.open = true;
        this.title = "添加${functionName}";
      },
      /** 修改按钮操作 */
      async handleUpdate(row) {
        this.isUpdate = true
        this.reset();
        const ${pkColumn.javaField} = row.${pkColumn.javaField} || this.ids
        const response = await get${BusinessName}(${pkColumn.javaField})
        this.form = response.data;
          #foreach ($column in $columns)
              #if($column.htmlType == "checkbox")
                this.form.$column.javaField = this.form.${column.javaField}.split(",");
              #end
          #end
          #if($table.sub)
            this.${subclassName}List = response.data.${subclassName}List;
          #end
        this.open = true;
        this.title = "修改${functionName}";
      },
      async handleView(row) {
        this.isUpdate = false
        this.reset();
        const ${pkColumn.javaField} = row.${pkColumn.javaField} || this.ids
        const response = await get${BusinessName}(${pkColumn.javaField})
        this.form = response.data;
          #foreach ($column in $columns)
              #if($column.htmlType == "checkbox")
                this.form.$column.javaField = this.form.${column.javaField}.split(",");
              #end
          #end
          #if($table.sub)
            this.${subclassName}List = response.data.${subclassName}List;
          #end
        this.open = true;
        this.title = "查看${functionName}";
      },
      /** 提交按钮 */
      submitForm() {
        this.#[[$]]#refs["form"].validate(valid => {
          if (valid) {
            #foreach($column in $columns)
            #if($column.htmlType == "checkbox")
              this.form.$column.javaField = this.form.${column.javaField}.join(",");
            #end
            #end
            #if($table.sub)
              this.form.${subclassName}List = this.${subclassName}List;
            #end
            if (this.form.${pkColumn.javaField}!=null){
              update${BusinessName}(this.form).then(response => {
                this.#[[$modal]]#.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              });
            }else{
              add${BusinessName}(this.form).then(response => {
                this.#[[$modal]]#.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              });
            }
          }
        });
      },
      /** 删除按钮操作 */
      handleDelete(row) {
        const ${pkColumn.javaField}s = row.${pkColumn.javaField}||this.ids;
        this.#[[$modal]]#.confirm('是否确认删除${functionName}编号为"' + ${pkColumn.javaField}s + '"的数据项？'
      ).then(function () {
          return del${BusinessName}(${pkColumn.javaField}s);
        }).then(() => {
          this.getList();
          this.#[[$modal]]#.msgSuccess("删除成功");
        })
      },
        #if($table.sub)
  /** ${subTable.functionName}序号 */
          row${subClassName}Index({row, rowIndex}){
    row.index = rowIndex + 1;
        },
  /** ${subTable.functionName}添加按钮操作 */
  handleAdd${subClassName}(){
    let obj = {};
    #foreach($column in $subTable.columns)
        #if($column.pk || $column.javaField == ${subTableFkclassName})
    #elseif($column.list && "" != $javaField)
    obj.$column.javaField = "";
    #end
    #end
          this.${subclassName}List.push(obj);
        },
  /** ${subTable.functionName}删除按钮操作 */
  handleDelete${subClassName}(){
          if (this.checked${subClassName}.length == 0){
            this.#[[$modal]]#.msgError("请先选择要删除的${subTable.functionName}数据");
          }else{
            const ${subclassName}List = this.${subclassName}List;
            const checked${subClassName}= this.checked${subClassName}
            this.${subclassName}List = ${subclassName}List.filter(function (item) {
              return checked${subClassName}.indexOf(item.index) == -1
            });
    }
  }
  ,
  /** 复选框选中数据 */
  handle${subClassName}SelectionChange(selection){
          this.checked${subClassName}= selection.map(item => item.index)
  },
  #end
  /** 导出按钮操作 */
  handleExport()
  {
    this.queryParams.pageNo = null
    this.queryParams.pageSize = null
    this.download(`${moduleName}/${businessName}/export`, {...this.queryParams}, `${businessName}_#[[${new Date().getTime()}]]#.xlsx`)
  }
  }
  }

</script>

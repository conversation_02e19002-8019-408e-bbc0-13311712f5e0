package com.ruoyi.hikvision.service;

import com.ruoyi.hikvision.HikvisionProperties;
import com.ruoyi.hikvision.sdk.HCNetSDK;
import com.sun.jna.Pointer;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.ByteBuffer;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * <AUTHOR>
 */
public interface IHandleHikvisionAlamService {

    IHikDeviceGatewayService hikDeviceGatewayService();

    HikvisionProperties properties();

    default void alarmDataHandle(int lCommand,
                                 HCNetSDK.NET_DVR_ALARMER pAlarmer,
                                 Pointer pAlarmInfo,
                                 int dwBufLen,
                                 Pointer pUser) {
        try {
            String deviceIp = new String(pAlarmer.sDeviceIP);
            StringBuilder sAlarmType = new StringBuilder();
            // DefaultTableModel alarmTableModel = ((DefaultTableModel) jTableAlarm.getModel());// 获取表格模型
            String[] newRow = new String[3];
            // 报警时间
            Date today = new Date();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
            String[] sIP = new String[2];

            sAlarmType = new StringBuilder("lCommand=0x" + Integer.toHexString(lCommand));
            // lCommand是传的报警类型
            switch (lCommand) {
                case HCNetSDK.COMM_ALARM_V40:
                    HCNetSDK.NET_DVR_ALARMINFO_V40 struAlarmInfoV40 = new HCNetSDK.NET_DVR_ALARMINFO_V40();
                    struAlarmInfoV40.write();
                    Pointer pInfoV40 = struAlarmInfoV40.getPointer();
                    pInfoV40.write(0, pAlarmInfo.getByteArray(0, struAlarmInfoV40.size()), 0, struAlarmInfoV40.size());
                    struAlarmInfoV40.read();

                    switch (struAlarmInfoV40.struAlarmFixedHeader.dwAlarmType) {
                        case 0:
                            struAlarmInfoV40.struAlarmFixedHeader.ustruAlarm.setType(HCNetSDK.struIOAlarm.class);
                            struAlarmInfoV40.read();
                            sAlarmType.append("：信号量报警")
                                    .append("，")
                                    .append("报警输入口：")
                                    .append(struAlarmInfoV40.struAlarmFixedHeader.ustruAlarm.struioAlarm.dwAlarmInputNo);
                            break;
                        case 1:
                            sAlarmType.append("：硬盘满");
                            break;
                        case 2:
                            sAlarmType.append("：信号丢失");
                            break;
                        case 3:
                            struAlarmInfoV40.struAlarmFixedHeader.ustruAlarm.setType(HCNetSDK.struAlarmChannel.class);
                            struAlarmInfoV40.read();
                            int iChanNum = struAlarmInfoV40.struAlarmFixedHeader.ustruAlarm.strualarmChannel.dwAlarmChanNum;
                            sAlarmType.append("：移动侦测")
                                    .append("，")
                                    .append("报警通道个数：")
                                    .append(iChanNum)
                                    .append("，")
                                    .append("报警通道号：");

                            for (int i = 0; i < iChanNum; i++) {
                                byte[] byChannel = struAlarmInfoV40.pAlarmData.getByteArray(i * 4L, 4);

                                int iChanneNo = 0;
                                for (int j = 0; j < 4; j++) {
                                    int ioffset = j * 8;
                                    int iByte = byChannel[j] & 0xff;
                                    iChanneNo = iChanneNo + (iByte << ioffset);
                                }

                                sAlarmType.append("+ch[").append(iChanneNo).append("]");
                            }

                            break;
                        case 4:
                            sAlarmType.append("：硬盘未格式化");
                            break;
                        case 5:
                            sAlarmType.append("：读写硬盘出错");
                            break;
                        case 6:
                            sAlarmType.append("：遮挡报警");
                            break;
                        case 7:
                            sAlarmType.append("：制式不匹配");
                            break;
                        case 8:
                            sAlarmType.append("：非法访问");
                            break;
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];


                    // alarmTableModel.insertRow(0, newRow);


                    break;
                case HCNetSDK.COMM_ALARM_V30:
                    HCNetSDK.NET_DVR_ALARMINFO_V30 strAlarmInfoV30 = new HCNetSDK.NET_DVR_ALARMINFO_V30();
                    strAlarmInfoV30.write();
                    Pointer pInfoV30 = strAlarmInfoV30.getPointer();
                    pInfoV30.write(0, pAlarmInfo.getByteArray(0, strAlarmInfoV30.size()), 0, strAlarmInfoV30.size());
                    strAlarmInfoV30.read();
                    switch (strAlarmInfoV30.dwAlarmType) {
                        case 0:
                            sAlarmType.append("：信号量报警")
                                    .append("，")
                                    .append("报警输入口：")
                                    .append(strAlarmInfoV30.dwAlarmInputNumber + 1);
                            break;
                        case 1:
                            sAlarmType.append("：硬盘满");
                            break;
                        case 2:
                            sAlarmType.append("：信号丢失");
                            break;
                        case 3:
                            sAlarmType.append("：移动侦测").append("，").append("报警通道：");
                            for (int i = 0; i < 64; i++) {
                                if (strAlarmInfoV30.byChannel[i] == 1) {
                                    sAlarmType.append("ch").append(i + 1).append(" ");
                                }
                            }
                            break;
                        case 4:
                            sAlarmType.append("：硬盘未格式化");
                            break;
                        case 5:
                            sAlarmType.append("：读写硬盘出错");
                            break;
                        case 6:
                            sAlarmType.append("：遮挡报警");
                            break;
                        case 7:
                            sAlarmType.append("：制式不匹配");
                            break;
                        case 8:
                            sAlarmType.append("：非法访问");
                            break;
                    }
                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
                case HCNetSDK.COMM_ALARM_RULE:
                    HCNetSDK.NET_VCA_RULE_ALARM strVcaAlarm = new HCNetSDK.NET_VCA_RULE_ALARM();
                    strVcaAlarm.write();
                    Pointer pVcaInfo = strVcaAlarm.getPointer();
                    pVcaInfo.write(0, pAlarmInfo.getByteArray(0, strVcaAlarm.size()), 0, strVcaAlarm.size());
                    strVcaAlarm.read();

                    switch (strVcaAlarm.struRuleInfo.wEventTypeEx) {
                        case 1:
                            sAlarmType.append("：穿越警戒面")
                                    .append("，")
                                    .append("_wPort:")
                                    .append(strVcaAlarm.struDevInfo.wPort)
                                    .append("_byChannel:")
                                    .append(strVcaAlarm.struDevInfo.byChannel)
                                    .append("_byIvmsChannel:")
                                    .append(strVcaAlarm.struDevInfo.byIvmsChannel)
                                    .append("_Dev IP：")
                                    .append(new String(strVcaAlarm.struDevInfo.struDevIP.sIpV4));
                            break;
                        case 2:
                            sAlarmType.append("：目标进入区域")
                                    .append("，")
                                    .append("_wPort:")
                                    .append(strVcaAlarm.struDevInfo.wPort)
                                    .append("_byChannel:")
                                    .append(strVcaAlarm.struDevInfo.byChannel)
                                    .append("_byIvmsChannel:")
                                    .append(strVcaAlarm.struDevInfo.byIvmsChannel)
                                    .append("_Dev IP：")
                                    .append(new String(strVcaAlarm.struDevInfo.struDevIP.sIpV4));
                            break;
                        case 3:
                            sAlarmType.append("：目标离开区域")
                                    .append("，")
                                    .append("_wPort:")
                                    .append(strVcaAlarm.struDevInfo.wPort)
                                    .append("_byChannel:")
                                    .append(strVcaAlarm.struDevInfo.byChannel)
                                    .append("_byIvmsChannel:")
                                    .append(strVcaAlarm.struDevInfo.byIvmsChannel)
                                    .append("_Dev IP：")
                                    .append(new String(strVcaAlarm.struDevInfo.struDevIP.sIpV4));
                            break;
                        default:
                            sAlarmType.append("：其他行为分析报警，事件类型：")
                                    .append(strVcaAlarm.struRuleInfo.wEventTypeEx)
                                    .append("_wPort:")
                                    .append(strVcaAlarm.struDevInfo.wPort)
                                    .append("_byChannel:")
                                    .append(strVcaAlarm.struDevInfo.byChannel)
                                    .append("_byIvmsChannel:")
                                    .append(strVcaAlarm.struDevInfo.byIvmsChannel)
                                    .append("_Dev IP：")
                                    .append(new String(strVcaAlarm.struDevInfo.struDevIP.sIpV4));
                            break;
                    }
                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    if (strVcaAlarm.dwPicDataLen > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            fout = new FileOutputStream(".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "wEventTypeEx[" + strVcaAlarm.struRuleInfo.wEventTypeEx + "]_" + newName + "_vca.jpg");
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strVcaAlarm.pImage.getByteBuffer(offset, strVcaAlarm.dwPicDataLen);
                            byte[] bytes = new byte[strVcaAlarm.dwPicDataLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                case HCNetSDK.COMM_UPLOAD_PLATE_RESULT:
                    HCNetSDK.NET_DVR_PLATE_RESULT strPlateResult = new HCNetSDK.NET_DVR_PLATE_RESULT();
                    strPlateResult.write();
                    Pointer pPlateInfo = strPlateResult.getPointer();
                    pPlateInfo.write(0, pAlarmInfo.getByteArray(0, strPlateResult.size()), 0, strPlateResult.size());
                    strPlateResult.read();
                    try {
                        String srt3 = new String(strPlateResult.struPlateInfo.sLicense, "GBK");
                        sAlarmType.append("：交通抓拍上传，车牌：").append(srt3);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    if (strPlateResult.dwPicLen > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            fout = new FileOutputStream(".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_plateResult.jpg");
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strPlateResult.pBuffer1.getByteBuffer(offset, strPlateResult.dwPicLen);
                            byte[] bytes = new byte[strPlateResult.dwPicLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                case HCNetSDK.COMM_ITS_PLATE_RESULT:
                    HCNetSDK.NET_ITS_PLATE_RESULT strItsPlateResult = new HCNetSDK.NET_ITS_PLATE_RESULT();
                    strItsPlateResult.write();
                    Pointer pItsPlateInfo = strItsPlateResult.getPointer();
                    pItsPlateInfo.write(0,
                            pAlarmInfo.getByteArray(0, strItsPlateResult.size()),
                            0,
                            strItsPlateResult.size());
                    strItsPlateResult.read();
                    try {
                        String srt3 = new String(strItsPlateResult.struPlateInfo.sLicense, "GBK");
                        sAlarmType.append(",车辆类型：")
                                .append(strItsPlateResult.byVehicleType)
                                .append(",交通抓拍上传，车牌：")
                                .append(srt3);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    for (int i = 0; i < strItsPlateResult.dwPicNum; i++) {
                        if (strItsPlateResult.struPicInfo[i].dwDataLen > 0) {
                            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                            String newName = sf.format(new Date());
                            FileOutputStream fout;
                            try {
                                String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_type[" + strItsPlateResult.struPicInfo[i].byType + "]_ItsPlate.jpg";
                                fout = new FileOutputStream(filename);
                                // 将字节写入文件
                                long offset = 0;
                                ByteBuffer buffers = strItsPlateResult.struPicInfo[i].pBuffer.getByteBuffer(offset,
                                        strItsPlateResult.struPicInfo[i].dwDataLen);
                                byte[] bytes = new byte[strItsPlateResult.struPicInfo[i].dwDataLen];
                                buffers.rewind();
                                buffers.get(bytes);
                                fout.write(bytes);
                                fout.close();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    break;
                case HCNetSDK.COMM_ALARM_PDC:
                    HCNetSDK.NET_DVR_PDC_ALRAM_INFO strPDCResult = new HCNetSDK.NET_DVR_PDC_ALRAM_INFO();
                    strPDCResult.write();
                    Pointer pPDCInfo = strPDCResult.getPointer();
                    pPDCInfo.write(0, pAlarmInfo.getByteArray(0, strPDCResult.size()), 0, strPDCResult.size());
                    strPDCResult.read();

                    if (strPDCResult.byMode == 0) {
                        strPDCResult.uStatModeParam.setType(HCNetSDK.NET_DVR_STATFRAME.class);
                        sAlarmType.append("：客流量统计，进入人数：")
                                .append(strPDCResult.dwEnterNum)
                                .append("，离开人数：")
                                .append(strPDCResult.dwLeaveNum)
                                .append(", byMode:")
                                .append(strPDCResult.byMode)
                                .append(", dwRelativeTime:")
                                .append(strPDCResult.uStatModeParam.struStatFrame.dwRelativeTime)
                                .append(", dwAbsTime:")
                                .append(strPDCResult.uStatModeParam.struStatFrame.dwAbsTime);
                    }
                    if (strPDCResult.byMode == 1) {
                        strPDCResult.uStatModeParam.setType(HCNetSDK.NET_DVR_STATTIME.class);
                        String strtmStart = "" + String.format("%04d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwYear) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwMonth) + String.format("%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwDay) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwHour) + String.format("%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwMinute) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmStart.dwSecond);
                        String strtmEnd = "" + String.format("%04d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwYear) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwMonth) + String.format("%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwDay) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwHour) + String.format("%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwMinute) + String.format(
                                "%02d",
                                strPDCResult.uStatModeParam.struStatTime.tmEnd.dwSecond);
                        sAlarmType.append("：客流量统计，进入人数：")
                                .append(strPDCResult.dwEnterNum)
                                .append("，离开人数：")
                                .append(strPDCResult.dwLeaveNum)
                                .append(", byMode:")
                                .append(strPDCResult.byMode)
                                .append(", tmStart:")
                                .append(strtmStart)
                                .append(",tmEnd :")
                                .append(strtmEnd);
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(strPDCResult.struDevInfo.struDevIP.sIpV4).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;

                case HCNetSDK.COMM_ITS_PARK_VEHICLE:
                    HCNetSDK.NET_ITS_PARK_VEHICLE strItsParkVehicle = new HCNetSDK.NET_ITS_PARK_VEHICLE();
                    strItsParkVehicle.write();
                    Pointer pItsParkVehicle = strItsParkVehicle.getPointer();
                    pItsParkVehicle.write(0,
                            pAlarmInfo.getByteArray(0, strItsParkVehicle.size()),
                            0,
                            strItsParkVehicle.size());
                    strItsParkVehicle.read();
                    try {
                        String srtParkingNo = new String(strItsParkVehicle.byParkingNo).trim(); // 车位编号
                        String srtPlate = new String(strItsParkVehicle.struPlateInfo.sLicense, "GBK").trim(); // 车牌号码
                        sAlarmType.append(",停产场数据,车位编号：")
                                .append(srtParkingNo)
                                .append(",车位状态：")
                                .append(strItsParkVehicle.byLocationStatus)
                                .append(",车牌：")
                                .append(srtPlate);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    for (int i = 0; i < strItsParkVehicle.dwPicNum; i++) {
                        if (strItsParkVehicle.struPicInfo[i].dwDataLen > 0) {
                            SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                            String newName = sf.format(new Date());
                            FileOutputStream fout;
                            try {
                                String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_type[" + strItsParkVehicle.struPicInfo[i].byType + "]_ParkVehicle.jpg";
                                fout = new FileOutputStream(filename);
                                // 将字节写入文件
                                long offset = 0;
                                ByteBuffer buffers = strItsParkVehicle.struPicInfo[i].pBuffer.getByteBuffer(offset,
                                        strItsParkVehicle.struPicInfo[i].dwDataLen);
                                byte[] bytes = new byte[strItsParkVehicle.struPicInfo[i].dwDataLen];
                                buffers.rewind();
                                buffers.get(bytes);
                                fout.write(bytes);
                                fout.close();
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                    break;
                case HCNetSDK.COMM_ALARM_TFS:
                    HCNetSDK.NET_DVR_TFS_ALARM strTFSAlarmInfo = new HCNetSDK.NET_DVR_TFS_ALARM();
                    strTFSAlarmInfo.write();
                    Pointer pTFSInfo = strTFSAlarmInfo.getPointer();
                    pTFSInfo.write(0, pAlarmInfo.getByteArray(0, strTFSAlarmInfo.size()), 0, strTFSAlarmInfo.size());
                    strTFSAlarmInfo.read();

                    try {
                        String srtPlate = new String(strTFSAlarmInfo.struPlateInfo.sLicense, "GBK").trim(); // 车牌号码
                        sAlarmType.append("：交通取证报警信息，违章类型：")
                                .append(strTFSAlarmInfo.dwIllegalType)
                                .append("，车牌号码：")
                                .append(srtPlate)
                                .append("，车辆出入状态：")
                                .append(strTFSAlarmInfo.struAIDInfo.byVehicleEnterState);
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(strTFSAlarmInfo.struDevInfo.struDevIP.sIpV4).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
                case HCNetSDK.COMM_ALARM_AID_V41:
                    HCNetSDK.NET_DVR_AID_ALARM_V41 struAIDAlarmInfo = new HCNetSDK.NET_DVR_AID_ALARM_V41();
                    struAIDAlarmInfo.write();
                    Pointer pAIDInfo = struAIDAlarmInfo.getPointer();
                    pAIDInfo.write(0, pAlarmInfo.getByteArray(0, struAIDAlarmInfo.size()), 0, struAIDAlarmInfo.size());
                    struAIDAlarmInfo.read();
                    sAlarmType.append("：交通事件报警信息，交通事件类型：")
                            .append(struAIDAlarmInfo.struAIDInfo.dwAIDType)
                            .append("，规则ID：")
                            .append(struAIDAlarmInfo.struAIDInfo.byRuleID)
                            .append("，车辆出入状态：")
                            .append(struAIDAlarmInfo.struAIDInfo.byVehicleEnterState);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(struAIDAlarmInfo.struDevInfo.struDevIP.sIpV4).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
                case HCNetSDK.COMM_ALARM_TPS_V41:
                    HCNetSDK.NET_DVR_TPS_ALARM_V41 struTPSAlarmInfo = new HCNetSDK.NET_DVR_TPS_ALARM_V41();
                    struTPSAlarmInfo.write();
                    Pointer pTPSInfo = struTPSAlarmInfo.getPointer();
                    pTPSInfo.write(0, pAlarmInfo.getByteArray(0, struTPSAlarmInfo.size()), 0, struTPSAlarmInfo.size());
                    struTPSAlarmInfo.read();

                    sAlarmType.append("：交通统计报警信息，绝对时标：")
                            .append(struTPSAlarmInfo.dwAbsTime)
                            .append("，能见度:")
                            .append(struTPSAlarmInfo.struDevInfo.byIvmsChannel)
                            .append("，车道1交通状态:")
                            .append(struTPSAlarmInfo.struTPSInfo.struLaneParam[0].byTrafficState)
                            .append("，监测点编号：")
                            .append(new String(struTPSAlarmInfo.byMonitoringSiteID).trim())
                            .append("，设备编号：")
                            .append(new String(struTPSAlarmInfo.byDeviceID).trim())
                            .append("，开始统计时间：")
                            .append(struTPSAlarmInfo.dwStartTime)
                            .append("，结束统计时间：")
                            .append(struTPSAlarmInfo.dwStopTime);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(struTPSAlarmInfo.struDevInfo.struDevIP.sIpV4).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
                case HCNetSDK.COMM_UPLOAD_FACESNAP_RESULT:
                    // 实时人脸抓拍上传
                    HCNetSDK.NET_VCA_FACESNAP_RESULT strFaceSnapInfo = new HCNetSDK.NET_VCA_FACESNAP_RESULT();
                    strFaceSnapInfo.write();
                    Pointer pFaceSnapInfo = strFaceSnapInfo.getPointer();
                    pFaceSnapInfo.write(0,
                            pAlarmInfo.getByteArray(0, strFaceSnapInfo.size()),
                            0,
                            strFaceSnapInfo.size());
                    strFaceSnapInfo.read();
                    sAlarmType.append("：人脸抓拍上传，人脸评分：")
                            .append(strFaceSnapInfo.dwFaceScore)
                            .append("，年龄段：")
                            .append(strFaceSnapInfo.struFeature.byAgeGroup)
                            .append("，性别：")
                            .append(strFaceSnapInfo.struFeature.bySex);
                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(strFaceSnapInfo.struDevInfo.struDevIP.sIpV4).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss"); // 设置日期格式
                    String time = df.format(new Date()); //  new Date()为获取当前系统时间
                    // 人脸图片写文件
                    try {
                        FileOutputStream small = new FileOutputStream(System.getProperty("user.dir") + "\\pic\\" + time + "small.jpg");
                        FileOutputStream big = new FileOutputStream(System.getProperty("user.dir") + "\\pic\\" + time + "big.jpg");

                        if (strFaceSnapInfo.dwFacePicLen > 0) {
                            try {
                                small.write(strFaceSnapInfo.pBuffer1.getByteArray(0, strFaceSnapInfo.dwFacePicLen),
                                        0,
                                        strFaceSnapInfo.dwFacePicLen);
                                small.close();
                            } catch (IOException ex) {
                                Logger.getLogger("AlarmJava").log(Level.SEVERE, null, ex);
                            }

                        }
                        if (strFaceSnapInfo.dwFacePicLen > 0) {
                            try {
                                big.write(strFaceSnapInfo.pBuffer2.getByteArray(0, strFaceSnapInfo.dwBackgroundPicLen),
                                        0,
                                        strFaceSnapInfo.dwBackgroundPicLen);
                                big.close();
                            } catch (IOException ex) {
                                Logger.getLogger("AlarmJava").log(Level.SEVERE, null, ex);
                            }
                        }
                    } catch (FileNotFoundException ex) {
                        Logger.getLogger("AlarmJava").log(Level.SEVERE, null, ex);
                    }
                    break;
                case HCNetSDK.COMM_SNAP_MATCH_ALARM:
                    // 人脸名单比对报警
                    HCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM strFaceSnapMatch = new HCNetSDK.NET_VCA_FACESNAP_MATCH_ALARM();
                    strFaceSnapMatch.write();
                    Pointer pFaceSnapMatch = strFaceSnapMatch.getPointer();
                    pFaceSnapMatch.write(0,
                            pAlarmInfo.getByteArray(0, strFaceSnapMatch.size()),
                            0,
                            strFaceSnapMatch.size());
                    strFaceSnapMatch.read();

                    if ((strFaceSnapMatch.dwSnapPicLen > 0) && (strFaceSnapMatch.byPicTransType == 0)) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = System.getProperty("user.dir") + "\\pic\\" + newName + "_pSnapPicBuffer" + ".jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strFaceSnapMatch.pSnapPicBuffer.getByteBuffer(offset,
                                    strFaceSnapMatch.dwSnapPicLen);
                            byte[] bytes = new byte[strFaceSnapMatch.dwSnapPicLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if ((strFaceSnapMatch.struSnapInfo.dwSnapFacePicLen > 0) && (strFaceSnapMatch.byPicTransType == 0)) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = System.getProperty("user.dir") + "\\pic\\" + newName + "_struSnapInfo_pBuffer1" + ".jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strFaceSnapMatch.struSnapInfo.pBuffer1.getByteBuffer(offset,
                                    strFaceSnapMatch.struSnapInfo.dwSnapFacePicLen);
                            byte[] bytes = new byte[strFaceSnapMatch.struSnapInfo.dwSnapFacePicLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if ((strFaceSnapMatch.struBlockListInfo.dwBlockListPicLen > 0) && (strFaceSnapMatch.byPicTransType == 0)) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = System.getProperty("user.dir") + "\\pic\\" + newName + "_fSimilarity_" + strFaceSnapMatch.fSimilarity + "_struBlockListInfo_pBuffer1" + ".jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strFaceSnapMatch.struBlockListInfo.pBuffer1.getByteBuffer(offset,
                                    strFaceSnapMatch.struBlockListInfo.dwBlockListPicLen);
                            byte[] bytes = new byte[strFaceSnapMatch.struBlockListInfo.dwBlockListPicLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    sAlarmType.append("：人脸名单比对报警，相识度：")
                            .append(strFaceSnapMatch.fSimilarity)
                            .append("，名单姓名：")
                            .append(new String(strFaceSnapMatch.struBlockListInfo.struBlockListInfo.struAttribute.byName,
                                    "GBK").trim())
                            .append("，\n名单证件信息：")
                            .append(new String(strFaceSnapMatch.struBlockListInfo.struBlockListInfo.struAttribute.byCertificateNumber).trim());

                    // 获取人脸库ID
                    byte[] FDIDbytes;
                    if ((strFaceSnapMatch.struBlockListInfo.dwFDIDLen > 0) && (strFaceSnapMatch.struBlockListInfo.pFDID != null)) {
                        ByteBuffer FDIDbuffers = strFaceSnapMatch.struBlockListInfo.pFDID.getByteBuffer(0,
                                strFaceSnapMatch.struBlockListInfo.dwFDIDLen);
                        FDIDbytes = new byte[strFaceSnapMatch.struBlockListInfo.dwFDIDLen];
                        FDIDbuffers.rewind();
                        FDIDbuffers.get(FDIDbytes);
                        sAlarmType.append("，人脸库ID:").append(new String(FDIDbytes).trim());
                    }
                    // 获取人脸图片ID
                    byte[] PIDbytes;
                    if ((strFaceSnapMatch.struBlockListInfo.dwPIDLen > 0) && (strFaceSnapMatch.struBlockListInfo.pPID != null)) {
                        ByteBuffer PIDbuffers = strFaceSnapMatch.struBlockListInfo.pPID.getByteBuffer(0,
                                strFaceSnapMatch.struBlockListInfo.dwPIDLen);
                        PIDbytes = new byte[strFaceSnapMatch.struBlockListInfo.dwPIDLen];
                        PIDbuffers.rewind();
                        PIDbuffers.get(PIDbytes);
                        sAlarmType.append("，人脸图片ID:").append(new String(PIDbytes).trim());
                    }
                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
                // 门禁主机报警信息
                case HCNetSDK.COMM_ALARM_ACS:
                    HCNetSDK.NET_DVR_ACS_ALARM_INFO strACSInfo = new HCNetSDK.NET_DVR_ACS_ALARM_INFO();
                    strACSInfo.write();
                    Pointer pACSInfo = strACSInfo.getPointer();
                    pACSInfo.write(0, pAlarmInfo.getByteArray(0, strACSInfo.size()), 0, strACSInfo.size());
                    strACSInfo.read();


                    Integer EmployeeNo = strACSInfo.struAcsEventInfo.dwEmployeeNo;

                    if (EmployeeNo > 0) {

                        // todo 两人通行逻辑

                        // 开门
                        hikDeviceGatewayService().openGateway(deviceIp);

                        // todo 添加通行记录

                        sAlarmType.append("：门禁主机报警信息，工号：")
                                .append(strACSInfo.struAcsEventInfo.dwEmployeeNo)
                                .append("，卡类型：")
                                .append(strACSInfo.struAcsEventInfo.byCardType)
                                .append("，报警主类型：")
                                .append(strACSInfo.dwMajor)
                                .append("，报警次类型：")
                                .append(strACSInfo.dwMinor);
//                        System.out.println(sAlarmType);

                        // 开门持续时间
                        TimeUnit.SECONDS.sleep(properties().getDoorOpeningInterval());

                        hikDeviceGatewayService().closeGateway(deviceIp);
                    }


                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];

                    // alarmTableModel.insertRow(0, newRow);

//                     if(strACSInfo.dwPicDataLen>0)
//                     {
//                         SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
//                         String newName = sf.format(new Date());
//                         FileOutputStream fout;
//                         try {
//                             String filename = ".\\pic\\"+ new String(pAlarmer.sDeviceIP).trim() +
//                                     "_byCardNo["+ new String(strACSInfo.struAcsEventInfo.byCardNo).trim() +
//                                     "_"+ newName + "_Acs.jpg";
//                             fout = new FileOutputStream(filename);
//                             // 将字节写入文件
//                             long offset = 0;
//                             ByteBuffer buffers = strACSInfo.pPicData.getByteBuffer(offset, strACSInfo.dwPicDataLen);
//                             byte [] bytes = new byte[strACSInfo.dwPicDataLen];
//                             buffers.rewind();
//                             buffers.get(bytes);
//                             fout.write(bytes);
//                             fout.close();
//                         } catch (FileNotFoundException e) {
//                             e.printStackTrace();
//                         } catch (IOException e) {
//                             e.printStackTrace();
//                         }
//                     }
                    break;
                // 身份证信息
                case HCNetSDK.COMM_ID_INFO_ALARM:
                    HCNetSDK.NET_DVR_ID_CARD_INFO_ALARM strIDCardInfo = new HCNetSDK.NET_DVR_ID_CARD_INFO_ALARM();
                    strIDCardInfo.write();
                    Pointer pIDCardInfo = strIDCardInfo.getPointer();
                    pIDCardInfo.write(0, pAlarmInfo.getByteArray(0, strIDCardInfo.size()), 0, strIDCardInfo.size());
                    strIDCardInfo.read();

                    sAlarmType.append("：门禁身份证刷卡信息，身份证号码：")
                            .append(new String(strIDCardInfo.struIDCardCfg.byIDNum).trim())
                            .append("，姓名：")
                            .append(new String(strIDCardInfo.struIDCardCfg.byName).trim())
                            .append("，报警主类型：")
                            .append(strIDCardInfo.dwMajor)
                            .append("，报警次类型：")
                            .append(strIDCardInfo.dwMinor);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    // 身份证图片
                    if (strIDCardInfo.dwPicDataLen > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_byCardNo[" + new String(
                                    strIDCardInfo.struIDCardCfg.byIDNum).trim() + "_" + newName + "_IDInfoPic.jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strIDCardInfo.pPicData.getByteBuffer(offset,
                                    strIDCardInfo.dwPicDataLen);
                            byte[] bytes = new byte[strIDCardInfo.dwPicDataLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }

                    // 抓拍图片
                    if (strIDCardInfo.dwCapturePicDataLen > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_byCardNo[" + new String(
                                    strIDCardInfo.struIDCardCfg.byIDNum).trim() + "_" + newName + "_IDInfoCapturePic.jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = strIDCardInfo.pCapturePicData.getByteBuffer(offset,
                                    strIDCardInfo.dwCapturePicDataLen);
                            byte[] bytes = new byte[strIDCardInfo.dwCapturePicDataLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                // 设备支持AI开放平台接入，上传视频检测数据
                case HCNetSDK.COMM_UPLOAD_AIOP_VIDEO:
                    HCNetSDK.NET_AIOP_VIDEO_HEAD struAIOPVideo = new HCNetSDK.NET_AIOP_VIDEO_HEAD();
                    struAIOPVideo.write();
                    Pointer pAIOPVideo = struAIOPVideo.getPointer();
                    pAIOPVideo.write(0, pAlarmInfo.getByteArray(0, struAIOPVideo.size()), 0, struAIOPVideo.size());
                    struAIOPVideo.read();

                    String strTime = "" + String.format("%04d", struAIOPVideo.struTime.wYear) + String.format("%02d",
                            struAIOPVideo.struTime.wMonth) + String.format(
                            "%02d",
                            struAIOPVideo.struTime.wDay) + String.format("%02d",
                            struAIOPVideo.struTime.wHour) + String.format(
                            "%02d",
                            struAIOPVideo.struTime.wMinute) + String.format("%02d",
                            struAIOPVideo.struTime.wSecond) + String.format(
                            "%03d",
                            struAIOPVideo.struTime.wMilliSec);

                    sAlarmType.append("：AI开放平台接入，上传视频检测数据，通道号:")
                            .append(struAIOPVideo.dwChannel)
                            .append(", 时间:")
                            .append(strTime);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    if (struAIOPVideo.dwAIOPDataSize > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_AIO_VideoData.json";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = struAIOPVideo.pBufferAIOPData.getByteBuffer(offset,
                                    struAIOPVideo.dwAIOPDataSize);
                            byte[] bytes = new byte[struAIOPVideo.dwAIOPDataSize];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    if (struAIOPVideo.dwPictureSize > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_AIO_VideoPic.jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = struAIOPVideo.pBufferPicture.getByteBuffer(offset,
                                    struAIOPVideo.dwPictureSize);
                            byte[] bytes = new byte[struAIOPVideo.dwPictureSize];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                // 设备支持AI开放平台接入，上传视频检测数据
                case HCNetSDK.COMM_UPLOAD_AIOP_PICTURE:
                    HCNetSDK.NET_AIOP_PICTURE_HEAD struAIOPPic = new HCNetSDK.NET_AIOP_PICTURE_HEAD();
                    struAIOPPic.write();
                    Pointer pAIOPPic = struAIOPPic.getPointer();
                    pAIOPPic.write(0, pAlarmInfo.getByteArray(0, struAIOPPic.size()), 0, struAIOPPic.size());
                    struAIOPPic.read();

                    String strPicTime = "" + String.format("%04d", struAIOPPic.struTime.wYear) + String.format("%02d",
                            struAIOPPic.struTime.wMonth) + String.format(
                            "%02d",
                            struAIOPPic.struTime.wDay) + String.format("%02d",
                            struAIOPPic.struTime.wHour) + String.format(
                            "%02d",
                            struAIOPPic.struTime.wMinute) + String.format("%02d",
                            struAIOPPic.struTime.wSecond) + String.format(
                            "%03d",
                            struAIOPPic.struTime.wMilliSec);

                    sAlarmType.append("：AI开放平台接入，上传图片检测数据，通道号:")
                            .append(new String(struAIOPPic.szPID))
                            .append(", 时间:")
                            .append(strPicTime);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    if (struAIOPPic.dwAIOPDataSize > 0) {
                        SimpleDateFormat sf = new SimpleDateFormat("yyyyMMddHHmmss");
                        String newName = sf.format(new Date());
                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + "_" + newName + "_AIO_PicData.json";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = struAIOPPic.pBufferAIOPData.getByteBuffer(offset,
                                    struAIOPPic.dwAIOPDataSize);
                            byte[] bytes = new byte[struAIOPPic.dwAIOPDataSize];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                // ISAPI协议报警信息
                case HCNetSDK.COMM_ISAPI_ALARM:
                    HCNetSDK.NET_DVR_ALARM_ISAPI_INFO struEventISAPI = new HCNetSDK.NET_DVR_ALARM_ISAPI_INFO();
                    struEventISAPI.write();
                    Pointer pEventISAPI = struEventISAPI.getPointer();
                    pEventISAPI.write(0, pAlarmInfo.getByteArray(0, struEventISAPI.size()), 0, struEventISAPI.size());
                    struEventISAPI.read();

                    sAlarmType.append("：ISAPI协议报警信息, 数据格式:")
                            .append(struEventISAPI.byDataType)
                            .append(", 图片个数:")
                            .append(struEventISAPI.byPicturesNumber);

                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);

                    SimpleDateFormat sf1 = new SimpleDateFormat("yyyyMMddHHmmss");
                    String curTime = sf1.format(new Date());
                    FileOutputStream foutdata;
                    try {
                        String jsonfilename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + curTime + "_ISAPI_Alarm_" + ".json";
                        foutdata = new FileOutputStream(jsonfilename);
                        // 将字节写入文件
                        ByteBuffer jsonbuffers = struEventISAPI.pAlarmData.getByteBuffer(0,
                                struEventISAPI.dwAlarmDataLen);
                        byte[] jsonbytes = new byte[struEventISAPI.dwAlarmDataLen];
                        jsonbuffers.rewind();
                        jsonbuffers.get(jsonbytes);
                        foutdata.write(jsonbytes);
                        foutdata.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }

                    for (int i = 0; i < struEventISAPI.byPicturesNumber; i++) {
                        HCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA struPicData = new HCNetSDK.NET_DVR_ALARM_ISAPI_PICDATA();
                        struPicData.write();
                        Pointer pPicData = struPicData.getPointer();
                        pPicData.write(0,
                                struEventISAPI.pPicPackData.getByteArray(i * struPicData.size(),
                                        struPicData.size()),
                                0,
                                struPicData.size());
                        struPicData.read();

                        FileOutputStream fout;
                        try {
                            String filename = ".\\pic\\" + new String(pAlarmer.sDeviceIP).trim() + curTime + "_ISAPIPic_" + i + "_" + new String(
                                    struPicData.szFilename).trim() + ".jpg";
                            fout = new FileOutputStream(filename);
                            // 将字节写入文件
                            long offset = 0;
                            ByteBuffer buffers = struPicData.pPicData.getByteBuffer(offset, struPicData.dwPicLen);
                            byte[] bytes = new byte[struPicData.dwPicLen];
                            buffers.rewind();
                            buffers.get(bytes);
                            fout.write(bytes);
                            fout.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                    break;
                default:
                    newRow[0] = dateFormat.format(today);
                    // 报警类型
                    newRow[1] = sAlarmType.toString();
                    // 报警设备IP地址
                    sIP = new String(pAlarmer.sDeviceIP).split("\0", 2);
                    newRow[2] = sIP[0];
                    // alarmTableModel.insertRow(0, newRow);
                    break;
            }
        } catch (UnsupportedEncodingException ex) {
            Logger.getLogger("AlarmJava").log(Level.SEVERE, null, ex);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }
}

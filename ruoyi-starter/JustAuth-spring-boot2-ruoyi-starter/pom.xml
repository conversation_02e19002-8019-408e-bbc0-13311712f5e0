<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
        <artifactId>ruoyi-starter</artifactId>
        <version>dfe-zh-ruoyi-lowcode-boot2-1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>JustAuth-spring-boot2-ruoyi-starter</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
            <artifactId>ruoyi-framework</artifactId>
        </dependency>
        <dependency>
            <groupId>com.xkcoding.justauth</groupId>
            <artifactId>justauth-spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hutool-core</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>

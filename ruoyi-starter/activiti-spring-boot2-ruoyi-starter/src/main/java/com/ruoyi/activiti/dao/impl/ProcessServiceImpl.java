package com.ruoyi.activiti.dao.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.pagehelper.PageInfo;
import com.ruoyi.activiti.dao.IProcessService;
import com.ruoyi.activiti.domain.*;
import com.ruoyi.activiti.mapper.ActTaskMapper;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.page.PageUtil;
import com.ruoyi.common.util.SecurityUtil;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.bean.BeanUtil;
import com.ruoyi.dingtalk.domain.DingCreateDingTalkTaskParam;
import com.ruoyi.dingtalk.service.IDingDingMqService;
import com.ruoyi.system.dao.ISysConfigDao;
import com.ruoyi.system.dao.ISysMassageDao;
import com.ruoyi.system.dao.ISysUserDao;
import com.ruoyi.system.domain.SysMassage;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricActivityInstanceQuery;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.persistence.entity.TaskEntityImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.DelegationState;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.convert.ConversionService;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProcessServiceImpl implements IProcessService {
    private final IdentityService identityService;
    private final TaskService taskService;
    private final HistoryService historyService;
    private final RuntimeService runtimeService;
    private final ActTaskMapper actTaskMapper;
    private final ISysUserDao userService;
    private final ISysMassageDao sysMassageService;
    private final ISysConfigDao configService;
    private final IDingDingMqService dingDingMqService;
    private final ConversionService conversionService;

    /**
     * 自动提交 添加事务注解 2023/8/14
     *
     * @param taskId
     * @param instanceId
     * @param variablesStr
     */
    @DSTransactional(rollbackFor = Exception.class)
    public void completeNext(String taskId, String instanceId, String variablesStr) {
        String crrentLoginName = SecurityUtil.getUsername();
        JSONObject variables = JSON.parseObject(variablesStr);
        variables.put("comment", "系统自动审核通过有相同审核人的审核节点");
        String comment = variables.get("comment").toString();
        variables.put("pass", true);
        taskService.createTaskQuery().taskId(taskId).singleResult();
        taskService.claim(taskId, crrentLoginName);
        identityService.setAuthenticatedUserId(crrentLoginName);
        taskService.addComment(taskId, instanceId, comment);
        taskService.complete(taskId, variables);
    }

    /**
     * 自动提交 添加事务注解 2023/8/14
     * 此方法应用了递归，请注意单个线程递归栈深度不足导致的OOM，可以调整Xss或好好设计业务代码（提前返回减少codeCache深度）
     *
     * @param instanceId
     * @param variables
     */

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public void autoComplete(String instanceId, String variables) {
        String crrentLoginName = SecurityUtil.getUsername();
        List<String> nextIdentitys = selectNextIdentity(instanceId);
        if (nextIdentitys == null || nextIdentitys.isEmpty() || !nextIdentitys.contains(crrentLoginName)) {
            return;
        }
        List<Task> taskList = nextAllNodeTaskList(instanceId);
        if (taskList == null || taskList.isEmpty()) {
            return;
        }
        Task task = taskList.get(0);
        if (task == null) {
            return;
        }
        String nextTaskAssignee = task.getAssignee();
        if (StringUtil.isEmpty(nextTaskAssignee)) {
            //下级审批人为空，说明流程完了
            return;
        }
        if (!nextTaskAssignee.equals(crrentLoginName)) {
            //下级审批人 不是当前用户 结束自动审批
            return;
        }
        //如果下一级别的审核人里面包含该级别审核人，则自动审核通过
        completeNext(task.getId(), instanceId, variables);
        autoComplete(instanceId, variables);
    }

    @Override
    public List<String> selectNextIdentity(String instanceId) {
        List<String> nextAssigneeList = actTaskMapper.selectNextAssignee(instanceId).stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<String> userList = nextAssigneeList.isEmpty() ?
                actTaskMapper.selectNextIdentity(instanceId)
                        .stream()
                        .filter(nextMap -> nextMap != null && !CollectionUtils.isEmpty(nextMap) && nextMap.containsKey(
                                "USER_ID_"))
                        .map(nextMap -> nextMap.get("USER_ID_").toString())
                        .collect(Collectors.toList()) :
                nextAssigneeList.stream()
                        .flatMap(nextMap -> Arrays.stream(nextMap.split(",")))
                        .collect(Collectors.toList());
        return userList.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Task> nextAllNodeTaskList(String instanceId) {
        return taskService.createTaskQuery().processInstanceId(instanceId).list();
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public boolean complete(CompleteData completeData) {
        try {
            complete(completeData.getTaskId(), completeData.getInstanceId(), completeData.getVariables());
            autoComplete(completeData.getInstanceId(), completeData.getVariables());
        } catch (Exception e) {
            throw new ServiceException("办理失败！");
        }
        return true;
    }

    /**
     * 提交申请
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public <T extends ProcessEntity<T>> void submitApply(T entity, String key) throws Exception {
        submitApply(entity, key, null);
    }

    /**
     * 自动提交 添加事务注解 2023/8/14
     *
     * @param entity
     * @param key
     * @param <T>
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public <T extends ProcessEntity<T>> void autoSubmitApply(T entity, String key) {
        autoSubmitApply(entity, key, null);
    }

    /**
     * 自动提交 添加事务注解 2023/8/14
     *
     * @param entity
     * @param key
     * @param variables
     * @param <T>
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public <T extends ProcessEntity<T>> void autoSubmitApply(T entity, String key, JSONObject variables) {
        variables = Optional.ofNullable(variables).orElse(JSONObject.of());
        variables.put("pass", true);
        autoComplete(entity.getInstanceId(), JSON.toJSONString(variables));
    }


    /**
     * 利用泛型直接取值，不用反射（太耗资源）
     *
     * @param entity
     * @param key
     * @param variables
     * @param <T>
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    public <T extends ProcessEntity<T>> void submitApply(T entity, String key, JSONObject variables) {
        Long id = entity.getId();
        String currentLoginUserName = SecurityUtil.getUsername();
        String currentLoginUserNickName = SecurityUtil.getLoginUser().getUser().getNickName();
        Date now = new Date();
        // 更新流程通用字段
        entity.setApplyUserId(currentLoginUserName);
        entity.setApplyUserName(currentLoginUserNickName);
        entity.setApplyTime(now);
        entity.setProcessKey(key);
        entity.setStatus(BizStatus.CHECKING.getCode());
        entity.setUpdateBy(currentLoginUserName);
        entity.setUpdateById(SecurityUtil.getUserId().toString());
        entity.setUpdateTime(now);
        // 用来设置启动流程的人员ID，引擎会自动把用户ID保存到activiti:initiator中
        identityService.setAuthenticatedUserId(currentLoginUserName);
        // 启动流程时设置业务 key
        ProcessInstance instance = runtimeService.startProcessInstanceByKey(key, String.valueOf(id), variables);
        String instanceId = instance.getId();
        // 更新业务表流程实例id字段
            entity.setInstanceId(instanceId);
        // 记录流程实例业务关系
        InstanceBusiness instanceBusiness = new InstanceBusiness();
        instanceBusiness.setInstanceId(entity.getInstanceId());
        instanceBusiness.setBusinessKey(String.valueOf(id));
        // 如果存在 @TableName注解，并且注解有value，则取value值
        // 否则取类名 驼峰命名方式 -> _ 隔开的（数据库）命名方式 的字符串
        TableName annotation = entity.getClass().getAnnotation(TableName.class);
        String tableName = ObjectUtils.isNotEmpty(annotation) && ObjectUtils.isNotEmpty(annotation.value()) ?
                annotation.value() :
                StringUtil.toUnderScoreCase(entity.getClass().getSimpleName());
        instanceBusiness.setModule(tableName);
        instanceBusiness.setDeptId(SecurityUtil.getLoginUser().getUser().getDeptId());
        instanceBusiness.setUserId(SecurityUtil.getLoginUser().getUser().getUserId());
        instanceBusiness.setNotify(entity.getNotify());
        instanceBusiness.setFinish(0);
        actTaskMapper.insertInstanceBusiness(instanceBusiness);
        if (StringUtil.isNotEmpty(entity.getNotify())) {
            String[] userids = entity.getNotify().split(",");
            for (String userId : userids) {
                actTaskMapper.insertBusinessNotify(instanceBusiness.getInstanceId(), Long.parseLong(userId));
            }
        }
        sendMsgOfSubmitApply(instanceId, key);
        pushDingTalkByMQOfSubmitApply(instance, entity);
    }

    @Override
    public void sendMsgOfSubmitApply(String instanceId, String key) {
        List<String> nextIdentity = selectNextIdentity(instanceId);
        if ("true".equals(configService.selectConfigByKey("sys.ws.enable"))) {
            nextIdentity.forEach(userName -> sysMassageService.sendMsg(SysMassage.builder()
                    .type("act")
                    .status(UserConstants.NO)
                    .actTaskId(instanceId)
                    .actTaskKey(key)
                    .recipientUserName(userName)
                    .build()));
        }
    }

    @Override
    public <T extends ProcessEntity<T>> void pushDingTalkByMQOfSubmitApply(ProcessInstance instance, T entity) {
        List<String> nextIdentity = selectNextIdentity(instance.getId());
        if ("true".equals(configService.selectConfigByKey("sys.account.usingDingTalk"))) {
            String dingTalkAppAddr = configService.selectConfigByKey("sys.account.dingTalkAppAddr");
            String dingTalkPcAddr = configService.selectConfigByKey("sys.account.dingTalkPcAddr");
            List<Task> taskList = taskService.createTaskQuery()
                    .processInstanceId(instance.getId())
                    .orderByProcessInstanceId()
                    .desc()
                    .list();
            String processDefinitionKey = instance.getProcessDefinitionKey();
            String processDefinitionName = instance.getProcessDefinitionName();
            DingCreateDingTalkTaskParam dingCreateDingTalkTaskParam = DingCreateDingTalkTaskParam.builder()
                    .processDefinitionKey(
                            processDefinitionKey)
                    .processDefinitionName(
                            processDefinitionName)
                    .taskId(taskList.get(0)
                            .getId())
                    .nextIdentity(
                            nextIdentity)
                    .formId(entity.getId()
                            .toString())
                    .dingTalkPcAddr(
                            dingTalkPcAddr)
                    .dingTalkAppAddr(
                            dingTalkAppAddr)
                    .build();
            dingDingMqService.createDingTalkTaskByMq(dingCreateDingTalkTaskParam);
        }
    }

    /**
     * 不用反射的形式
     * 填充流程相关字段
     */
    @Override
    public <T extends ProcessEntity<T>> void richProcessField(T entity) {
        String instanceId = entity.getInstanceId();
        // 当前环节
        if (StringUtil.isNotBlank(instanceId)) {
            List<Task> taskList = taskService.createTaskQuery()
                    .processInstanceId(instanceId)
                    .list(); // 例如请假会签，会同时拥有多个任务
            if (!CollectionUtils.isEmpty(taskList)) {
                TaskEntityImpl task = (TaskEntityImpl) taskList.get(0);
                entity.setTaskId(task.getId());
                if (task.getSuspensionState() == 2) {
                    entity.setTaskName("已挂起").setSuspendState("2").setSuspendStateName("已挂起");
                } else {
                    entity.setTaskName(task.getName()).setSuspendState("1").setSuspendStateName("已激活");
                }
            } else {
                // 已办结或者已撤销
                List<HistoricTaskInstance> list = historyService.createHistoricTaskInstanceQuery()
                        .processInstanceId(instanceId)
                        .orderByTaskCreateTime()
                        .desc()
                        .list();
                if (!CollectionUtils.isEmpty(list)) {
                    HistoricTaskInstance lastTask = list.get(0); // 该流程实例最后一个任务
                    if (lastTask == null) {
                        // 这种情况是流程表被删除，业务表的instanceId找不到对应记录
                        entity.setTaskName("流程已删除");
                        // 流程已删除，前端不能查看审批历史和进度
                        entity.setTaskId("-2");
                    } else {
                        entity.setTaskName(StringUtil.isNotBlank(lastTask.getDeleteReason()) ? "已撤销" : "已结束");
                        // 已撤销或已结束，任务id不妨设置成-1
                        entity.setTaskId("-1");
                    }
                } else {
                    // 这种情况是流程表被删除，业务表的instanceId找不到对应记录
                    entity.setTaskName("流程已删除");
                    // 流程已删除，前端不能查看审批历史和进度
                    entity.setTaskId("-2");
                }
            }
        } else {
            entity.setTaskName("未启动");
        }
    }

    /**
     * 利用 pagehelper 自带的分页功能计算 total 为了减少数据库压力 limit 1,1
     *
     * @param username
     * @return
     */

    @Override
    public long countTodoByUserName(String username) {
        TodoTaskVO vo = new TodoTaskVO();
        vo.setUserId(username);
        PageUtil.startPage(1, 1);
        PageInfo<JSONObject> pageInfo = PageInfo.of(actTaskMapper.todoList(vo));
        PageUtil.clearPage();
        return pageInfo.getTotal();
    }

    @Override
    public long countTodoByProcessKeyANDUserName(String username,String processKey) {
        TodoTaskVO vo = new TodoTaskVO();
        vo.setUserId(username);
        vo.setProcessKey(processKey);
        PageUtil.startPage(1, 1);
        PageInfo<JSONObject> pageInfo = PageInfo.of(actTaskMapper.todoList(vo));
        PageUtil.clearPage();
        return pageInfo.getTotal();
    }

    @Override
    public long countDoneByUserName(String username) {
        DoneTaskVO vo = new DoneTaskVO();
        vo.setUserId(username);
        PageUtil.startPage(1, 1);
        PageInfo<JSONObject> pageInfo = PageInfo.of(actTaskMapper.doneList(vo));
        PageUtil.clearPage();
        return pageInfo.getTotal();
    }

    @Override
    public long countNotifyByUserName(String username) {
        NotifyTaskVO vo = new NotifyTaskVO();
        Long userId = userService.selectUserByUserName(username).getUserId();
        vo.setUserId(userId.toString());
        PageUtil.startPage(1, 1);
        PageInfo<JSONObject> pageInfo = PageInfo.of(actTaskMapper.notifyList(vo));
        PageUtil.clearPage();
        return pageInfo.getTotal();
    }

    @Override
    public boolean notifyStatus(String instanceId) {
        InstanceBusiness instanceBusiness = actTaskMapper.getInstanceBusinessById(instanceId);
        if ( instanceBusiness != null){
           if (actTaskMapper.notifyStatus(instanceId)){
               System.out.println(1);
           }
        }

        return true;
    }

    @Override
    public int notifyListCounts(String username) {
        NotifyTaskVO vo = new NotifyTaskVO();
        Long userId = userService.selectUserByUserName(username).getUserId();
        vo.setUserId(userId.toString());
        if (actTaskMapper.notifyListCounts(vo)==0){
            return 0;
        }else {
            return actTaskMapper.notifyListCounts(vo);
        }

    }

    private <TV extends CommonTaskVO<TV>> void parseTask(@NonNull TV info, @NonNull JSONObject map) {
        // 查询业务表单数据，放入 map 中
        InstanceBusiness instanceBusiness = actTaskMapper.getInstanceBusinessById(map.get("PROC_INST_ID_").toString());
        if (instanceBusiness == null) {
            return;
        }
        JSONObject formData = actTaskMapper.selectBusinessByBusinessKeyAndModule(instanceBusiness.getBusinessKey(),
                instanceBusiness.getModule());
        if (CollectionUtils.isEmpty(formData)) {
            return;
        }
        formData = parseFormData(formData);
        info.setFormData(formData);
        CommonTaskVO commonTaskVO = JSON.to(CommonTaskVO.class, formData);
        info.setApplyUserName(commonTaskVO.getApplyUserName());
    }

    @NonNull
    private JSONObject parseFormData(@NonNull JSONObject sourceMap) {
        JSONObject targetMap = JSONObject.of();
        sourceMap.forEach((k, v) -> targetMap.put(StringUtil.toCamelCase(k.toLowerCase()), v));
        return targetMap;
    }

    @Override
    public PageInfo<TodoTaskVO> pagingTodo(TodoTaskVO vo) {
        PageUtil.startPage();
        PageInfo<JSONObject> mapPageInfo = PageInfo.of(actTaskMapper.todoList(vo));
        PageUtil.clearPage();
        //由于PageHelper的PageInfo没有类型转换方法，这里先草率的用 BeanUtils.copyProperties(); 转换一下
        PageInfo<TodoTaskVO> pageInfo = PageInfo.emptyPageInfo();
        BeanUtil.copyProperties(mapPageInfo, pageInfo);
        List<TodoTaskVO> collect = mapPageInfo.getList().stream().map(map -> {

            TodoTaskVO info = new TodoTaskVO();
            info.setUserId(vo.getUserId());
            info.setTaskName(map.get("NAME_").toString());
            info.setAssigneeName(userService.selectUserByUserName(info.getUserId()).getNickName());
            info.setInstanceId(map.get("PROC_INST_ID_").toString());
            info.setDefName(map.get("DEF_NAME_").toString());


            info.setTaskId(map.get("ID_").toString());
            info.setType("todo");

            info.setSuspendState(map.get("SUSPENSION_STATE_").toString());
            info.setCreateTime((Date) map.get("CREATE_TIME_"));
            info.setSuspendStateName("2".equals(info.getSuspendState()) ? "已挂起" : "已激活");

            // 查询业务表单数据，放入 map 中
            try {
                parseTask(info, map);
            } catch (Exception e) {
                e.printStackTrace();
                return info;
            }
            return info;
        }).collect(Collectors.toList());
        pageInfo.setList(collect);
        return pageInfo;

    }

    @Override
    public PageInfo<DoneTaskVO> pagingDone(DoneTaskVO vo) {
        PageUtil.startPage();
        PageInfo<JSONObject> mapPageInfo = PageInfo.of(actTaskMapper.doneList(vo));
        PageUtil.clearPage();
        //由于PageHelper的PageInfo没有类型转换方法，这里先草率的用 BeanUtils.copyProperties(); 转换一下
        PageInfo<DoneTaskVO> pageInfo = PageInfo.emptyPageInfo();
        BeanUtil.copyProperties(mapPageInfo, pageInfo);
        List<DoneTaskVO> collect = mapPageInfo.getList().stream().map(map -> {
            DoneTaskVO info = new DoneTaskVO();
            info.setUserId(vo.getUserId());
            info.setTaskName(map.get("NAME_").toString());
            info.setAssigneeName(userService.selectUserByUserName(map.get("ASSIGNEE_").toString()).getNickName());
            info.setInstanceId(map.get("PROC_INST_ID_").toString());
            info.setDefName(map.get("DEF_NAME_").toString());


            info.setTaskId(map.get("ID_").toString());
            info.setType("done");

            info.setAssignee(map.get("ASSIGNEE_").toString());
            info.setStartTime(JSON.to(Date.class, map.get("START_TIME_")));
            info.setEndTime(JSON.to(Date.class, map.get("END_TIME_")));

            // 查询业务表单数据，放入 map 中
            try {
                parseTask(info, map);
            } catch (Exception e) {
                e.printStackTrace();
                return info;
            }
            return info;
        }).collect(Collectors.toList());
        pageInfo.setList(collect);
        return pageInfo;
    }

    @Override
    public PageInfo<NotifyTaskVO> pagingNotify(NotifyTaskVO vo) {
        PageUtil.startPage();
        PageInfo<JSONObject> mapPageInfo = PageInfo.of(actTaskMapper.notifyList(vo));
        PageUtil.clearPage();
        //由于PageHelper的PageInfo没有类型转换方法，这里先草率的用 BeanUtils.copyProperties(); 转换一下
        PageInfo<NotifyTaskVO> pageInfo = PageInfo.emptyPageInfo();
        BeanUtil.copyProperties(mapPageInfo, pageInfo);
        List<NotifyTaskVO> collect = mapPageInfo.getList().stream().map(map -> {
            System.out.println(map);
            NotifyTaskVO info = new NotifyTaskVO();
            info.setUserId(vo.getUserId());
            info.setAssigneeName(userService.selectUserByUserName(map.get("START_USER_ID_").toString()).getNickName());
            info.setInstanceId(map.get("PROC_INST_ID_").toString());
            info.setDefName(map.get("DEF_NAME_").toString());
            info.setStatusId(map.get("read_status").toString());
            info.setTaskId("0");
            info.setType("notify");

            info.setAssignee(map.get("START_USER_ID_").toString());
            info.setStartTime(JSON.to(Date.class, map.get("START_TIME_")));
            info.setEndTime(JSON.to(Date.class, map.get("END_TIME_")));

            // 查询业务表单数据，放入 map 中
            try {
                parseTask(info, map);
            } catch (Exception e) {
                e.printStackTrace();
                return info;
            }
            return info;
        }).collect(Collectors.toList());
        pageInfo.setList(collect);
        return pageInfo;
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void complete(String taskId, String instanceId, String variablesStr) {
        String crrentLoginName = SecurityUtil.getUsername();
        JSONObject variables = JSON.parseObject(variablesStr);
        String comment = variables.containsKey("comment") && ObjectUtils.isNotEmpty(variables.get("comment")) ?
                variables.get("comment").toString() :
                "";
        String pass = variables.get("pass").toString();
        try {
            variables.put("pass", "true".equals(pass));
            // 被委派人处理完成任务
            // p.s. 被委托的流程需要先 resolved 这个任务再提交。
            // 所以在 complete 之前需要先 resolved

            // 判断该任务是否是委托任务（转办）
            TaskEntityImpl task = (TaskEntityImpl) taskService.createTaskQuery().taskId(taskId).singleResult();

            // DELEGATION_ 为 PENDING 表示该任务是转办任务
            if (task.getDelegationState() != null && task.getDelegationState().equals(DelegationState.PENDING)) {
                taskService.resolveTask(taskId, variables);
                // 批注说明是转办
                String delegateUserName = userService.selectUserByUserName(crrentLoginName).getNickName();
                comment += "【由" + delegateUserName + "转办】";

                // 如果是 OWNER_ 为 null 的转办任务（候选组的待办），暂且用转办人来签收该任务
                if (StringUtil.isBlank(task.getOwner())) {
                    taskService.claim(taskId, crrentLoginName);
                }
            } else {
                // 只有签收任务，act_hi_taskinst 表的 assignee 字段才不为 null
                taskService.claim(taskId, crrentLoginName);
            }
            if (StringUtil.isNotEmpty(comment)) {
                identityService.setAuthenticatedUserId(crrentLoginName);
                taskService.addComment(taskId, instanceId, comment);
            }
            taskService.complete(taskId, variables);

            // 发送钉钉消息给下级审批人
            pushDingTalkByMQOfComplete(instanceId, variables);

        } catch (Exception e) {
            log.error("error on complete task {}, variables={}", taskId, variables, e);
        }
    }

    @Override
    public <T extends ProcessEntity<T>> void pushDingTalkByMQOfComplete(String instanceId, JSONObject variables) {
        if ("true".equals(configService.selectConfigByKey("sys.account.usingDingTalk"))) {
            JSONObject formData = (JSONObject) variables.get("formData");
            String formId = formData.get("id").toString();
            ProcessInstance instance = runtimeService.createProcessInstanceQuery()
                    .processInstanceId(instanceId)
                    .singleResult();
            List<String> nextIdentity = selectNextIdentity(instance.getId());
            String dingTalkAppAddr = configService.selectConfigByKey("sys.account.dingTalkAppAddr");
            String dingTalkPcAddr = configService.selectConfigByKey("sys.account.dingTalkPcAddr");
            List<Task> taskList = taskService.createTaskQuery()
                    .processInstanceId(instanceId)
                    .orderByProcessInstanceId()
                    .desc()
                    .list();
            String processDefinitionKey = instance.getProcessDefinitionKey();
            String processDefinitionName = instance.getProcessDefinitionName();
            DingCreateDingTalkTaskParam dingCreateDingTalkTaskParam =
                    DingCreateDingTalkTaskParam.builder()
                            .processDefinitionKey(processDefinitionKey)
                            .processDefinitionName(processDefinitionName)
                            .taskId(taskList.get(0).getId())
                            .nextIdentity(nextIdentity)
                            .formId(formId)
                            .dingTalkPcAddr(dingTalkPcAddr)
                            .dingTalkAppAddr(dingTalkAppAddr)
                            .build();
            dingDingMqService.createDingTalkTaskByMq(dingCreateDingTalkTaskParam);
        }
    }

    @Override
    public List<HistoricActivity> selectHistoryList(HistoricActivity historicActivity) {
        List<HistoricActivity> activityList = new ArrayList<>();

        String assignee = historicActivity.getAssignee();
        String activityName = historicActivity.getActivityName();
        String processInstanceId = historicActivity.getProcessInstanceId();

        HistoricActivityInstanceQuery query = historyService.createHistoricActivityInstanceQuery();
        if (StringUtil.isNotBlank(assignee)) {
            query.taskAssignee(assignee);
        }
        if (StringUtil.isNotBlank(activityName)) {
            query.activityName(activityName);
        }
        List<HistoricActivityInstance> list = query.processInstanceId(processInstanceId)
                .activityType("userTask")
                .finished()
                .orderByHistoricActivityInstanceStartTime()
                .asc()
                .list();
        AtomicInteger index = new AtomicInteger(0);
        int size = list.size();
        list.forEach(instance -> {
            HistoricActivity activity = new HistoricActivity();
            BeanUtil.copyProperties(instance, activity);
            String taskId = instance.getTaskId();
            List<Comment> comment = taskService.getTaskComments(taskId, "comment");
            if (!CollectionUtils.isEmpty(comment)) {
                activity.setComment(comment.get(0).getFullMessage());
            }
            // 如果是撤销（deleteReason 不为 null），写入审批意见栏
            if (StringUtil.isNotBlank(activity.getDeleteReason())) {
                activity.setComment(activity.getDeleteReason());
            }
            SysUser sysUser = userService.selectUserByUserName(instance.getAssignee());
            if (sysUser != null) {
                activity.setAssigneeName(sysUser.getNickName());
            }
            if (index.get() == size - 1) {
                activity.setColor("#36b038");
                activity.setIcon("el-icon-check");
            } else {
                activity.setColor("gray");
                activity.setIcon("el-icon-bottom");
            }
            activityList.add(activity);
            index.getAndIncrement();
        });

        // 以下手动封装发起人节点的数据
        query = historyService.createHistoricActivityInstanceQuery();
        HistoricActivityInstance startActivityInstance = query.processInstanceId(processInstanceId)
                .activityType("startEvent")
                .singleResult();
        HistoricActivity startActivity = conversionService.convert(startActivityInstance, HistoricActivity.class);
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();
        startActivity.setAssignee(historicProcessInstance.getStartUserId());
        SysUser sysUser = userService.selectUserByUserName(historicProcessInstance.getStartUserId());
        if (sysUser != null) {
            startActivity.setAssigneeName(sysUser.getNickName());
        }
        startActivity.setComment("提交申请");
        // 如果只有提交申请 则给 绿色 对勾图标，否则给灰色 下箭头图标
        if (ObjectUtils.isEmpty(activityList)) {
            startActivity.setIcon("el-icon-check");
            startActivity.setColor("#36b038");
        } else {
            startActivity.setIcon("el-icon-bottom");
            startActivity.setColor("gray");
        }

        // 手动过滤该条发起人数据
        boolean necessaryAdd = (!StringUtil.isNotBlank(activityName) || startActivity.getActivityName()
                .equals(activityName)) && (!StringUtil.isNotBlank(
                assignee) || startActivity.getAssignee().equals(assignee));
        if (necessaryAdd) {
            activityList.add(0, startActivity);
        }


        // 以下手动封装结束节点的数据
        HistoricActivity endActivity = new HistoricActivity();
        query = historyService.createHistoricActivityInstanceQuery();
        HistoricActivityInstance endActivityInstance = query.processInstanceId(processInstanceId)
                .activityType("endEvent")
                .singleResult();
        if (null != endActivityInstance) {
            BeanUtil.copyProperties(endActivityInstance, endActivity);
            endActivity.setAssignee("admin");
            sysUser = userService.selectUserByUserName("admin");
            if (sysUser != null) {
                endActivity.setAssigneeName(sysUser.getNickName());
            }
            endActivity.setComment("自动结束");

            // 手动过滤该条发起人数据
            necessaryAdd = (!StringUtil.isNotBlank(activityName) || endActivity.getActivityName()
                    .equals(activityName)) && (!StringUtil.isNotBlank(
                    assignee) || endActivity.getAssignee().equals(assignee));
            if (necessaryAdd) {
                // 如果有结束节点, 则给结束节点 绿色 对勾图标
                endActivity.setIcon("el-icon-check");
                endActivity.setColor("#36b038");
                // 如果有结束节点, 则给倒数第二个节点 灰色 下箭头图标
                activityList.get(activityList.size() - 1).setIcon("el-icon-bottom");
                activityList.get(activityList.size() - 1).setColor("gray");

                activityList.add(endActivity);
            }
        }

        // 以下处理待办节点
        List<String> userNameList = selectNextIdentity(processInstanceId);
        if (!userNameList.isEmpty()) {
            String[] userNickNames = userService.lambdaQuery()
                    .select(SysUser::getNickName)
                    .in(SysUser::getUserName, userNameList)
                    .list()
                    .stream()
                    .map(SysUser::getNickName)
                    .toArray(String[]::new);
            HistoricActivity todoActivity = new HistoricActivity();
            todoActivity.setIcon("el-icon-bell");
            todoActivity.setColor("orange");
            todoActivity.setAssigneeName("待办");
            todoActivity.setComment(String.join(" , ", userNickNames));
            activityList.add(todoActivity);
        }

        // 以下处理知会节点
        List<JSONObject> businessNotifies = actTaskMapper.selectBusinessNotifyByInstanceId(processInstanceId);
        if (ObjectUtils.isNotEmpty(businessNotifies)) {
            String[] userNickNames = userService.lambdaQuery()
                    .select(SysUser::getNickName)
                    .in(SysUser::getUserId,
                            businessNotifies.stream()
                                    .map(map -> map.get("user_id"))
                                    .collect(Collectors.toList()))
                    .list()
                    .stream()
                    .map(SysUser::getNickName)
                    .toArray(String[]::new);
            HistoricActivity notifyActivity = new HistoricActivity();
            notifyActivity.setIcon("el-icon-bell");
            notifyActivity.setColor("orange");
            notifyActivity.setAssigneeName("知会");
            notifyActivity.setComment(String.join(" , ", userNickNames));
            activityList.add(notifyActivity);
        }


        return activityList;
    }

    @Override
    public void delegate(String taskId, String fromUser, String delegateToUser) {
        taskService.delegateTask(taskId, delegateToUser);
    }

    /**
     * 执行此方法后未审批的任务 act_ru_task 会被删除，流程历史 act_hi_taskinst 不会被删除，并且流程历史的状态为finished完成
     *
     * @param instanceId
     * @param deleteReason
     */
    @Override
    public void cancelApply(String instanceId, String deleteReason) {
        runtimeService.deleteProcessInstance(instanceId, deleteReason);
    }

    @Override
    public void cancelApply(TodoTaskVO vo) {
        // 查询业务表单数据，放入 map 中
        InstanceBusiness instanceBusiness = actTaskMapper.getInstanceBusinessById(vo.getInstanceId());
        if (instanceBusiness == null) {
            return;
        }
        String module = instanceBusiness.getModule();
        actTaskMapper.initBusinessStatus(BizStatus.CANCEL.getCode(), module, vo.getBusinessId());

        // 撤销时，删除钉钉待办任务
        if ("true".equals(configService.selectConfigByKey("sys.account.usingDingTalk"))) {
            List<Task> taskList = taskService.createTaskQuery()
                    .processInstanceId(vo.getInstanceId())
                    .orderByProcessInstanceId()
                    .desc()
                    .list();
            dingDingMqService.deleteDingTalkTaskByMq(taskList.get(0).getId());
        }

        // 执行此方法后未审批的任务 act_ru_task 会被删除，流程历史 act_hi_taskinst 不会被删除，并且流程历史的状态为finished完成
        runtimeService.deleteProcessInstance(vo.getInstanceId(), "用户撤销");

    }


    @Override
    public int readBusinessNotify(String instanceId, Long userId) {
        return actTaskMapper.readBusinessNotify(instanceId, userId);
    }


    @Override
    public void suspendOrActiveApply(String instanceId, String suspendState) {
        if ("1".equals(suspendState)) {
            // 当流程实例被挂起时，无法通过下一个节点对应的任务id来继续这个流程实例。
            // 通过挂起某一特定的流程实例，可以终止当前的流程实例，而不影响到该流程定义的其他流程实例。
            // 激活之后可以继续该流程实例，不会对后续任务造成影响。
            // 直观变化：act_ru_task 的 SUSPENSION_STATE_ 为 2
            runtimeService.suspendProcessInstanceById(instanceId);
        } else if ("2".equals(suspendState)) {
            runtimeService.activateProcessInstanceById(instanceId);
        }
    }

}

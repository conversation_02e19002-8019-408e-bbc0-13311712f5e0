package com.ruoyi.activiti.dao.impl;

import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.activiti.dao.IProcessService;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.activiti.mapper.IActMapper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;

public class ActDaoImpl<M extends IActMapper<T>, T extends ProcessEntity<T>> extends BaseDaoImpl<M, T> implements IActDao<T> {

    @Override
    public String processKey() {
        return getEntityClass().getSimpleName();
    }

    /**
     * @return
     */
    @Override
    public IProcessService processService() {
        return processService;
    }

}

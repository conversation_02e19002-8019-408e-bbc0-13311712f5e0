package com.ruoyi.activiti.controller;

import com.ruoyi.activiti.config.ICustomProcessDiagramGenerator;
import com.ruoyi.activiti.config.WorkflowConstants;
import com.ruoyi.activiti.dao.IProcessService;
import com.ruoyi.activiti.domain.*;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.common.page.TableDataInfo;
import com.ruoyi.common.util.SecurityUtil;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.system.dao.ISysUserDao;
import lombok.RequiredArgsConstructor;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.repository.ProcessDefinitionQuery;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/activiti/process")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProcessController extends BaseController implements IBaseController {

    private final RepositoryService repositoryService;
    private final HistoryService historyService;
    private final ProcessEngine processEngine;
    private final IProcessService processService;
    private final RuntimeService runtimeService;
    private final ISysUserDao userService;

    /**
     * TODO 审批历史列表
     */

    @PostMapping("/listHistory")
    public TableDataInfo<HistoricActivity> listHistory(@RequestBody HistoricActivity historicActivity) {
        return getDataTable(() -> processService.selectHistoryList(historicActivity));
    }

    /**
     * 进度查看
     */

    @RequestMapping(value = "/read-resource")
    public void readResource(String pProcessInstanceId, HttpServletResponse response) throws Exception {
        // 设置页面不缓存
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);

        String processDefinitionId = "";
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(pProcessInstanceId).singleResult();
        if (processInstance == null) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(pProcessInstanceId).singleResult();
            processDefinitionId = historicProcessInstance.getProcessDefinitionId();
        } else {
            processDefinitionId = processInstance.getProcessDefinitionId();
        }
        ProcessDefinitionQuery pdq = repositoryService.createProcessDefinitionQuery();
        ProcessDefinition pd = pdq.processDefinitionId(processDefinitionId).singleResult();

        String resourceName = pd.getDiagramResourceName();

        if (resourceName.endsWith(".png") && !StringUtil.isEmpty(pProcessInstanceId)) {
            getActivitiProccessImage(pProcessInstanceId, response);
            // ProcessDiagramGenerator.generateDiagram(pde, "png",
            // getRuntimeService().getActiveActivityIds(processInstanceId));
        } else {
            // 通过接口读取
            InputStream resourceAsStream = repositoryService.getResourceAsStream(pd.getDeploymentId(), resourceName);
            // 输出资源内容到相应对象
            byte[] b = new byte[1024];
            int len = -1;
            while ((len = resourceAsStream.read(b, 0, 1024)) != -1) {
                response.getOutputStream().write(b, 0, len);
            }
        }
    }

    /**
     * 获取流程图像，已执行节点和流程线高亮显示
     */
    private void getActivitiProccessImage(String pProcessInstanceId, HttpServletResponse response) {
        // logger.info("[开始]-获取流程图图像");
        try {
            //  获取历史流程实例
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(pProcessInstanceId).singleResult();
            if (historicProcessInstance == null) {
                // throw new BusinessException("获取流程实例ID[" + pProcessInstanceId + "]对应的历史流程实例失败！");
            } else {
                // 获取流程定义
//                ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) ((RepositoryServiceImpl) repositoryService).getDeployedProcessDefinition(historicProcessInstance.getProcessDefinitionId());

                // 获取流程历史中已执行节点，并按照节点在流程中执行先后顺序排序
                List<HistoricActivityInstance> historicActivityInstanceList = historyService.createHistoricActivityInstanceQuery().processInstanceId(pProcessInstanceId).orderByHistoricActivityInstanceId().asc().list();

                // 已执行的节点ID集合
                List<String> executedActivityIdList = new ArrayList<>();
                int index = 1;
                // logger.info("获取已经执行的节点ID");
                for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
                    executedActivityIdList.add(activityInstance.getActivityId());

                    // logger.info("第[" + index + "]个已执行节点=" + activityInstance.getActivityId() + " : "
                    // +activityInstance.getActivityName());
                    index++;
                }

                BpmnModel bpmnModel = repositoryService.getBpmnModel(historicProcessInstance.getProcessDefinitionId());

                // 已执行的线集合
                List<String> flowIds = new ArrayList<>();
                // 获取流程走过的线 (getHighLightedFlows是下面的方法)
                flowIds = getHighLightedFlows(bpmnModel, historicActivityInstanceList);

                //                // 获取流程图图像字符流
                //                ProcessDiagramGenerator pec =
                // processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
                //                //配置字体
                //                InputStream imageStream = pec.generateDiagram(bpmnModel, "png",
                // executedActivityIdList, flowIds,Constants.FONT,"微软雅黑","黑体",null,2.0);

                Set<String> currIds = runtimeService.createExecutionQuery().processInstanceId(pProcessInstanceId).list().stream().map(Execution::getActivityId).collect(Collectors.toSet());

                ICustomProcessDiagramGenerator diagramGenerator = (ICustomProcessDiagramGenerator) processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
                InputStream imageStream = diagramGenerator.generateDiagram(bpmnModel, "png", executedActivityIdList, flowIds, Constants.FONT, Constants.FONT, Constants.FONT, null, 1.0, new Color[]{WorkflowConstants.COLOR_NORMAL, WorkflowConstants.COLOR_CURRENT}, currIds);
                response.setContentType("image/png");
                OutputStream os = response.getOutputStream();
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                while ((bytesRead = imageStream.read(buffer, 0, 8192)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.close();
                imageStream.close();
            }
            // logger.info("[完成]-获取流程图图像");
        } catch (Exception e) {
//            System.out.println(e.getMessage());
            // logger.error("【异常】-获取流程图失败！" + e.getMessage());
            // throw new BusinessException("获取流程图失败！" + e.getMessage());
        }
    }

    private List<String> getHighLightedFlows(BpmnModel bpmnModel, List<HistoricActivityInstance> historicActivityInstances) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); // 24小时制
        List<String> highFlows = new ArrayList<>(); // 用以保存高亮的线flowId

        for (int i = 0; i < historicActivityInstances.size() - 1; i++) {
            // 对历史流程节点进行遍历
            // 得到节点定义的详细信息
            FlowNode activityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(i).getActivityId());

            List<FlowNode> sameStartTimeNodes = new ArrayList<>(); // 用以保存后续开始时间相同的节点
            FlowNode sameActivityImpl1 = null;

            HistoricActivityInstance activityImpl_ = historicActivityInstances.get(i); // 第一个节点
            HistoricActivityInstance activityImp2_;

            for (int k = i + 1; k <= historicActivityInstances.size() - 1; k++) {
                activityImp2_ = historicActivityInstances.get(k); // 后续第1个节点

                if ("userTask".equals(activityImpl_.getActivityType()) && "userTask".equals(activityImp2_.getActivityType()) && df.format(activityImpl_.getStartTime()).equals(df.format(activityImp2_.getStartTime()))) // 都是usertask，且主节点与后续节点的开始时间相同，说明不是真实的后继节点
                {

                } else {
                    sameActivityImpl1 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstances.get(k).getActivityId()); // 找到紧跟在后面的一个节点
                    break;
                }
            }
            sameStartTimeNodes.add(sameActivityImpl1); // 将后面第一个节点放在时间相同节点的集合里
            for (int j = i + 1; j < historicActivityInstances.size() - 1; j++) {
                HistoricActivityInstance activityImpl1 = historicActivityInstances.get(j); // 后续第一个节点
                HistoricActivityInstance activityImpl2 = historicActivityInstances.get(j + 1); // 后续第二个节点

                if (df.format(activityImpl1.getStartTime()).equals(df.format(activityImpl2.getStartTime()))) { // 如果第一个节点和第二个节点开始时间相同保存
                    FlowNode sameActivityImpl2 = (FlowNode) bpmnModel.getMainProcess().getFlowElement(activityImpl2.getActivityId());
                    sameStartTimeNodes.add(sameActivityImpl2);
                } else { // 有不相同跳出循环
                    break;
                }
            }
            List<SequenceFlow> pvmTransitions = activityImpl.getOutgoingFlows(); // 取出节点的所有出去的线

            for (SequenceFlow pvmTransition : pvmTransitions) { // 对所有的线进行遍历
                FlowNode pvmActivityImpl = (FlowNode) bpmnModel.getMainProcess().getFlowElement(pvmTransition.getTargetRef()); // 如果取出的线的目标节点存在时间相同的节点里，保存该线的id，进行高亮显示
                if (sameStartTimeNodes.contains(pvmActivityImpl)) {
                    highFlows.add(pvmTransition.getId());
                }
            }
        }
        return highFlows;
    }

    /**
     * 转办
     */

    @PostMapping("/delegate")
    public AjaxResult delegate(String taskId, String delegateToUser) {
        processService.delegate(taskId, SecurityUtil.getUsername(), delegateToUser);
        return success();
    }


    @PostMapping("/cancelApply")
    public AjaxResult cancelApply(@RequestBody TodoTaskVO vo) {
        processService.cancelApply(vo);
        return success();
    }

    /**
     * 激活/挂起流程实例
     */

    @PostMapping("/suspendOrActiveApply")
    public AjaxResult suspendOrActiveApply(String instanceId, String suspendState) {
        processService.suspendOrActiveApply(instanceId, suspendState);
        return success();
    }


    @GetMapping("/pagingTodo")
    public AjaxResult pagingTodo(TodoTaskVO vo) {
        vo.setUserId(SecurityUtil.getUsername());
        return success(processService.pagingTodo(vo));
    }


    @GetMapping("countTodoByUsername")
    public AjaxResult countTodoByUsername(String username) {
        return success(processService.countTodoByUserName(username));
    }
    @GetMapping("countTodoByProcessKeyANDUserName")
    public AjaxResult countTodoByProcessKey( String username,String processKey) {
        return success(processService.countTodoByProcessKeyANDUserName(username,processKey));
    }

    @GetMapping("countDoneByUsername")
    public AjaxResult countDoneByUsername(String username) {
        return success(processService.countDoneByUserName(username));
    }


    @GetMapping("countNotifyByUsername")
    public AjaxResult countNotifyByUsername(String username) {
        return success(processService.countNotifyByUserName(username));
    }
    /**
     * 我的待阅数量
     */
    @GetMapping("notifyListCounts")
    public AjaxResult notifyListCounts(String username) {
        return success(processService.notifyListCounts(username));
    }
    /**
     * 修改待阅的状态
     */
    @GetMapping("notifyStatus")
    public AjaxResult notifyStatus(@RequestParam String instanceId) {
        return success(processService.notifyStatus(instanceId));
    }

    @GetMapping("selectNextNickNames")
    public AjaxResult selectNextNickNames(@RequestParam String instanceId) {
        List<String> list = processService.selectNextIdentity(instanceId);
        Object[] obj = list.stream().map(userService::selectUserByUserName).filter(Objects::nonNull).map(SysUser::getNickName).toArray();
        return AjaxResult.success(StringUtil.join(list.stream().map(userService::selectUserByUserName).filter(Objects::nonNull).map(SysUser::getNickName).toArray(), ","));
    }
    /**
     * 办理任务
     */

    @PostMapping("/complete")
    public AjaxResult complete(@RequestBody CompleteData completeData) {
        return success(processService.complete(completeData));
    }


    @GetMapping("/pagingDone")
    public AjaxResult pagingDone(DoneTaskVO vo) {
        vo.setUserId(SecurityUtil.getUsername());
        return success(processService.pagingDone(vo));
    }

    /**
     * 我的已办列表
     */

    @GetMapping("/pagingNotify")
    public AjaxResult pagingNotify(NotifyTaskVO vo) {
        vo.setUserId(SecurityUtil.getUserId().toString());
        return success(processService.pagingNotify(vo));
    }


    @GetMapping("readBusinessNotify")
    public AjaxResult readBusinessNotify(@RequestParam String instanceId) {
        return success(processService.readBusinessNotify(instanceId, SecurityUtil.getUserId()));
    }
}

package com.ruoyi.activiti.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 模型管理对象 act_model_manage
 *
 * <AUTHOR>
 * @date 2022-10-10
 */

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ActModelManage extends BaseEntity<ActModelManage> {


    /**
     * ID
     */

    private String modelId;

    /**
     * 模型标识
     */

    @Excel(name = "模型标识")
    private String modelKey;

    /**
     * 模型名称
     */

    @Excel(name = "模型名称")
    private String modelName;

    /**
     * 分类
     */

    @Excel(name = "分类")
    private String category;

    /**
     * 备注
     */

    @Excel(name = "备注")
    private String description;


    @Excel(name = "版本号")
    private Integer version;


    private String deploymentId;

}

package com.ruoyi.minio;

import io.minio.*;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;

@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@Component
public class MinioTemplate {

    private final MinioProperties properties;

    private final MinioClient minioClient;


    /**
     * 判断桶是否存在,如果存在返回true,如果不存在返回false
     *
     * @param bucketName
     * @return
     */
    @SneakyThrows
    public Boolean existBucket(String bucketName) throws Exception {
        try {
            boolean exist = minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
            //boolean exist = minioClient.bucketExists(bucketName);
            return exist;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }


    }

    /**
     * 创建桶
     *
     * @param bucketName
     * @return
     */
    @SneakyThrows
    public void makeBucket(String bucketName) {
        minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
    }

    /**
     * 上传对象
     *
     * @param objectName
     * @param file
     * @return
     * @throws Exception
     */
    @SneakyThrows
    public void putObject(String objectName, MultipartFile file) {

        //判断 桶是否存在
        boolean flag = existBucket(properties.getBucketName());

        if (flag) {
            PutObjectArgs args = PutObjectArgs.builder()
                    .bucket(properties.getBucketName())
                    .object(objectName)
                    .contentType(file.getContentType())
                    .stream(file.getInputStream(), file.getSize(), -1)
                    .build();
            minioClient.putObject(args);
        }
    }

    /**
     * 删除对象
     *
     * @param objectName
     * @return
     * @throws Exception
     */
    @SneakyThrows
    public boolean removeObject(String objectName) {
        boolean flag = existBucket(properties.getBucketName());
        if (flag) {
            RemoveObjectArgs args = RemoveObjectArgs.builder()
                    .bucket(properties.getBucketName())
                    .object(objectName)
                    .build();
            minioClient.removeObject(args);
            return true;
        }
        return false;
    }

    /**
     * 获取对象信息
     *
     * @param objectName
     * @return
     * @throws Exception
     */
    @SneakyThrows
    public ObjectStat getMessage(String objectName) {
        boolean flag = existBucket(properties.getBucketName());
        if (flag) {
            ObjectStat statObject = minioClient.statObject(StatObjectArgs.builder()
                    .bucket(properties.getBucketName())
                    .object(objectName)
                    .build());
            return statObject;
        }
        return null;
    }

    /**
     * 返回的文件路径,不会过期.
     *
     * @param objectName
     * @return
     */
    @SneakyThrows
    public String getObjectUrl(String objectName) {
        Boolean flag = existBucket(properties.getBucketName());
        String url = "";
        if (flag) {
            url = minioClient.getObjectUrl(properties.getBucketName(), objectName);

        }
        return url;

    }

    /**
     * 下载
     *
     * @param filename
     * @param response
     */
    public void getObject(String filename, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            in = minioClient.getObject(GetObjectArgs.builder().bucket(properties.getBucketName()).object(filename).build());
            int length = 0;
            byte[] buffer = new byte[1024];
            out = response.getOutputStream();
            response.reset();
            response.addHeader("Content-Disposition",
                    " attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
            response.setContentType("application/octet-stream");
            while ((length = in.read(buffer)) > 0) {
                out.write(buffer, 0, length);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }


}

package com.ruoyi.common.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.yulichang.annotation.EntityMapping;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class SysDept extends BaseEntity<SysDept> {

    @TableId(type = IdType.AUTO)
    private Long deptId;
    private Long parentId;
    private String ancestors;
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 30, message = "部门名称长度不能超过30个字符")
    private String deptName;
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;
    private String leader;
    @Size(max = 11, message = "联系电话长度不能超过11个字符")
    private String phone;
    @Email(message = "邮箱格式不正确")
    @Size(max = 50, message = "邮箱长度不能超过50个字符")
    private String email;
    private String status;
    @TableLogic(delval = "2")
    private String delFlag;

    /**
     * 是否进入管理范畴
     */
    @Excel(name = "是否进入管理范畴")
    private Character inManagementFlag;

    /**
     * 部门编码
     */
    private String code;

    //TODO 由于不明原因 这个字段不可去除 否则会 java.lang.NoSuchMethodError: com.ruoyi.common.domain.SysDept.getParentName()Ljava/lang/String;
    @TableField(exist = false)
    private String parentName;
    @TableField(exist = false)
    @EntityMapping(joinField = Fields.parentId)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private List<SysDept> children = new ArrayList<>();
    @TableField(exist = false)
    private Long id;
    @TableField(exist = false)
    private String createById;
    @TableField(exist = false)
    private String updateById;
    @TableField(exist = false)
    private String remark;

    /**
     * 名称缩写
     */
    private String acronymName;

    /**
     * 部门类型
     */
    private String type;


    /**
     * 需要过滤掉的Type
     */
    @TableField(exist = false)
    private Long[] filteredTypes;

  /*  @TableField(exist = false)
    private Long wellheadId;

    @TableField(exist = false)
    private String wellheadName;*/
}

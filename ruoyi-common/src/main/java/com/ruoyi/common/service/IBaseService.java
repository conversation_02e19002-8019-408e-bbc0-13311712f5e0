package com.ruoyi.common.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.LoginUser;
import com.ruoyi.common.page.PageDomain;
import com.ruoyi.common.page.PageUtil;
import com.ruoyi.common.page.TableDataInfo;
import com.ruoyi.common.page.TableSupport;
import com.ruoyi.common.redis.RedisCache;
import com.ruoyi.common.util.SecurityUtil;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.sql.SqlUtil;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;
import java.util.function.Supplier;

public interface IBaseService {
    ConversionService conversionService();

    TransactionTemplate transactionTemplate();

    ThreadPoolTaskExecutor threadPoolTaskExecutor();

    RedisCache redisCache();

    Environment environment();

    /**
     * 设置请求分页数据
     */
    default void startPage() {
        PageUtil.startPage();
    }

    /**
     * 设置请求排序数据
     */
    default void startOrderBy() {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        if (StringUtil.isNotEmpty(pageDomain.getOrderBy())) {
            String orderBy = SqlUtil.escapeOrderBySql(pageDomain.getOrderBy());
            PageHelper.orderBy(orderBy);
        }
    }

    /**
     * 清理分页的线程变量
     */
    default void clearPage() {
        PageUtil.clearPage();
    }

    /**
     * 响应请求分页数据
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Deprecated
    default <T> TableDataInfo<T> getDataTable(List<T> list) {
        clearPage();
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * 重载上面的方法
     *
     * @param supplier
     * @param <T>
     * @return
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    @Deprecated
    default <T> TableDataInfo<T> getDataTable(Supplier<List<T>> supplier) {
        PageInfo<T> pageInfo = getPageInfo(supplier);
        return new TableDataInfo(pageInfo.getList(), pageInfo.getTotal()).setCode(HttpStatus.SUCCESS).setMsg("查询成功");
    }

    /**
     * PageHelper原生分页
     *
     * @param supplier
     * @param <T>
     * @return
     */
    default <T> PageInfo<T> getPageInfo(Supplier<List<T>> supplier) {
        startPage();
        PageInfo<T> pageInfo = PageInfo.of(supplier.get());
        clearPage();
        return pageInfo;
    }

    /**
     * 有可能是分页
     *
     * @param supplier
     * @param <T>
     * @return
     */
    default <T> AjaxResult list(Supplier<List<T>> supplier) {
        return success(getPageInfo(supplier));
    }

    /**
     * 返回成功
     */
    default AjaxResult success() {
        return AjaxResult.success();
    }

    /**
     * 返回失败消息
     */
    default AjaxResult error() {
        return AjaxResult.error();
    }

    /**
     * 返回成功消息
     */
    default AjaxResult success(String message) {
        return AjaxResult.success(message);
    }

    /**
     * 返回成功消息
     */
    default AjaxResult success(Object data) {
        return AjaxResult.success(data);
    }

    /**
     * 返回失败消息
     */
    default AjaxResult error(String message) {
        return AjaxResult.error(message);
    }

    /**
     * 返回警告消息
     */
    default AjaxResult warn(String message) {
        return AjaxResult.warn(message);
    }

    /**
     * 响应返回结果
     *
     * @param rows 影响行数
     * @return 操作结果
     */
    default AjaxResult toAjax(int rows) {
        return rows > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param id 插入的行id
     * @return 操作结果
     */
    default AjaxResult toAjax(Long id) {
        return id > 0 ? AjaxResult.success(id) : AjaxResult.error();
    }

    /**
     * 响应返回结果
     *
     * @param result 结果
     * @return 操作结果
     */
    default AjaxResult toAjax(boolean result) {
        return result ? success() : error();
    }

    /**
     * 页面跳转
     */
    default String redirect(String url) {
        return StringUtil.format("redirect:{}", url);
    }

    /**
     * 获取用户缓存信息
     */
    default LoginUser getLoginUser() {
        return SecurityUtil.getLoginUser();
    }

    /**
     * 获取登录用户id
     */
    default Long getUserId() {
        return getLoginUser().getUserId();
    }

    /**
     * 获取登录部门id
     */
    default Long getDeptId() {
        return getLoginUser().getDeptId();
    }

    /**
     * 获取登录用户名
     */
    default String getUsername() {
        return getLoginUser().getUsername();
    }
}

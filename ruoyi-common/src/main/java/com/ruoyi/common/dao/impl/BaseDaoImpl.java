package com.ruoyi.common.dao.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.common.redis.RedisCache;
import org.apache.ibatis.binding.BindingException;
import org.springframework.core.convert.ConversionService;
import org.springframework.core.env.Environment;
import org.springframework.data.repository.NoRepositoryBean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

@SuppressWarnings("unchecked")
@NoRepositoryBean
public abstract class BaseDaoImpl<M extends IBaseMapper<T>, T extends BaseEntity<T>> extends MPJBaseServiceImpl<M, T> implements IBaseDao<T> {

    @Override
    public ConversionService conversionService() {
        return conversionService;
    }

    @Override
    public TransactionTemplate transactionTemplate() {
        return transactionTemplate;
    }

    @Override
    public ThreadPoolTaskExecutor threadPoolTaskExecutor() {
        return applicationTaskExecutor;
    }

    @Override
    public RedisCache redisCache() {
        return redisCache;
    }

    @Override
    public Environment environment() {
        return environment;
    }

    /**
     * @param entity
     * @param config
     * @return
     */
    @Override
    public List<T> list(T entity, DeepConfig<T>... config) {
        List<T> list;
        try {
            list = getBaseMapper().list(entity);
        } catch (BindingException e) {
            log.warn(entity.getClass().getName() + "Mapper.xml中未定义list方法.暂时用公共查询！");
            list = lambdaQuery(entity).list();
        }
        return config != null && config.length > 0 ? Relation.list(list, 0, config[0]) : list;
    }
}

package com.ruoyi.common.enums;

import lombok.Getter;

/**
 * 要素费用单分配方式(0:未分配 ,1:直接分配,2:共耗分配)
 */
@Getter
public enum CMElementCostBillDistributionMethod {
    UNDISTRIBUTED("0", "未分配"),
    DIRECT_DISTRIBUTION("1", "直接分配"),
    COST_DISTRIBUTION("2", "共耗分配");


    private final String code;
    private final String info;

    CMElementCostBillDistributionMethod(String code, String info) {
        this.code = code;
        this.info = info;
    }
}

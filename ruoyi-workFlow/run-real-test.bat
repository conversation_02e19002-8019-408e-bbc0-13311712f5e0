@echo off
chcp 65001 >nul
echo ========================================
echo 真实场景测试：三甲矿区排序问题
echo ========================================
echo.

echo 正在编译测试类...
cd /d "%~dp0"

:: 创建输出目录
if not exist "target\test-classes" mkdir target\test-classes

:: 编译测试类
echo 编译 RealScenarioTest...
javac -encoding UTF-8 -d target\test-classes src\test\java\com\ruoyi\cm\electricityDetails\test\RealScenarioTest.java 2>nul

if errorlevel 1 (
    echo 编译失败，尝试简单编译...
    javac -d target\test-classes src\test\java\com\ruoyi\cm\electricityDetails\test\RealScenarioTest.java 2>nul
    if errorlevel 1 (
        echo 编译失败，请检查Java环境
        pause
        exit /b 1
    )
)

echo 编译成功！
echo.

echo ========================================
echo 运行真实场景测试
echo ========================================
java -cp target\test-classes com.ruoyi.cm.electricityDetails.test.RealScenarioTest

echo.
echo ========================================
echo 测试完成
echo ========================================
echo.
echo 这个测试展示了：
echo 1. 问题：Excel中三甲矿区在前面，但显示时在后面
echo 2. 原因：批量添加时创建时间相同，ID降序排列颠倒了顺序
echo 3. 解决方案：创建时间微调 + 新排序逻辑
echo 4. 效果：保持Excel原始顺序，最新批次在前面
echo.
pause

@echo off
echo ========================================
echo 电耗明细排序功能测试
echo ========================================
echo.

echo 正在编译测试类...
cd /d "%~dp0"

:: 设置类路径
set CLASSPATH=src\main\java;src\test\java;target\classes

:: 编译依赖的主类
echo 编译 CmElectricityDetails...
javac -cp "%CLASSPATH%" -d target\test-classes src\main\java\com\ruoyi\cm\electricityDetails\domain\CmElectricityDetails.java src\main\java\com\ruoyi\common\domain\BaseEntity.java 2>nul

:: 编译测试类
echo 编译测试类...
javac -cp "%CLASSPATH%;target\test-classes" -d target\test-classes src\test\java\com\ruoyi\cm\electricityDetails\test\SortOrderTest.java 2>nul
javac -cp "%CLASSPATH%;target\test-classes" -d target\test-classes src\test\java\com\ruoyi\cm\electricityDetails\test\MockDatabaseTest.java 2>nul

if errorlevel 1 (
    echo 编译失败，请检查Java环境和依赖
    pause
    exit /b 1
)

echo 编译成功！
echo.

echo ========================================
echo 运行排序逻辑测试
echo ========================================
java -cp "%CLASSPATH%;target\test-classes" com.ruoyi.cm.electricityDetails.test.SortOrderTest

echo.
echo ========================================
echo 运行模拟数据库测试
echo ========================================
java -cp "%CLASSPATH%;target\test-classes" com.ruoyi.cm.electricityDetails.test.MockDatabaseTest

echo.
echo ========================================
echo 测试完成
echo ========================================
pause

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.indicator.bizAssessmentDocumentsItems.mapper.bizAssessmentDocumentsItemsMapper">
    <resultMap type="bizAssessmentDocumentsItems" id="bizAssessmentDocumentsItemsResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="indicatorId" column="indicator_id"/>
            <result property="indicatorName" column="indicator_name"/>
            <result property="assessmentContent" column="assessment_content"/>
            <result property="assessmentBasis" column="assessment_basis"/>
            <result property="assessmentAmount" column="assessment_amount"/>
            <result property="upperThreshold" column="upper_threshold"/>
            <result property="lowerThreshold" column="lower_threshold"/>
            <result property="actualValue" column="actual_value"/>
            <result property="evaluationResults" column="evaluation_results"/>
            <result property="assessmentReason" column="assessment_reason"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.indicator_id,
            t.indicator_name,
            t.assessment_content,
            t.assessment_basis,
            t.assessment_amount,
            t.upper_threshold,
            t.lower_threshold,
            t.actual_value,
            t.evaluation_results,
            t.assessment_reason,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time,
            t.del_flag
        from biz_assessment_documents_items t
        where t.id in
              (select distinct t.id
        from biz_assessment_documents_items t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="bizAssessmentDocumentsItems" resultMap="bizAssessmentDocumentsItemsResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="indicatorId != null ">
                            and t.indicator_id = #{indicatorId}
                        </if>
                        <if test="indicatorName != null  and indicatorName != ''">
                            and t.indicator_name LIKE concat('%', #{indicatorName}, '%')
                        </if>
                        <if test="assessmentContent != null  and assessmentContent != ''">
                            and t.assessment_content = #{assessmentContent}
                        </if>
                        <if test="assessmentBasis != null  and assessmentBasis != ''">
                            and t.assessment_basis = #{assessmentBasis}
                        </if>
                        <if test="assessmentAmount != null ">
                            and t.assessment_amount = #{assessmentAmount}
                        </if>
                        <if test="upperThreshold != null ">
                            and t.upper_threshold = #{upperThreshold}
                        </if>
                        <if test="lowerThreshold != null ">
                            and t.lower_threshold = #{lowerThreshold}
                        </if>
                        <if test="actualValue != null ">
                            and t.actual_value = #{actualValue}
                        </if>
                        <if test="evaluationResults != null  and evaluationResults != ''">
                            and t.evaluation_results = #{evaluationResults}
                        </if>
                        <if test="assessmentReason != null  and assessmentReason != ''">
                            and t.assessment_reason = #{assessmentReason}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

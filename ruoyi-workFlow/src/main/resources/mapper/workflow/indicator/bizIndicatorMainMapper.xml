<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.indicator.bizIndicatorMain.mapper.bizIndicatorMainMapper">
    <resultMap type="bizIndicatorMain" id="bizIndicatorMainResult">
        <result property="id" column="id"/>
        <result property="indicatorCode" column="indicator_code"/>
        <result property="indicatorName" column="indicator_name"/>
        <result property="levelCategory" column="level_category"/>
        <result property="description" column="description"/>
        <result property="indicatorType" column="indicator_type"/>
        <result property="unit" column="unit"/>
        <result property="statisticsTime" column="statistics_time"/>
        <result property="ownerDepartment" column="owner_department"/>
        <result property="deptId" column="dept_id"/>
        <result property="responsibleId" column="responsible_id"/>
        <result property="assessmentType" column="assessment_type"/>
        <result property="assessControl" column="assess_control"/>
        <result property="versionNo" column="version_no"/>
        <result property="expiredStatus" column="expired_status"/>
        <result property="indicatorProperty" column="indicator_property"/>
        <result property="remark" column="remark"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.indicator_code,
            t.indicator_name,
            t.level_category,
            (select classification_name from biz_indicator_classification c where c.id = t.level_category) as level_category_name,
            t.description,
            t.indicator_type,
            t.unit,
            t.statistics_time,
            t.owner_department,
            t.dept_id,
            t.responsible_id,
            t.assessment_type,
            t.assess_control,
            t.version_no,
            t.expired_status,
            t.indicator_property,
            t.remark,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time,
            t.del_flag
        from biz_indicator_main t
        where t.id in
              (select distinct t.id
        from biz_indicator_main t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="bizIndicatorMain" resultMap="bizIndicatorMainResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="indicatorCode != null  and indicatorCode != ''">
                and t.indicator_code LIKE concat('%', #{indicatorCode}, '%')
            </if>
            <if test="indicatorName != null  and indicatorName != ''">
                and t.indicator_name LIKE concat('%', #{indicatorName}, '%')
            </if>
            <if test="indicatorType != null  and indicatorType != ''">
                and t.indicator_type = #{indicatorType}
            </if>
            <if test="ownerDepartment != null  and ownerDepartment != ''">
                and t.owner_department = #{ownerDepartment}
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="responsibleId != null ">
                and t.responsible_id = #{responsibleId}
            </if>
            <if test="assessmentType != null  and assessmentType != ''">
                and t.assessment_type = #{assessmentType}
            </if>
            <if test="assessControl != null  and assessControl != ''">
                and t.assess_control = #{assessControl}
            </if>
            <if test="indicatorProperty != null  and indicatorProperty != ''">
                and t.indicator_property = #{indicatorProperty}
            </if>
            <if test="levelCategory != null  and levelCategory != ''">
                and t.level_category = #{levelCategory}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

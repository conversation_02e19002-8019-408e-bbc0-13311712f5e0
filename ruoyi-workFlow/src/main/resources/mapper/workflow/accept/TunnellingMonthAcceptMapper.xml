<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.accept.mapper.TunnellingMonthAcceptMapper">
    <resultMap type="TunnellingMonthAccept" id="TunnellingMonthAcceptResult">
        <result property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="constructionUnitId" column="construction_unit_id"/>
        <result property="accountYear" column="account_year"/>
        <result property="accountMonth" column="account_month"/>
        <result property="submitStatusValue" column="submit_status_value"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.plan_id,
                        t.dept_id,
                        t.construction_unit_id,
                        t.account_year,
                        t.account_month,
                        t.submit_status_value,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag
        from biz_tunnelling_month_accept t
        where t.id in (select distinct t.id
                       from biz_tunnelling_month_accept t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="TunnellingMonthAccept" resultMap="TunnellingMonthAcceptResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="planId != null ">
                and t.plan_id = #{planId}
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="constructionUnitId != null ">
                and t.construction_unit_id = #{constructionUnitId}
            </if>
            <if test="accountYear != null ">
                and t.account_year = #{accountYear}
            </if>
            <if test="accountMonth != null ">
                and t.account_month = #{accountMonth}
            </if>
            <if test="submitStatusValue != null and submitStatusValue != ''">
                and t.submit_status_value = #{submitStatusValue}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

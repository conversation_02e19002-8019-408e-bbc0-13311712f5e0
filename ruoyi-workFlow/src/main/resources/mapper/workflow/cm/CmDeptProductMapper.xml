<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmDept.mapper.CmDeptProductMapper">
    <resultMap type="CmDeptProduct" id="CmDeptProductResult">
            <result property="deptId" column="dept_id"/>
            <result property="productId" column="product_id"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.dept_id,
            t.product_id
        from cm_dept_product t
        where t.dept_id in
              (select distinct t.dept_id
        from cm_dept_product t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.dept_id
        order by t.dept_id desc
        )
        group by t.dept_id
        order by t.dept_id desc
    </sql>
    <select id="list" parameterType="CmDeptProduct" resultMap="CmDeptProductResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.dept_id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="productId != null ">
                            and t.product_id = #{productId}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

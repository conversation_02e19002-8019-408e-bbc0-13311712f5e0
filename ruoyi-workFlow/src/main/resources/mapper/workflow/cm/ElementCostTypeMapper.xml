<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmElementCostType.mapper.ElementCostTypeMapper">
    <resultMap type="ElementCostType" id="CmElementCostTypeResult">
            <result property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="parentId" column="parent_id"/>
            <result property="ancestors" column="ancestors"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.code,
            t.name,
            t.full_name,
            t.parent_id,
            t.ancestors,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from cm_element_cost_type t
        where t.id in
              (select distinct t.id
        from cm_element_cost_type t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="ElementCostType" resultMap="CmElementCostTypeResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="code != null  and code != ''">
                            and t.code = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="fullName != null  and fullName != ''">
                            and t.full_name LIKE concat('%', #{fullName}, '%')
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
    <select id="recursiveCteByTypeId" resultType="java.lang.Long">
        WITH RECURSIVE cte AS (
            SELECT t1.id, t1.parent_id, t1.name, 0 AS level
            FROM cm_element_cost_type t1
            WHERE id = #{id}
            UNION ALL
            SELECT t2.id, t2.parent_id, t2.name, (cte.level+1) AS level
            FROM cm_element_cost_type t2
            JOIN cte ON t2.parent_id = cte.id
        )
        SELECT id FROM cte;
    </select>
</mapper>

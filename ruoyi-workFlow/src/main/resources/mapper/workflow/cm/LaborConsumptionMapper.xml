<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.LaborConsumption.mapper.LaborConsumptionMapper">
    <resultMap type="LaborConsumption" id="LaborConsumptionResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
            <result property="accountPeriodName" column="account_period_name"/>
            <result property="offerDeptId" column="offer_dept_id"/>
            <result property="laborId" column="labor_id"/>
            <result property="benefitDeptId" column="benefit_dept_id"/>
            <result property="measureUnit" column="measure_unit"/>
            <result property="qty" column="qty"/>
            <result property="billUserId" column="bill_user_id"/>
            <result property="auditUserId" column="audit_user_id"/>
            <result property="status" column="status"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.account_period_id,
            concat(ap.account_year,'年',ap.account_month,'月') as account_period_name,
            t.offer_dept_id,
            t.labor_id,
            t.benefit_dept_id,
            t.measure_unit,
            t.qty,
            t.bill_user_id,
            t.audit_user_id,
            t.status,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from cm_labor_consumption t
        left join biz_account_period ap on (t.account_period_id) = (ap.id)
        where t.id in
              (select distinct t.id
        from cm_labor_consumption t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join cm_dept c on (t.offer_dept_id) = (c.id)
        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="LaborConsumption" resultMap="LaborConsumptionResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="accountPeriodId != null ">
                            and t.account_period_id = #{accountPeriodId}
                        </if>
                        <if test="offerDeptId != null ">
                            and t.offer_dept_id = #{offerDeptId}
                        </if>
                        <if test="laborId != null ">
                            and t.labor_id = #{laborId}
                        </if>
                        <if test="benefitDeptId != null ">
                            and t.benefit_dept_id = #{benefitDeptId}
                        </if>
                        <if test="measureUnit != null  and measureUnit != ''">
                            and t.measure_unit LIKE concat('%', #{measureUnit}, '%')
                        </if>
                        <if test="qty != null ">
                            and t.qty = #{qty}
                        </if>
                        <if test="billUserId != null ">
                            and t.bill_user_id = #{billUserId}
                        </if>
                        <if test="auditUserId != null ">
                            and t.audit_user_id = #{auditUserId}
                        </if>
                        <if test="status != null  and status != ''">
                            and t.status = #{status}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

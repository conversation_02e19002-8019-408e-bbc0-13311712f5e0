<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.payRatio.mapper.PayRatioMapper">
    <resultMap type="com.ruoyi.cm.payRatio.domain.PayRatio" id="PayRatioResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
            <result property="benefit" column="benefit"/>
            <result property="workerUnion" column="worker_union"/>
            <result property="workerEducation" column="worker_education"/>
            <result property="pensionInsurancePerson" column="pension_insurance_person"/>
            <result property="pensionInsuranceCorp" column="pension_insurance_corp"/>
            <result property="unemploymentInsurancePerson" column="unemployment_insurance_person"/>
            <result property="unemploymentInsuranceCorp" column="unemployment_insurance_corp"/>
            <result property="injuryInsuranceCorp" column="injury_insurance_corp"/>
            <result property="medicalInsurancePerson" column="medical_insurance_person"/>
            <result property="medicalInsuranceCorp" column="medical_insurance_corp"/>
            <result property="housingProvidentFundPerson" column="housing_provident_fund_person"/>
            <result property="housingProvidentFundCorp" column="housing_provident_fund_corp"/>
            <result property="enterpriseAnnuityPerson" column="enterprise_annuity_person"/>
            <result property="enterpriseAnnuityCorp" column="enterprise_annuity_corp"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.account_period_id,
                        concat(ap.account_year, '年', ap.account_month, '月') as account_period_name,
                        t.benefit,
                        t.worker_union,
                        t.worker_education,
                        t.pension_insurance_person,
                        t.pension_insurance_corp,
                        t.unemployment_insurance_person,
                        t.unemployment_insurance_corp,
                        t.injury_insurance_corp,
                        t.medical_insurance_person,
                        t.medical_insurance_corp,
                        t.housing_provident_fund_person,
                        t.housing_provident_fund_corp,
                        t.enterprise_annuity_person,
                        t.enterprise_annuity_corp,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        from cm_pay_ratio t
                 left join biz_account_period ap on (t.account_period_id) = (ap.id)
        where t.id in
              (select distinct t.id
               from cm_pay_ratio t
                        left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.cm.payRatio.domain.PayRatio" resultMap="PayRatioResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="accountPeriodId != null ">
                            and t.account_period_id = #{accountPeriodId}
                        </if>
                        <if test="benefit != null ">
                            and t.benefit = #{benefit}
                        </if>
                        <if test="workerUnion != null ">
                            and t.worker_union = #{workerUnion}
                        </if>
                        <if test="workerEducation != null ">
                            and t.worker_education = #{workerEducation}
                        </if>
                        <if test="pensionInsurancePerson != null ">
                            and t.pension_insurance_person = #{pensionInsurancePerson}
                        </if>
                        <if test="pensionInsuranceCorp != null ">
                            and t.pension_insurance_corp = #{pensionInsuranceCorp}
                        </if>
                        <if test="unemploymentInsurancePerson != null ">
                            and t.unemployment_insurance_person = #{unemploymentInsurancePerson}
                        </if>
                        <if test="unemploymentInsuranceCorp != null ">
                            and t.unemployment_insurance_corp = #{unemploymentInsuranceCorp}
                        </if>
                        <if test="injuryInsuranceCorp != null ">
                            and t.injury_insurance_corp = #{injuryInsuranceCorp}
                        </if>
                        <if test="medicalInsurancePerson != null ">
                            and t.medical_insurance_person = #{medicalInsurancePerson}
                        </if>
                        <if test="medicalInsuranceCorp != null ">
                            and t.medical_insurance_corp = #{medicalInsuranceCorp}
                        </if>
                        <if test="housingProvidentFundPerson != null ">
                            and t.housing_provident_fund_person = #{housingProvidentFundPerson}
                        </if>
                        <if test="housingProvidentFundCorp != null ">
                            and t.housing_provident_fund_corp = #{housingProvidentFundCorp}
                        </if>
                        <if test="enterpriseAnnuityPerson != null ">
                            and t.enterprise_annuity_person = #{enterpriseAnnuityPerson}
                        </if>
                        <if test="enterpriseAnnuityCorp != null ">
                            and t.enterprise_annuity_corp = #{enterpriseAnnuityCorp}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

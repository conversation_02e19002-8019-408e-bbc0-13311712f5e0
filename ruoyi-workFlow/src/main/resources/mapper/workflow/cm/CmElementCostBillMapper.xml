<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.CmElementCostBill.mapper.CmElementCostBillMapper">
    <resultMap type="CmElementCostBill" id="CmElementCostBillResult">
        <result property="id" column="id"/>
        <result property="accountPeriodId" column="account_period_id"/>
        <result property="accountPeriodName" column="account_period_name"/>
        <result property="billCode" column="bill_code"/>
        <result property="prepareDeptId" column="prepare_dept_id"/>
        <result property="receiveDeptId" column="receive_dept_id"/>
        <result property="dataSource" column="data_source"/>
        <result property="elementCostId" column="element_cost_id"/>
        <result property="costProjectId" column="cost_project_id"/>
        <result property="rollOutFlag" column="roll_out_flag"/>
        <result property="totalAmount" column="total_amount"/>
        <result property="rawAmount" column="raw_amount"/>
        <result property="rollOutAmount" column="roll_out_amount"/>
        <result property="finalAmount" column="final_amount"/>
        <result property="assignWay" column="assign_way"/>
        <result property="ccdStandardId" column="ccd_standard_id"/>
        <result property="auditStatus" column="audit_status"/>
        <result property="confirmStatus" column="confirm_status"/>
        <result property="auditUserId" column="audit_user_id"/>
        <result property="confirmUserId" column="confirm_user_id"/>
        <result property="prepareUserId" column="prepare_user_id"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
        t.id,
        t.account_period_id,
        concat(ap.account_year,'年',ap.account_month,'月') as account_period_name,
        t.bill_code,
        t.prepare_dept_id,
        t.receive_dept_id,
        t.data_source,
        t.element_cost_id,
        t.cost_project_id,
        t.roll_out_flag,
        t.total_amount,
        t.raw_amount,
        t.roll_out_amount,
        t.final_amount,
        t.assign_way,
        t.ccd_standard_id,
        t.audit_status,
        t.confirm_status,
        t.audit_user_id,
        t.confirm_user_id,
        t.prepare_user_id,
        t.remark,
        t.del_flag,
        t.create_by_id,
        t.create_by,
        t.create_time,
        t.update_by_id,
        t.update_by,
        t.update_time
        from cm_element_cost_bill t
        left join biz_account_period ap on (t.account_period_id) = (ap.id)
        where t.id in
        (select distinct t.id
        from cm_element_cost_bill t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join cm_dept c on ((t.prepare_dept_id) = (c.id) or (t.receive_dept_id) = (c.id))
        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
        <!--  left join sys_dept d on (d.dept_id)=(t.prepare_dept_id)-->
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="CmElementCostBill" resultMap="CmElementCostBillResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="accountPeriodId != null ">
                and t.account_period_id = #{accountPeriodId}
            </if>
            <if test="billCode != null  and billCode != ''">
                and t.bill_code like concat('%', #{billCode} ,'%')
            </if>
            <if test="prepareDeptId != null ">
                and t.prepare_dept_id = #{prepareDeptId}
            </if>
            <if test="receiveDeptId != null ">
                and t.receive_dept_id = #{receiveDeptId}
            </if>
            <if test="dataSource != null  and dataSource != ''">
                and t.data_source = #{dataSource}
            </if>
            <if test="elementCostId != null ">
                and t.element_cost_id = #{elementCostId}
            </if>
            <if test="costProjectId != null ">
                and t.cost_project_id = #{costProjectId}
            </if>
            <if test="rollOutFlag != null  and rollOutFlag != ''">
                and t.roll_out_flag = #{rollOutFlag}
            </if>
            <if test="totalAmount != null ">
                and t.total_amount = #{totalAmount}
            </if>
            <if test="rawAmount != null ">
                and t.raw_amount = #{rawAmount}
            </if>
            <if test="rollOutAmount != null ">
                and t.roll_out_amount = #{rollOutAmount}
            </if>
            <if test="finalAmount != null ">
                and t.final_amount = #{finalAmount}
            </if>
            <if test="assignWay != null  and assignWay != ''">
                and t.assign_way = #{assignWay}
            </if>
            <if test="auditStatus != null  and auditStatus != ''">
                and t.audit_status = #{auditStatus}
            </if>
            <if test="filteredAssignWay !=null and filteredAssignWay.length >0">
                and t.assign_way not in
                <foreach collection="filteredAssignWay" open="(" close=")" item="item" index="i" separator=",">
                    #{filteredAssignWay.[${i}]}
                </foreach>
            </if>
            <if test="confirmStatus != null  and confirmStatus != ''">
                and t.confirm_status = #{confirmStatus}
            </if>
            <if test="auditUserId != null ">
                and t.audit_user_id = #{auditUserId}
            </if>
            <if test="confirmUserId != null ">
                and t.confirm_user_id = #{confirmUserId}
            </if>
            <if test="prepareUserId != null ">
                and t.prepare_user_id = #{prepareUserId}
            </if>
        </where>
        <include refid="groupOrder"/>

    </select>
    <select id="getTableBottomSum" resultType="com.ruoyi.cm.CmElementCostBill.domain.CmElementCostBill">
        select
        sum(t.raw_amount) as raw_amount,
        sum(t.roll_out_amount) as roll_out_amount,
        sum(t.final_amount) as final_amount
        from cm_element_cost_bill t
        where t.id in
        (select distinct t.id
        from cm_element_cost_bill t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join cm_dept c on (t.prepare_dept_id) = (c.id)
        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="accountPeriodId != null ">
                and t.account_period_id = #{accountPeriodId}
            </if>
            <if test="billCode != null  and billCode != ''">
                and t.bill_code like concat('%', #{billCode} ,'%')
            </if>
            <if test="prepareDeptId != null ">
                and t.prepare_dept_id = #{prepareDeptId}
            </if>
            <if test="receiveDeptId != null ">
                and t.receive_dept_id = #{receiveDeptId}
            </if>
            <if test="dataSource != null  and dataSource != ''">
                and t.data_source = #{dataSource}
            </if>
            <if test="elementCostId != null ">
                and t.element_cost_id = #{elementCostId}
            </if>
            <if test="costProjectId != null ">
                and t.cost_project_id = #{costProjectId}
            </if>
            <if test="rollOutFlag != null  and rollOutFlag != ''">
                and t.roll_out_flag = #{rollOutFlag}
            </if>
            <if test="totalAmount != null ">
                and t.total_amount = #{totalAmount}
            </if>
            <if test="rawAmount != null ">
                and t.raw_amount = #{rawAmount}
            </if>
            <if test="rollOutAmount != null ">
                and t.roll_out_amount = #{rollOutAmount}
            </if>
            <if test="finalAmount != null ">
                and t.final_amount = #{finalAmount}
            </if>
            <if test="assignWay != null  and assignWay != ''">
                and t.assign_way = #{assignWay}
            </if>
            <if test="auditStatus != null  and auditStatus != ''">
                and t.audit_status = #{auditStatus}
            </if>
            <if test="confirmStatus != null  and confirmStatus != ''">
                and t.confirm_status = #{confirmStatus}
            </if>
            <if test="auditUserId != null ">
                and t.audit_user_id = #{auditUserId}
            </if>
            <if test="confirmUserId != null ">
                and t.confirm_user_id = #{confirmUserId}
            </if>
            <if test="prepareUserId != null ">
                and t.prepare_user_id = #{prepareUserId}
            </if>
        </where>
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
    </select>
    <select id="selectStatisticsForUnitPrice" resultType="com.ruoyi.cm.CmElementCostBill.domain.CmElementCostBill">
        SELECT t.receive_dept_id,
        d.dept_name as receive_dept_name,
        sub.assign_objects as sub_assign_objects,
        sum(sub.amount) as statistics_amount
        FROM cm_element_cost_bill t
        LEFT JOIN cm_dept d ON t.receive_dept_id = d.id
        left join cm_element_cost_bill_sub sub on t.id = sub.parent_id
        <where>
            d.type = '1'
            and t.account_period_id = #{accountPeriodId}
            and t.audit_status = '1'
            and sub.type = 'L'
        </where>
        group by t.receive_dept_id,d.dept_name, sub.assign_objects
        order by t.receive_dept_id, sub.assign_objects;
    </select>
</mapper>

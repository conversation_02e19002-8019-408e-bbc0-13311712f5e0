<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.electricityDetails.mapper.CmElectricityDetailsMapper">
    <resultMap type="CmElectricityDetails" id="CmElectricityDetailsResult">
        <result property="id" column="id"/>
        <result property="deptName" column="dept_name"/>
        <result property="projectName" column="project_name"/>
        <result property="electricityConsumption" column="electricity_consumption"/>
        <result property="unitPrice" column="unit_price"/>
        <result property="amount" column="amount"/>
        <result property="accountPeriodId" column="account_period_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectFrom">
        select distinct
            t.id,
            t.dept_name,
            t.project_name,
            t.electricity_consumption,
            t.unit_price,
            t.amount,
            t.account_period_id,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.remark
        from cm_electricity_details t
        where t.id in
              (select distinct t.id
               from cm_electricity_details t
                        left join sys_user u on t.create_by = u.user_name and t.create_by_id = u.user_id
    </sql>

    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.create_time desc, t.id asc
        )
        group by t.id
        order by t.create_time desc, t.id asc
    </sql>

    <select id="list" parameterType="CmElectricityDetails" resultMap="CmElectricityDetailsResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptName != null  and deptName != ''">
                and t.dept_name LIKE concat('%', #{deptName}, '%')
            </if>
            <if test="projectName != null  and projectName != ''">
                and t.project_name LIKE concat('%', #{projectName}, '%')
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmCostProject.mapper.CostProjectMapper">
    <resultMap type="com.ruoyi.cm.cmCostProject.domain.CostProject" id="CostProjectResult">
            <result property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="parentId" column="parent_id"/>
            <result property="ancestors" column="ancestors"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="standardCostProject" column="standard_cost_project"/>
            <result property="systemPreset" column="system_preset"/>
            <result property="orderNum" column="order_num"/>
            <result property="status" column="status"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.code,
            t.name,
            t.full_name,
            t.parent_id,
            t.ancestors,
            t.order_num,
            t.status,
            t.standard_cost_project,
            t.system_preset,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from cm_cost_project t
        where t.id in
              (select distinct t.id
        from cm_cost_project t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.parent_id,t.order_num,t.id
    </sql>
    <select id="list" parameterType="com.ruoyi.cm.cmCostProject.domain.CostProject" resultMap="CostProjectResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = "0"
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="code != null  and code != ''">
                            and t.code LIKE concat('%', #{code}, '%')
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="fullName != null  and fullName != ''">
                            and t.full_name LIKE concat('%', #{fullName}, '%')
                        </if>
                        <if test="standardCostProject != null  and standardCostProject != ''">
                            and t.standard_cost_project = #{standardCostProject}
                        </if>
                        <if test="systemPreset != null  and systemPreset != ''">
                            and t.system_preset = #{systemPreset}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>

    <select id="listChildrenById" parameterType="Long" resultMap="CostProjectResult">
        <include refid="selectFrom"/>
        <where>
            find_in_set(#{id}, ancestors)
        </where>
        <include refid="groupOrder"/>
    </select>


    <update id="updateChildren" parameterType="java.util.List">
        update cm_cost_project set ancestors =
        <foreach collection="depts" item="item" index="index" separator=" " open="case id" close="end">
            when #{item.id} then #{item.ancestors}
        </foreach>
        where id in
        <foreach collection="depts" item="item" index="index" separator="," open="(" close=")">
            #{item.id}
        </foreach>
    </update>

    <select id="countNormalChildrenById" parameterType="Long" resultType="int">
        select count(*)
        from cm_cost_project
        where status = 0
          and del_flag = '0'
          and find_in_set(#{projectId}, ancestors)
    </select>
</mapper>

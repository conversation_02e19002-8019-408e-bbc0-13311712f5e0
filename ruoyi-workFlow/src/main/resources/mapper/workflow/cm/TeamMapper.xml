<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmTeam.mapper.TeamMapper">
    <resultMap type="com.ruoyi.cm.cmTeam.domain.Team" id="TeamResult">
            <result property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="equipmentId" column="equipment_id"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="deptId" column="dept_id"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.code,
            t.equipment_id,
            t.name,
            t.full_name,
            t.dept_id,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from cm_team t
        where t.id in
              (select distinct t.id
        from cm_team t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join cm_dept c on (t.dept_id) = (c.id)
        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="Team" resultMap="TeamResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = "0"
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="code != null  and code != ''">
                            and t.code = #{code}
                        </if>
                        <if test="equipmentId != null  and equipmentId != ''">
                            and t.equipment_id = #{equipmentId}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="fullName != null  and fullName != ''">
                            and t.full_name LIKE concat('%', #{fullName}, '%')
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
    <select id="findAllEquipmentIds" resultType="String">
        select equipment_id from cm_team
        where equipment_id
    </select>
    <select id="findCurrentTeamEquipmentIds" resultType="String">
        SELECT equipment_id
        FROM cm_team
        WHERE id = #{teamId}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.CmElementCostBill.mapper.CmRollOutDetailMapper">
    <resultMap type="CmRollOutDetail" id="CmRollOutDetailResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="type" column="type"/>
            <result property="amount" column="amount"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.type,
            t.amount,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from cm_roll_out_detail t
        where t.id in
              (select distinct t.id
        from cm_roll_out_detail t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        <!--  left join sys_dept d on (t.dept_id)=(t.dept_id) -->
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="CmRollOutDetail" resultMap="CmRollOutDetailResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="type != null  and type != ''">
                            and t.type = #{type}
                        </if>
                        <if test="amount != null ">
                            and t.amount = #{amount}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

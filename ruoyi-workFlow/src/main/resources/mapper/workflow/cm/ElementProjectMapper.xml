<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmElementProject.mapper.ElementProjectMapper">
    <resultMap type="ElementProject" id="ElementProjectResult">
            <result property="elementCostId" column="element_cost_id"/>
            <result property="costProjectId" column="cost_project_id"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.element_cost_id,
            t.cost_project_id
        from cm_element_project t
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.element_cost_id
        order by t.element_cost_id desc
        )
        group by t.element_cost_id
        order by t.element_cost_id desc
    </sql>
    <insert id="newSave" parameterType="ElementProject">
        INSERT INTO cm_element_project (element_cost_id, cost_project_id) VALUES (#{elementCostId}, #{costProjectId})
    </insert>
    <select id="list" parameterType="ElementProject" resultMap="ElementProjectResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.element_cost_id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="elementCostId != null ">
                            and t.element_cost_id = #{elementCostId}
                        </if>
                        <if test="costProjectId != null ">
                            and t.cost_project_id = #{costProjectId}
                        </if>
        </where>
        order by t.element_cost_id desc
    </select>
</mapper>

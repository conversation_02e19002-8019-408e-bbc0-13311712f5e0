<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmElementCost.mapper.ElementCostMapper">
    <resultMap type="ElementCost" id="ElementCostResult">
            <result property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="elementCostTypeId" column="element_cost_type_id"/>
            <result property="systemPreset" column="system_preset"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
            <result property="parentId" column="parent_id"/>
            <result property="ancestors" column="ancestors"/>
            <result property="orderNum" column="order_num"/>
            <result property="status" column="status"/>
            <result property="costProjectId" column="cost_project_id"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.code,
            t.name,
            t.full_name,
            t.element_cost_type_id,
            t.system_preset,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark,
            t.parent_id,
            t.ancestors,
            t.order_num,
            t.status,
            c.cost_project_id AS cost_project_id
        from cm_element_cost t
        left join cm_element_project c on c.element_cost_id = t.id
        where t.id in
              (select distinct t.id
        from cm_element_cost t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        ##group by t.id
        order by t.order_num,t.id desc
    </sql>
    <select id="list" parameterType="ElementCost" resultMap="ElementCostResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="code != null  and code != ''">
                            and t.code = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="fullName != null  and fullName != ''">
                            and t.full_name LIKE concat('%', #{fullName}, '%')
                        </if>
                        <if test="elementCostTypeId != null">
                            and (t.element_cost_type_id = #{elementCostTypeId}
                            or t.element_cost_type_id in ( SELECT m.id FROM cm_element_cost_type m WHERE find_in_set(#{elementCostTypeId},ancestors)))
                        </if>
                        <if test="systemPreset != null  and systemPreset != ''">
                            and t.system_preset = #{systemPreset}
                        </if>
                        <if test="parentId != null">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="ancestors != null  and ancestors != ''">
                            and t.ancestors = #{ancestors}
                        </if>
                        <if test="orderNum != null">
                            and t.order_num = #{orderNum}
                        </if>
                        <if test="status != null  and status != ''">
                            and t.status = #{status}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

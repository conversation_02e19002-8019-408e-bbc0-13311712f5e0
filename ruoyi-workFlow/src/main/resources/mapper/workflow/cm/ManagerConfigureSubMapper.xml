<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.ManagerConfigure.mapper.ManagerConfigureSubMapper">
    <resultMap type="ManagerConfigureSub" id="ManagerConfigureSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.code,
            t.name,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_time,
            t.remark
        from cm_manager_configure_sub t
        where t.id in
              (select distinct t.id
        from cm_manager_configure_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="ManagerConfigureSub" resultMap="ManagerConfigureSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="code != null  and code != ''">
                            and t.code = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

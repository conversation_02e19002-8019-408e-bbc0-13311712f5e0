<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.CcdStandard.mapper.CcdStandardMapper">
    <resultMap type="CcdStandard" id="CcdStandardResult">
            <result property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result property="fullName" column="full_name"/>
            <result property="type" column="type"/>
            <result property="calculateMethod" column="calculate_method"/>
            <result property="status" column="status"/>
            <result property="systemPreset" column="system_preset"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.code,
            t.name,
            t.full_name,
            t.type,
            t.calculate_method,
            t.status,
            t.system_preset,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from cm_ccd_standard t
        where t.id in
              (select distinct t.id
        from cm_ccd_standard t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="CcdStandard" resultMap="CcdStandardResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = "0"
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="code != null  and code != ''">
                            and t.code LIKE concat('%', #{code}, '%')
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="fullName != null  and fullName != ''">
                            and t.full_name LIKE concat('%', #{fullName}, '%')
                        </if>
                        <if test="type != null  and type != ''">
                            and t.type = #{type}
                        </if>
                        <if test="calculateMethod != null  and calculateMethod != ''">
                            and t.calculate_method = #{calculateMethod}
                        </if>
                        <if test="status != null  and status != ''">
                            and t.status = #{status}
                        </if>
                        <if test="systemPreset != null  and systemPreset != ''">
                            and t.system_preset = #{systemPreset}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

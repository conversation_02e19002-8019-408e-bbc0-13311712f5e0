<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmPay.mapper.PaySubMapper">
    <resultMap type="PaySub" id="PaySubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="code" column="code"/>
            <result property="name" column="name"/>
            <result property="deptId" column="dept_id"/>
            <result property="post" column="post"/>
            <result property="tradesId" column="trades_id"/>
            <result property="flag" column="flag"/>
            <result property="basePay" column="base_pay"/>
            <result property="meritPay" column="merit_pay"/>
            <result property="safetyPay" column="safety_pay"/>
            <result property="reward" column="reward"/>
            <result property="seniorityAllOwance" column="seniority_all_owance"/>
            <result property="nightShiftSubsidy" column="night_shift_subsidy"/>
            <result property="descentSubsidy" column="descent_subsidy"/>
            <result property="expertiseSubsidy" column="expertise_subsidy"/>
            <result property="degreeSubsidy" column="degree_subsidy"/>
            <result property="trafficSubsidy" column="traffic_subsidy"/>
            <result property="overtimePay" column="overtime_pay"/>
            <result property="bucklePay" column="buckle_pay"/>
            <result property="grossSalary" column="gross_salary"/>
            <result property="rentFee" column="rent_fee"/>
            <result property="otherWithhold" column="other_withhold"/>
            <result property="pension" column="pension"/>
            <result property="medicare" column="medicare"/>
            <result property="housingProvidentFund" column="housing_provident_fund"/>
            <result property="unemployBenefit" column="unemploy_benefit"/>
            <result property="enterpriseAnnuity" column="enterprise_annuity"/>
            <result property="tax" column="tax"/>
            <result property="actualWage" column="actual_wage"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.code,
            t.name,
            t.dept_id,
            t.post,
            t.trades_id,
            t.flag,
            t.base_pay,
            t.merit_pay,
            t.safety_pay,
            t.reward,
            t.seniority_all_owance,
            t.night_shift_subsidy,
            t.descent_subsidy,
            t.expertise_subsidy,
            t.degree_subsidy,
            t.traffic_subsidy,
            t.overtime_pay,
            t.buckle_pay,
            t.gross_salary,
            t.rent_fee,
            t.other_withhold,
            t.pension,
            t.medicare,
            t.housing_provident_fund,
            t.unemploy_benefit,
            t.enterprise_annuity,
            t.tax,
            t.actual_wage,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from cm_pay_sub t
        where t.id in
              (select distinct t.id
        from cm_pay_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="PaySub" resultMap="PaySubResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="code != null  and code != ''">
                            and t.code = #{code}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="post != null  and post != ''">
                            and t.post = #{post}
                        </if>
                        <if test="tradesId != null  and tradesId != ''">
                            and t.trades_id = #{tradesId}
                        </if>
                        <if test="flag != null  and flag != ''">
                            and t.flag = #{flag}
                        </if>
                        <if test="basePay != null ">
                            and t.base_pay = #{basePay}
                        </if>
                        <if test="meritPay != null ">
                            and t.merit_pay = #{meritPay}
                        </if>
                        <if test="safetyPay != null ">
                            and t.safety_pay = #{safetyPay}
                        </if>
                        <if test="reward != null ">
                            and t.reward = #{reward}
                        </if>
                        <if test="seniorityAllOwance != null ">
                            and t.seniority_all_owance = #{seniorityAllOwance}
                        </if>
                        <if test="nightShiftSubsidy != null ">
                            and t.night_shift_subsidy = #{nightShiftSubsidy}
                        </if>
                        <if test="descentSubsidy != null ">
                            and t.descent_subsidy = #{descentSubsidy}
                        </if>
                        <if test="expertiseSubsidy != null ">
                            and t.expertise_subsidy = #{expertiseSubsidy}
                        </if>
                        <if test="degreeSubsidy != null ">
                            and t.degree_subsidy = #{degreeSubsidy}
                        </if>
                        <if test="trafficSubsidy != null ">
                            and t.traffic_subsidy = #{trafficSubsidy}
                        </if>
                        <if test="overtimePay != null ">
                            and t.overtime_pay = #{overtimePay}
                        </if>
                        <if test="bucklePay != null ">
                            and t.buckle_pay = #{bucklePay}
                        </if>
                        <if test="grossSalary != null ">
                            and t.gross_salary = #{grossSalary}
                        </if>
                        <if test="rentFee != null ">
                            and t.rent_fee = #{rentFee}
                        </if>
                        <if test="otherWithhold != null ">
                            and t.other_withhold = #{otherWithhold}
                        </if>
                        <if test="pension != null ">
                            and t.pension = #{pension}
                        </if>
                        <if test="medicare != null ">
                            and t.medicare = #{medicare}
                        </if>
                        <if test="housingProvidentFund != null ">
                            and t.housing_provident_fund = #{housingProvidentFund}
                        </if>
                        <if test="unemployBenefit != null ">
                            and t.unemploy_benefit = #{unemployBenefit}
                        </if>
                        <if test="enterpriseAnnuity != null ">
                            and t.enterprise_annuity = #{enterpriseAnnuity}
                        </if>
                        <if test="tax != null ">
                            and t.tax = #{tax}
                        </if>
                        <if test="actualWage != null ">
                            and t.actual_wage = #{actualWage}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

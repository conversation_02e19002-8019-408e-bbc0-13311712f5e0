<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.Trades.mapper.TradesMapper">
    <resultMap type="com.ruoyi.cm.Trades.domain.Trades" id="TradesResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="deptId" column="dept_id"/>
        <result property="financeItemId" column="finance_item_id"/>
        <result property="laborId" column="labor_id"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="costProjectId" column="cost_project_id"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="standardCostProject" column="standard_cost_project"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.code,
                        t.name,
                        t.dept_id,
                        t.finance_item_id,
                        t.labor_id,
                        t.remark,
                        t.del_flag,
                        t.create_by_id,
                        t.standard_cost_project,
                        t.cost_project_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time
        from cm_trades t
        where t.id in
              (select distinct t.id
               from cm_trades t
                        left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
                        left join cm_dept c on (t.dept_id) = (c.id)
                        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.cm.Trades.domain.Trades" resultMap="TradesResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="id != null ">
                and t.id = #{id}
            </if>
            <if test="code != null  and code != ''">
                and t.code LIKE concat('%', #{code}, '%')
            </if>
            <if test="name != null  and name != ''">
                and t.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="financeItemId != null ">
                and t.finance_item_id = #{financeItemId}
            </if>
            <if test="laborId != null ">
                and t.labor_id = #{laborId}
            </if>
            <if test="remark != null  and remark != ''">
                and t.remark = #{remark}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

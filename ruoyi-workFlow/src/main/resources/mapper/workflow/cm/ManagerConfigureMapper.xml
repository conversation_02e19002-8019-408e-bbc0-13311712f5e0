<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.ManagerConfigure.mapper.ManagerConfigureMapper">
    <resultMap type="ManagerConfigure" id="ManagerConfigureResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
            <result property="accountPeriodName" column="account_period_name"/>
            <result property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.account_period_id,
                        concat(ap.account_year, '年', ap.account_month, '月') as account_period_name,
                        t.dept_id,
                        cd.dept_name                                          as dept_name,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        from cm_manager_configure t
                 left join biz_account_period ap on (t.account_period_id) = (ap.id)
                 left join cm_dept cd on t.dept_id = cd.id
        where t.del_flag = 0
          and t.id in
              (select distinct t.id
               from cm_manager_configure t
                        left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
                        left join cm_dept c on (t.dept_id) = (c.id)
                        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="ManagerConfigure" resultMap="ManagerConfigureResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="accountPeriodId != null ">
                            and t.account_period_id = #{accountPeriodId}
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.dept.mapper.LaborElementCostMapper">
    <resultMap type="LaborElementCost" id="LaborElementCostResult">
        <result property="laborId" column="labor_id"/>
        <result property="elementCostId" column="element_cost_id"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.labor_id,
            t.element_cost_id
        from cm_labor_element_cost t
        where t.labor_id in
              (select distinct t.labor_id
               from cm_labor_element_cost t
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.labor_id
        order by t.labor_id desc
        )
        group by t.labor_id
        order by t.labor_id desc
    </sql>
    <select id="list" parameterType="LaborElementCost" resultMap="LaborElementCostResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.labor_id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="laborId != null ">
                and t.labor_id = #{laborId}
            </if>
            <if test="elementCostId != null ">
                and t.element_cost_id = #{elementCostId}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.WorkPercentageFill.mapper.WorkPercentageFillMapper">
    <resultMap type="com.ruoyi.cm.WorkPercentageFill.domain.WorkPercentageFill" id="WorkPercentageFillResult">
        <result property="id" column="id"/>
        <result property="accountPeriodId" column="account_period_id"/>
        <result property="accountPeriodName" column="account_period_name"/>
        <result property="num" column="num"/>
        <result property="deptId" column="dept_id"/>
        <result property="tradesId" column="trades_id"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="userName" column="user_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.account_period_id,
                        concat(ap.account_year,'年',ap.account_month,'月') as account_period_name,
                        t.num,
                        t.user_name,
                        t.dept_id,
                        t.trades_id,
                        t.remark,
                        t.del_flag,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time
        from cm_work_percentage_fill t
         left join biz_account_period ap on (t.account_period_id) = (ap.id)
        where t.id in
              (select distinct t.id
               from cm_work_percentage_fill t
                        left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
                        left join cm_dept c on (t.dept_id) = (c.id)
                        left join sys_dept d on (c.real_dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.cm.WorkPercentageFill.domain.WorkPercentageFill"
            resultMap="WorkPercentageFillResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="accountPeriodId != null ">
                and t.account_period_id = #{accountPeriodId}
            </if>
            <if test="num != null  and num != ''">
                and t.num = #{num}
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="tradesId != null ">
                and t.trades_id = #{tradesId}
            </if>
            <if test="remark != null  and remark != ''">
                and t.remark = #{remark}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.yieldDataFill.mapper.YieldDataFillMapper">
    <resultMap type="YieldDataFill" id="YieldDataFillResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
            <result property="deptId" column="dept_id"/>
            <result property="product" column="product"/>
            <result property="unit" column="unit"/>
            <result property="qty" column="qty"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.account_period_id,
            t.dept_id,
            t.product,
            t.unit,
            t.qty,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from cm_yield_data_fill t
        where t.id in
              (select distinct t.id
        from cm_yield_data_fill t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id)=(d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="YieldDataFill" resultMap="YieldDataFillResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="accountPeriodId != null ">
                            and t.account_period_id = #{accountPeriodId}
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="product != null ">
                            and t.product = #{product}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

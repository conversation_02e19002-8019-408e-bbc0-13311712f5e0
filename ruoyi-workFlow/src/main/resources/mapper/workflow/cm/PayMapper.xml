<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.cmPay.mapper.PayMapper">
    <resultMap type="Pay" id="PayResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
        <result property="accountPeriodName" column="account_period_name"/>
            <result property="auditDeptId" column="audit_dept_id"/>
            <result property="auditDeptName" column="audit_dept_name"/>
            <result property="deptId" column="dept_id"/>
            <result property="deptName" column="dept_name"/>
            <result property="auditUserId" column="audit_user_id"/>
            <result property="auditUserName" column="audit_user_name"/>
            <result property="status" column="status"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.account_period_id,
            concat(ap.account_year,'年',ap.account_month,'月') as account_period_name,
            t.audit_dept_id,
            sd1.dept_name as audit_dept_name,
            t.dept_id,
            cd.dept_name as dept_name,
            t.audit_user_id,
            u1.nick_name as audit_user_name,
            t.status,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from cm_pay t
        left join biz_account_period ap on (t.account_period_id) = (ap.id)
        left join sys_user u1 on t.audit_user_id = u1.user_id
        left join sys_dept sd1 on t.audit_dept_id = sd1.dept_id
        left join cm_dept cd on t.dept_id = cd.id
        left join sys_dept sd2 on cd.real_dept_id = sd2.dept_id
        where t.id in
              (select distinct t.id
        from cm_pay t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="Pay" resultMap="PayResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="accountPeriodId != null ">
                and t.account_period_id = #{accountPeriodId}
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="auditDeptId != null ">
                and t.audit_dept_id = #{auditDeptId}
            </if>
             <if test="status != null  and status != ''">
                and t.status = #{status}
             </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

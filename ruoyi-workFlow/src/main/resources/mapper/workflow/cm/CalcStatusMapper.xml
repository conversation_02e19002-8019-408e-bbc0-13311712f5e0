<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.cm.CmCalcStatus.mapper.CalcStatusMapper">
    <resultMap type="CalcStatus" id="CalcStatusResult">
        <result property="id" column="id"/>
        <result property="accountPeriodId" column="account_period_id"/>
        <result property="extractStatus" column="extract_status"/>
        <result property="dataCollectStatus" column="data_collect_status"/>
        <result property="auxiliaryShopStatus" column="auxiliary_shop_status"/>
        <result property="produceWorkshopStatus" column="produce_workshop_status"/>
        <result property="clacStatus" column="clac_status"/>
        <result property="currentProgress" column="current_progress"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.account_period_id,
                        t.extract_status,
                        t.data_collect_status,
                        t.auxiliary_shop_status,
                        t.produce_workshop_status,
                        t.clac_status,
                        t.current_progress,
                        t.remark,
                        t.del_flag,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time
        from cm_calc_status t
        where t.id in
              (select distinct t.id
               from cm_calc_status t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="CalcStatus" resultMap="CalcStatusResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            and t.account_period_id = #{accountPeriodId}
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

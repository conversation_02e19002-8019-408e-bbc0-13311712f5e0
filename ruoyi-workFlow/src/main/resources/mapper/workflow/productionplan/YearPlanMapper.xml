<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.yearPlan.mapper.YearPlanMapper">
    <resultMap type="com.ruoyi.productionplan.yearPlan.domain.YearPlan" id="YearPlanResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="accountYear" column="account_year"/>
        <result property="planTypeValue" column="plan_type_value"/>
        <result property="miningExcavationVolume" column="mining_excavation_volume"/>
        <result property="miningVolume" column="mining_volume"/>
        <result property="excavationVolume" column="excavation_volume"/>
        <result property="excavationMeters" column="excavation_meters"/>
        <result property="geologicalProspectingMeters" column="geological_prospecting_meters"/>
        <result property="productiveProspectingMeters" column="productive_prospecting_meters"/>
        <result property="developingTechnicalMeters" column="developing_technical_meters"/>
        <result property="pitDrillMeters" column="pit_drill_meters"/>
        <result property="surfaceDrillingMeters" column="surface_drilling_meters"/>
        <result property="totalOreOutput" column="total_ore_output"/>
        <result property="stopeOreOutput" column="stope_ore_output"/>
        <result property="byproductOreOutput" column="byproduct_ore_output"/>
        <result property="stopeTotalFillingCapacity" column="stope_total_filling_capacity"/>
        <result property="tailingsFillingCapacity" column="tailings_filling_capacity"/>
        <result property="wasteStoneFillingCapacity" column="waste_stone_filling_capacity"/>
        <result property="emptySpaceFillingCapacity" column="empty_space_filling_capacity"/>
        <result property="tailingsCapacity" column="tailings_capacity"/>
        <result property="tailingsDischargeCapacity" column="tailings_discharge_capacity"/>
        <result property="tailingsEmptySpaceFillingCapacity" column="tailings_empty_space_filling_capacity"/>
        <result property="oreProcessingCapacity" column="ore_processing_capacity"/>
        <result property="flotationProcessingCapacity" column="flotation_processing_capacity"/>
        <result property="directProcessingCapacity" column="direct_processing_capacity"/>
        <result property="gradeOfCrudeOre" column="grade_of_crude_ore"/>
        <result property="crudeOreMetallicity" column="crude_ore_metallicity"/>
        <result property="oreDressingRecoveryRate" column="ore_dressing_recovery_rate"/>
        <result property="concentrateGoldReturnRate" column="concentrate_gold_return_rate"/>
        <result property="goldProduction" column="gold_production"/>
        <result property="miningExcavationVolume2" column="mining_excavation_volume2"/>
        <result property="miningVolume2" column="mining_volume2"/>
        <result property="excavationVolume2" column="excavation_volume2"/>
        <result property="excavationMeters2" column="excavation_meters2"/>
        <result property="geologicalProspectingMeters2" column="geological_prospecting_meters2"/>
        <result property="productiveProspectingMeters2" column="productive_prospecting_meters2"/>
        <result property="developingTechnicalMeters2" column="developing_technical_meters2"/>
        <result property="pitDrillMeters2" column="pit_drill_meters2"/>
        <result property="surfaceDrillingMeters2" column="surface_drilling_meters2"/>
        <result property="totalOreOutput2" column="total_ore_output2"/>
        <result property="stopeOreOutput2" column="stope_ore_output2"/>
        <result property="byproductOreOutput2" column="byproduct_ore_output2"/>
        <result property="stopeTotalFillingCapacity2" column="stope_total_filling_capacity2"/>
        <result property="tailingsFillingCapacity2" column="tailings_filling_capacity2"/>
        <result property="wasteStoneFillingCapacity2" column="waste_stone_filling_capacity2"/>
        <result property="emptySpaceFillingCapacity2" column="empty_space_filling_capacity2"/>
        <result property="tailingsCapacity2" column="tailings_capacity2"/>
        <result property="tailingsDischargeCapacity2" column="tailings_discharge_capacity2"/>
        <result property="tailingsEmptySpaceFillingCapacity2" column="tailings_empty_space_filling_capacity2"/>
        <result property="oreProcessingCapacity2" column="ore_processing_capacity2"/>
        <result property="flotationProcessingCapacity2" column="flotation_processing_capacity2"/>
        <result property="directProcessingCapacity2" column="direct_processing_capacity2"/>
        <result property="gradeOfCrudeOre2" column="grade_of_crude_ore2"/>
        <result property="crudeOreMetallicity2" column="crude_ore_metallicity2"/>
        <result property="oreDressingRecoveryRate2" column="ore_dressing_recovery_rate2"/>
        <result property="concentrateGoldReturnRate2" column="concentrate_gold_return_rate2"/>
        <result property="goldProduction2" column="gold_production2"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectYearPlanVo">
        SELECT distinct t.id,
                        t.dept_id,
                        t.account_year,
                        t.plan_type_value,
                        t.mining_excavation_volume,
                        t.mining_volume,
                        t.excavation_volume,
                        t.excavation_meters,
                        t.geological_prospecting_meters,
                        t.productive_prospecting_meters,
                        t.developing_technical_meters,
                        t.pit_drill_meters,
                        t.surface_drilling_meters,
                        t.total_ore_output,
                        t.stope_ore_output,
                        t.byproduct_ore_output,
                        t.stope_total_filling_capacity,
                        t.tailings_filling_capacity,
                        t.waste_stone_filling_capacity,
                        t.empty_space_filling_capacity,
                        t.tailings_capacity,
                        t.tailings_discharge_capacity,
                        t.tailings_empty_space_filling_capacity,
                        t.ore_processing_capacity,
                        t.flotation_processing_capacity,
                        t.direct_processing_capacity,
                        t.grade_of_crude_ore,
                        t.crude_ore_metallicity,
                        t.ore_dressing_recovery_rate,
                        t.concentrate_gold_return_rate,
                        t.gold_production,
                        t.mining_excavation_volume2,
                        t.mining_volume2,
                        t.excavation_volume2,
                        t.excavation_meters2,
                        t.geological_prospecting_meters2,
                        t.productive_prospecting_meters2,
                        t.developing_technical_meters2,
                        t.pit_drill_meters2,
                        t.surface_drilling_meters2,
                        t.total_ore_output2,
                        t.stope_ore_output2,
                        t.byproduct_ore_output2,
                        t.stope_total_filling_capacity2,
                        t.tailings_filling_capacity2,
                        t.waste_stone_filling_capacity2,
                        t.empty_space_filling_capacity2,
                        t.tailings_capacity2,
                        t.tailings_discharge_capacity2,
                        t.tailings_empty_space_filling_capacity2,
                        t.ore_processing_capacity2,
                        t.flotation_processing_capacity2,
                        t.direct_processing_capacity2,
                        t.grade_of_crude_ore2,
                        t.crude_ore_metallicity2,
                        t.ore_dressing_recovery_rate2,
                        t.concentrate_gold_return_rate2,
                        t.gold_production2,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        u.nick_name as uName,
                        t.del_flag
        FROM biz_year_plan t
        left join sys_user u on t.create_by_id = u.user_id
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.yearPlan.domain.YearPlan" resultMap="YearPlanResult">
        <include refid="selectYearPlanVo"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="planTypeValue != null  and planTypeValue != ''">AND
                t.plan_type_value = #{planTypeValue}
            </if>
            <if test="accountYear != null  and accountYear != ''">AND
                t.account_year = #{accountYear}
            </if>
            <if test="miningExcavationVolume != null ">AND
                t.mining_excavation_volume = #{miningExcavationVolume}
            </if>
            <if test="miningVolume != null ">AND
                t.mining_volume = #{miningVolume}
            </if>
            <if test="excavationVolume != null ">AND
                t.excavation_volume = #{excavationVolume}
            </if>
            <if test="excavationMeters != null ">AND
                t.excavation_meters = #{excavationMeters}
            </if>
            <if test="geologicalProspectingMeters != null ">AND
                t.geological_prospecting_meters = #{geologicalProspectingMeters}
            </if>
            <if test="productiveProspectingMeters != null ">AND
                t.productive_prospecting_meters = #{productiveProspectingMeters}
            </if>
            <if test="developingTechnicalMeters != null ">AND
                t.developing_technical_meters = #{developingTechnicalMeters}
            </if>
            <if test="pitDrillMeters != null ">AND
                t.pit_drill_meters = #{pitDrillMeters}
            </if>
            <if test="surfaceDrillingMeters != null ">AND
                t.surface_drilling_meters = #{surfaceDrillingMeters}
            </if>
            <if test="totalOreOutput != null ">AND
                t.total_ore_output = #{totalOreOutput}
            </if>
            <if test="stopeOreOutput != null ">AND
                t.stope_ore_output = #{stopeOreOutput}
            </if>
            <if test="byproductOreOutput != null ">AND
                t.byproduct_ore_output = #{byproductOreOutput}
            </if>
            <if test="stopeTotalFillingCapacity != null ">AND
                t.stope_total_filling_capacity = #{stopeTotalFillingCapacity}
            </if>
            <if test="tailingsFillingCapacity != null ">AND
                t.tailings_filling_capacity = #{tailingsFillingCapacity}
            </if>
            <if test="wasteStoneFillingCapacity != null ">AND
                t.waste_stone_filling_capacity = #{wasteStoneFillingCapacity}
            </if>
            <if test="emptySpaceFillingCapacity != null ">AND
                t.empty_space_filling_capacity = #{emptySpaceFillingCapacity}
            </if>
            <if test="tailingsCapacity != null ">AND
                t.tailings_capacity = #{tailingsCapacity}
            </if>
            <if test="tailingsDischargeCapacity != null ">AND
                t.tailings_discharge_capacity = #{tailingsDischargeCapacity}
            </if>
            <if test="tailingsEmptySpaceFillingCapacity != null ">AND
                t.tailings_empty_space_filling_capacity = #{tailingsEmptySpaceFillingCapacity}
            </if>
            <if test="oreProcessingCapacity != null ">AND
                t.ore_processing_capacity = #{oreProcessingCapacity}
            </if>
            <if test="flotationProcessingCapacity != null ">AND
                t.flotation_processing_capacity = #{flotationProcessingCapacity}
            </if>
            <if test="directProcessingCapacity != null ">AND
                t.direct_processing_capacity = #{directProcessingCapacity}
            </if>
            <if test="gradeOfCrudeOre != null ">AND
                t.grade_of_crude_ore = #{gradeOfCrudeOre}
            </if>
            <if test="crudeOreMetallicity != null ">AND
                t.crude_ore_metallicity = #{crudeOreMetallicity}
            </if>
            <if test="oreDressingRecoveryRate != null ">AND
                t.ore_dressing_recovery_rate = #{oreDressingRecoveryRate}
            </if>
            <if test="concentrateGoldReturnRate != null ">AND
                t.concentrate_gold_return_rate = #{concentrateGoldReturnRate}
            </if>
            <if test="goldProduction != null ">AND
                t.gold_production = #{goldProduction}
            </if>
            <if test="miningExcavationVolume2 != null ">AND
                t.mining_excavation_volume2 = #{miningExcavationVolume2}
            </if>
            <if test="miningVolume2 != null ">AND
                t.mining_volume2 = #{miningVolume2}
            </if>
            <if test="excavationVolume2 != null ">AND
                t.excavation_volume2 = #{excavationVolume2}
            </if>
            <if test="excavationMeters2 != null ">AND
                t.excavation_meters2 = #{excavationMeters2}
            </if>
            <if test="geologicalProspectingMeters2 != null ">AND
                t.geological_prospecting_meters2 = #{geologicalProspectingMeters2}
            </if>
            <if test="productiveProspectingMeters2 != null ">AND
                t.productive_prospecting_meters2 = #{productiveProspectingMeters2}
            </if>
            <if test="developingTechnicalMeters2 != null ">AND
                t.developing_technical_meters2 = #{developingTechnicalMeters2}
            </if>
            <if test="pitDrillMeters2 != null ">AND
                t.pit_drill_meters2 = #{pitDrillMeters2}
            </if>
            <if test="surfaceDrillingMeters2 != null ">AND
                t.surface_drilling_meters2 = #{surfaceDrillingMeters2}
            </if>
            <if test="totalOreOutput2 != null ">AND
                t.total_ore_output2 = #{totalOreOutput2}
            </if>
            <if test="stopeOreOutput2 != null ">AND
                t.stope_ore_output2 = #{stopeOreOutput2}
            </if>
            <if test="byproductOreOutput2 != null ">AND
                t.byproduct_ore_output2 = #{byproductOreOutput2}
            </if>
            <if test="stopeTotalFillingCapacity2 != null ">AND
                t.stope_total_filling_capacity2 = #{stopeTotalFillingCapacity2}
            </if>
            <if test="tailingsFillingCapacity2 != null ">AND
                t.tailings_filling_capacity2 = #{tailingsFillingCapacity2}
            </if>
            <if test="wasteStoneFillingCapacity2 != null ">AND
                t.waste_stone_filling_capacity2 = #{wasteStoneFillingCapacity2}
            </if>
            <if test="emptySpaceFillingCapacity2 != null ">AND
                t.empty_space_filling_capacity2 = #{emptySpaceFillingCapacity2}
            </if>
            <if test="tailingsCapacity2 != null ">AND
                t.tailings_capacity2 = #{tailingsCapacity2}
            </if>
            <if test="tailingsDischargeCapacity2 != null ">AND
                t.tailings_discharge_capacity2 = #{tailingsDischargeCapacity2}
            </if>
            <if test="tailingsEmptySpaceFillingCapacity2 != null ">AND
                t.tailings_empty_space_filling_capacity2 = #{tailingsEmptySpaceFillingCapacity2}
            </if>
            <if test="oreProcessingCapacity2 != null ">AND
                t.ore_processing_capacity2 = #{oreProcessingCapacity2}
            </if>
            <if test="flotationProcessingCapacity2 != null ">AND
                t.flotation_processing_capacity2 = #{flotationProcessingCapacity2}
            </if>
            <if test="directProcessingCapacity2 != null ">AND
                t.direct_processing_capacity2 = #{directProcessingCapacity2}
            </if>
            <if test="gradeOfCrudeOre2 != null ">AND
                t.grade_of_crude_ore2 = #{gradeOfCrudeOre2}
            </if>
            <if test="crudeOreMetallicity2 != null ">AND
                t.crude_ore_metallicity2 = #{crudeOreMetallicity2}
            </if>
            <if test="oreDressingRecoveryRate2 != null ">AND
                t.ore_dressing_recovery_rate2 = #{oreDressingRecoveryRate2}
            </if>
            <if test="concentrateGoldReturnRate2 != null ">AND
                t.concentrate_gold_return_rate2 = #{concentrateGoldReturnRate2}
            </if>
            <if test="goldProduction2 != null ">AND
                t.gold_production2 = #{goldProduction2}
            </if>
            <if test="remark != null  and remark != ''">AND
                t.remark = #{remark}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
    <select id="selectInternalPlanningData" statementType="CALLABLE" resultType="com.ruoyi.productionplan.yearPlan.domain.YearPlan">
        {
            CALL p_year_plan_indicator(
                #{deptId, mode=IN,jdbcType=BIGINT},
                #{accountYear, mode=IN,jdbcType=BIGINT},
                #{planTypeValue, mode=IN,jdbcType=VARCHAR}
            )
        }
    </select>
</mapper>

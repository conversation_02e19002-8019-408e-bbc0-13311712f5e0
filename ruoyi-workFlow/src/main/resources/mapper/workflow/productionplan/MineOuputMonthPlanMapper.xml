<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.month.mapper.MineOuputMonthPlanMapper">
    <resultMap type="com.ruoyi.productionplan.month.domain.MineOuputMonthPlan"
               id="MineOuputMonthPlanResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="accountYear" column="account_year"/>
        <result property="accountMonth" column="account_month"/>
        <result property="status" column="status"/>
        <result property="applyUserId" column="apply_user_id"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="instanceId" column="instance_id"/>
        <result property="processKey" column="process_key"/>
        <result property="notify" column="notify"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectMineOuputMonthPlanVo">
        SELECT DISTINCT t.id,
                        t.dept_id,
                        t.account_year,
                        t.account_month,
                        t.status,
                        t.apply_user_id,
                        t.apply_user_name,
                        t.apply_time,
                        t.instance_id,
                        t.process_key,
                        t.notify,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag
        FROM biz_mine_ouput_month_plan t
                 LEFT JOIN sys_user u ON u.user_id = t.create_by_id AND u.user_name = t.create_by
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = t.dept_id
        </if>
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.month.domain.MineOuputMonthPlan"
            resultMap="MineOuputMonthPlanResult">
        <include refid="selectMineOuputMonthPlanVo"/>
        <where>
            1=1 and t.del_flag = '0'
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="accountYear != null ">
                AND t.account_year = #{accountYear}
            </if>
            <if test="accountMonth != null ">
                AND t.account_month = #{accountMonth}
            </if>
            <if test="status != null  and status != ''">
                AND t.status = #{status}
            </if>
            <if test="applyUserId != null  and applyUserId != ''">
                AND t.apply_user_id = #{applyUserId}
            </if>
            <if test="applyUserName != null  and applyUserName != ''">
                AND t.apply_user_name LIKE concat('%', #{applyUserName}, '%')
            </if>
            <if test="applyTime != null ">
                AND t.apply_time = #{applyTime}
            </if>
            <if test="instanceId != null  and instanceId != ''">
                AND t.instance_id = #{instanceId}
            </if>
            <if test="processKey != null  and processKey != ''">
                AND t.process_key = #{processKey}
            </if>
            <if test="notify != null  and notify != ''">
                AND t.notify = #{notify}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
    <select id="dailyPlanForChart" resultType="java.lang.String">
        SELECT sum( ore_output_capacity ) /( SELECT DATEDIFF( end_date, start_date )+ 1 FROM biz_account_period WHERE account_year = #{accountYear} AND account_month = #{accountMonth} ) AS ore_output_capacity
        FROM
            biz_mine_ouput_month_plan main
                INNER JOIN biz_mine_ouput_month_plan_sub sub ON main.id = sub.parent_id
        <where>
            `status` = 6 and main.del_flag =0 and sub.del_flag=0
            <if test="deptId!=null and deptId!=100">
                AND dept_id = #{deptId}
            </if>
            AND account_year = #{accountYear}
            AND account_month = #{accountMonth}
        </where>

    </select>
    <select id="dailyQuantityForChart" resultType="java.lang.String">
        SELECT
            sum( amount ) AS complete_amount
        FROM
            biz_rec_mining main
                INNER JOIN biz_rec_mining_sub sub ON main.id = sub.parent_id
        <where>
            submit_status_value = 1 and main.del_flag =0 and sub.del_flag=0
        <if test="deptId!=null and deptId!=100">
            AND dept_id = #{deptId}
        </if>
            AND rec_time = #{dispatchDate}
        </where>
    </select>
    <select id="monthlyPlannedQuantityForChart" resultType="java.lang.String">
        SELECT
            sum( ore_output_capacity ) AS ore_output_capacity
        FROM
            biz_mine_ouput_month_plan main
                INNER JOIN biz_mine_ouput_month_plan_sub sub ON main.id = sub.parent_id
        <where>
            `status` = 6  and main.del_flag =0 and sub.del_flag=0
        <if test="deptId!=null and deptId!=100">
            AND dept_id = #{deptId}
        </if>
            AND account_year = #{accountYear}
            AND account_month = #{accountMonth}
        </where>

    </select>
    <select id="monthlyCompletionVolumeForChart" resultType="java.lang.String">
        SELECT
            sum( amount ) AS complete_length
        FROM
            biz_rec_mining main
                INNER JOIN biz_rec_mining_sub sub ON main.id = sub.parent_id
        <where>
            submit_status_value = 1
        <if test="deptId!=null and deptId!=100">
            AND dept_id = #{deptId}
        </if>
            AND rec_time BETWEEN #{startDate}
            AND #{dispatchDate}
        </where>

    </select>
    <select id="yearPlannedQuantityForChart" resultType="java.lang.String">
        SELECT
            sum( ore_output_capacity ) AS ore_output_capacity
        FROM
            biz_mine_ouput_year_plan main
                INNER JOIN biz_mine_ouput_year_plan_sub sub ON main.id = sub.parent_id
        <where>
            `status` = 6  and main.del_flag =0 and sub.del_flag=0
        <if test="deptId!=null and deptId!=100">
            AND dept_id = #{deptId}
        </if>
            AND account_year = #{accountYear}
        </where>

    </select>
    <select id="yearCompletionVolumeForChart" resultType="java.lang.String">
        SELECT
            sum( footage ) AS accept_length
        FROM
            biz_tunnelling_month_accept main
                INNER JOIN biz_tunnelling_month_accept_sub sub ON main.id = sub.parent_id
        <where>
            submit_status_value = 1
        <if test="deptId!=null and deptId!=100">
            AND dept_id = #{deptId}
        </if>

            AND account_year = #{accountYear}
            AND account_month  &lt; #{accountMonth}
        </where>


    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.month.mapper.MineOuputMonthPlanSubMapper">
    <resultMap type="com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub"
               id="MineOuputMonthPlanSubResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="yearPlanSubId" column="year_plan_sub_id"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="paragraphId" column="paragraph_id"/>
        <result property="projectItemId" column="project_item_id"/>
        <result property="workAreaId" column="work_area_id"/>
        <result property="miningMethodsValue" column="mining_methods_value"/>
        <result property="unitThroughput" column="unit_throughput"/>
        <result property="unitNumber" column="unit_number"/>
        <result property="oreConsumption" column="ore_consumption"/>
        <result property="geologicalGrade" column="geological_grade"/>
        <result property="geologicalMetallicity" column="geological_metallicity"/>
        <result property="firstLoss" column="first_loss"/>
        <result property="firstDilution" column="first_dilution"/>
        <result property="improveWellheadId" column="improve_wellhead_id"/>
        <result property="improveCapactity" column="improve_capactity"/>
        <result property="miningGrade" column="mining_grade"/>
        <result property="miningCapacity" column="mining_capacity"/>
        <result property="miningMetallicity" column="mining_metallicity"/>
        <result property="secondLoss" column="second_loss"/>
        <result property="secondDilution" column="second_dilution"/>
        <result property="oreOutputGrade" column="ore_output_grade"/>
        <result property="oreOutputCapacity" column="ore_output_capacity"/>
        <result property="oreOutputMetallicity" column="ore_output_metallicity"/>
        <result property="tailingsFillingCapacity" column="tailings_filling_capacity"/>
        <result property="wasteStoneFillingCapacity" column="waste_stone_filling_capacity"/>
        <result property="fillingTerm" column="filling_term"/>
        <result property="investmentUnitPrice" column="investment_unit_price"/>
        <result property="investmentAmount" column="investment_amount"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="constructionTime" column="construction_time"/>
        <result property="rigNo" column="rig_no"/>
        <result property="yesornoValue" column="yesorno_value"/>
        <result property="transportRoute" column="transport_route"/>
        <result property="drawingFile" column="drawing_file"/>
        <result property="stopeDescribe" column="stope_describe"/>
    </resultMap>
    <sql id="selectMineOuputMonthPlanSubVo">
        SELECT DISTINCT t.id,
                        t.parent_id,
                        t.year_plan_sub_id,
                        t.wellhead_id,
                        t.paragraph_id,
                        t.work_area_id,
                        t.project_item_id,
                        t.mining_methods_value,
                        t.unit_throughput,
                        t.improve_wellhead_id,
                        t.improve_capactity,
                        t.unit_number,
                        t.ore_consumption,
                        t.geological_grade,
                        t.geological_metallicity,
                        t.first_loss,
                        t.first_dilution,
                        t.mining_grade,
                        t.mining_capacity,
                        t.mining_metallicity,
                        t.second_loss,
                        t.second_dilution,
                        t.ore_output_grade,
                        t.ore_output_capacity,
                        t.ore_output_metallicity,
                        t.tailings_filling_capacity,
                        t.waste_stone_filling_capacity,
                        t.filling_term,
                        t.investment_unit_price,
                        t.investment_amount,
                        t.remark,
                        t.del_flag,
                        t.construction_time,
                        t.rig_no,
                        t.yesorno_value,
                        t.transport_route,
                        t.drawing_file,
                        t.stope_describe
        FROM biz_mine_ouput_month_plan_sub t
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub"
            resultMap="MineOuputMonthPlanSubResult">
        <include refid="selectMineOuputMonthPlanSubVo"/>
        <where>
            1=1 and t.del_flag = '0'
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="parentId != null ">
                AND t.parent_id = #{parentId}
            </if>
            <if test="yearPlanSubId != null ">
                AND t.year_plan_sub_id = #{yearPlanSubId}
            </if>
            <if test="wellheadId != null ">
                AND t.wellhead_id = #{wellheadId}
            </if>
            <if test="paragraphId != null ">
                AND t.paragraph_id = #{paragraphId}
            </if>
            <if test="workAreaId != null ">
                AND t.work_area_id = #{workAreaId}
            </if>
            <if test="projectItemId != null ">
                AND t.project_item_id = #{projectItemId}
            </if>
            <if test="miningMethodsValue != null  and miningMethodsValue != ''">
                AND t.mining_methods_value = #{miningMethodsValue}
            </if>
            <if test="unitThroughput != null ">
                AND t.unit_throughput = #{unitThroughput}
            </if>
            <if test="unitNumber != null ">
                AND t.unit_number = #{unitNumber}
            </if>
            <if test="oreConsumption != null ">
                AND t.ore_consumption = #{oreConsumption}
            </if>
            <if test="geologicalGrade != null ">
                AND t.geological_grade = #{geologicalGrade}
            </if>
            <if test="geologicalMetallicity != null ">
                AND t.geological_metallicity = #{geologicalMetallicity}
            </if>
            <if test="firstLoss != null ">
                AND t.first_loss = #{firstLoss}
            </if>
            <if test="firstDilution != null ">
                AND t.first_dilution = #{firstDilution}
            </if>
            <if test="miningGrade != null ">
                AND t.mining_grade = #{miningGrade}
            </if>
            <if test="miningCapacity != null ">
                AND t.mining_capacity = #{miningCapacity}
            </if>
            <if test="miningMetallicity != null ">
                AND t.mining_metallicity = #{miningMetallicity}
            </if>
            <if test="secondLoss != null ">
                AND t.second_loss = #{secondLoss}
            </if>
            <if test="secondDilution != null ">
                AND t.second_dilution = #{secondDilution}
            </if>
            <if test="oreOutputGrade != null ">
                AND t.ore_output_grade = #{oreOutputGrade}
            </if>
            <if test="oreOutputCapacity != null ">
                AND t.ore_output_capacity = #{oreOutputCapacity}
            </if>
            <if test="oreOutputMetallicity != null ">
                AND t.ore_output_metallicity = #{oreOutputMetallicity}
            </if>
            <if test="tailingsFillingCapacity != null ">
                AND t.tailings_filling_capacity = #{tailingsFillingCapacity}
            </if>
            <if test="wasteStoneFillingCapacity != null ">
                AND t.waste_stone_filling_capacity = #{wasteStoneFillingCapacity}
            </if>
            <if test="fillingTerm != null ">
                AND t.filling_term = #{fillingTerm}
            </if>
            <if test="investmentUnitPrice != null ">
                AND t.investment_unit_price = #{investmentUnitPrice}
            </if>
            <if test="investmentAmount != null ">
                AND t.investment_amount = #{investmentAmount}
            </if>
        </where>
        ORDER BY t.id asc
    </select>
</mapper>

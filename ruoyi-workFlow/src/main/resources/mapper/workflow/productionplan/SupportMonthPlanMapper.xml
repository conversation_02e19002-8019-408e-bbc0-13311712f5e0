<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.month.mapper.SupportMonthPlanMapper">
    <resultMap type="com.ruoyi.productionplan.month.domain.SupportMonthPlan" id="SupportMonthPlanResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="accountYear" column="account_year"/>
        <result property="accountMonth" column="account_month"/>
        <result property="status" column="status"/>
        <result property="applyUserId" column="apply_user_id"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="instanceId" column="instance_id"/>
        <result property="processKey" column="process_key"/>
        <result property="notify" column="notify"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.dept_id,
                        t.account_year,
                        t.account_month,
                        t.status,
                        t.apply_user_id,
                        t.apply_user_name,
                        t.apply_time,
                        t.instance_id,
                        t.process_key,
                        t.notify,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag
        from biz_support_month_plan t
        where t.id in (select distinct t.id
                       from biz_support_month_plan t
                                left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = t.dept_id
        </if>
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.month.domain.SupportMonthPlan"
            resultMap="SupportMonthPlanResult">
        <include refid="selectFrom"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="accountYear != null ">
                and t.account_year = #{accountYear}
            </if>
            <if test="accountMonth != null ">
                and t.account_month = #{accountMonth}
            </if>
            <if test="status != null  and status != ''">
                and t.status = #{status}
            </if>
            <if test="applyUserName != null  and applyUserName != ''">
                and t.apply_user_name LIKE concat('%', #{applyUserName}, '%')
            </if>
            <if test="applyTime != null ">
                and t.apply_time = #{applyTime}
            </if>
            <if test="notify != null  and notify != ''">
                and t.notify = #{notify}
            </if>
            ${params.dataScope}
        </where>
        <include refid="groupOrder"/>
    </select>
    <select id="excavationAddExtractionPlan"
            resultType="com.ruoyi.productionplan.month.domain.SupportMonthPlanSub">
        SELECT
            project_item_id,
            paragraph_id,
            work_area_id,
            wellhead_id,
            plan_volume,
            unit_value
        FROM
            (
                SELECT
                    ts.project_item_id,
                    ts.paragraph_id,
                    ts.work_area_id,
                    ts.wellhead_id,
                    ts.length AS plan_volume,
                    "m" AS unit_value
                FROM
                    biz_tunnelling_month_plan t
                        LEFT JOIN biz_tunnelling_month_plan_sub ts ON t.id = ts.parent_id
                WHERE
                    t.account_year =  #{accountYear}
                  AND t.account_month = #{accountMonth}
                  AND t.dept_id = #{deptId}
                UNION
                SELECT
                    ms.project_item_id,
                    ms.paragraph_id,
                    ms.work_area_id,
                    ms.wellhead_id,
                    ms.mining_capacity AS plan_volume,
                    "t" AS unit_value
                FROM
                    biz_mine_ouput_month_plan m
                        LEFT JOIN biz_mine_ouput_month_plan_sub ms ON m.id = ms.parent_id
                WHERE
                    m.account_year =  #{accountYear}
                  AND m.account_month = #{accountMonth}
                  AND m.dept_id = #{deptId}
            ) AS a
    </select>
</mapper>

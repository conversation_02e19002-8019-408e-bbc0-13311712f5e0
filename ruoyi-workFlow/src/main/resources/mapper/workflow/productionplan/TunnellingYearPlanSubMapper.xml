<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.year.mapper.TunnellingYearPlanSubMapper">
    <resultMap type="com.ruoyi.productionplan.year.domain.TunnellingYearPlanSub" id="TunnellingYearPlanSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="wellheadId" column="wellhead_id"/>
            <result property="paragraphId" column="paragraph_id"/>
            <result property="workAreaId" column="work_area_id"/>
            <result property="projectItemId" column="project_item_id"/>
            <result property="unitThroughput" column="unit_throughput"/>
            <result property="unitNumber" column="unit_number"/>
            <result property="specifications" column="specifications"/>
            <result property="crossSectional" column="cross_sectional"/>
            <result property="length" column="length"/>
            <result property="volume" column="volume"/>
            <result property="excavationVolume" column="excavation_volume"/>
            <result property="byproductCapacity" column="byproduct_capacity"/>
            <result property="byproductGrade" column="byproduct_grade"/>
            <result property="byproductMetallicity" column="byproduct_metallicity"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.wellhead_id,
            t.paragraph_id,
            t.work_area_id,
            t.project_item_id,
            t.unit_throughput,
            t.unit_number,
            t.specifications,
            t.cross_sectional,
            t.length,
            t.volume,
            t.excavation_volume,
            t.byproduct_capacity,
            t.byproduct_grade,
            t.byproduct_metallicity,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time,
            t.remark,
            t.del_flag
        from biz_tunnelling_year_plan_sub t
        where t.id in
              (select distinct t.id
        from biz_tunnelling_year_plan_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        <!-- left join sys_dept d on (t.dept_id) = (d.dept_id)-->
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id asc
        )
        group by t.id
        order by t.id asc
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.year.domain.TunnellingYearPlanSub" resultMap="TunnellingYearPlanSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1 and t.del_flag = '0'
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="wellheadId != null ">
                            and t.wellhead_id = #{wellheadId}
                        </if>
                        <if test="paragraphId != null ">
                            and t.paragraph_id = #{paragraphId}
                        </if>
                        <if test="workAreaId != null ">
                            and t.work_area_id = #{workAreaId}
                        </if>
                        <if test="projectItemId != null ">
                            and t.project_item_id = #{projectItemId}
                        </if>
                        <if test="unitThroughput != null ">
                            and t.unit_throughput = #{unitThroughput}
                        </if>
                        <if test="unitNumber != null ">
                            and t.unit_number = #{unitNumber}
                        </if>
                        <if test="specifications != null  and specifications != ''">
                            and t.specifications = #{specifications}
                        </if>
                        <if test="crossSectional != null  and crossSectional != ''">
                            and t.cross_sectional = #{crossSectional}
                        </if>
                        <if test="length != null ">
                            and t.length = #{length}
                        </if>
                        <if test="volume != null ">
                            and t.volume = #{volume}
                        </if>
                        <if test="excavationVolume != null ">
                            and t.excavation_volume = #{excavationVolume}
                        </if>
                        <if test="byproductCapacity != null ">
                            and t.byproduct_capacity = #{byproductCapacity}
                        </if>
                        <if test="byproductGrade != null ">
                            and t.byproduct_grade = #{byproductGrade}
                        </if>
                        <if test="byproductMetallicity != null ">
                            and t.byproduct_metallicity = #{byproductMetallicity}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="createBy != null  and createBy != ''">
                            and t.create_by = #{createBy}
                        </if>
                        <if test="createTime != null ">
                            and t.create_time = #{createTime}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="updateBy != null  and updateBy != ''">
                            and t.update_by = #{updateBy}
                        </if>
                        <if test="updateTime != null ">
                            and t.update_time = #{updateTime}
                        </if>
                        <if test="remark != null  and remark != ''">
                            and t.remark = #{remark}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productionplan.year.mapper.SupportYearPlanSubMapper">
    <resultMap type="com.ruoyi.productionplan.year.domain.SupportYearPlanSub" id="SupportYearPlanSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="wellheadId" column="wellhead_id"/>
            <result property="paragraphId" column="paragraph_id"/>
            <result property="workAreaId" column="work_area_id"/>
            <result property="projectItemId" column="project_item_id"/>
            <result property="supportTypeValue" column="support_type_value"/>
            <result property="specifications" column="specifications"/>
            <result property="unitValue" column="unit_value"/>
            <result property="planVolume" column="plan_volume"/>
            <result property="supportMethodValue" column="support_method_value"/>
            <result property="boltNumber" column="bolt_number"/>
            <result property="beltNumber" column="belt_number"/>
            <result property="netArea" column="net_area"/>
            <result property="concreteVolume" column="concrete_volume"/>
            <result property="usteelNumber" column="usteel_number"/>
            <result property="boltCost" column="bolt_cost"/>
            <result property="beltCost" column="belt_cost"/>
            <result property="netCost" column="net_cost"/>
            <result property="concreteCost" column="concrete_cost"/>
            <result property="usteelCost" column="usteel_cost"/>
            <result property="totalSupportCost" column="total_support_cost"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.wellhead_id,
            t.paragraph_id,
            t.work_area_id,
            t.project_item_id,
            t.support_type_value,
            t.specifications,
            t.unit_value,
            t.plan_volume,
            t.support_method_value,
            t.bolt_number,
            t.belt_number,
            t.net_area,
            t.concrete_volume,
            t.usteel_number,
            t.bolt_cost,
            t.belt_cost,
            t.net_cost,
            t.concrete_cost,
            t.usteel_cost,
            t.total_support_cost,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time,
            t.remark,
            t.del_flag
        from biz_support_year_plan_sub t
        where t.id in
              (select distinct t.id
        from biz_support_year_plan_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        <!--left join sys_dept d on (t.dept_id) = (d.dept_id)-->
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id asc
        )
        group by t.id
        order by t.id asc
    </sql>
    <select id="list" parameterType="com.ruoyi.productionplan.year.domain.SupportYearPlanSub" resultMap="SupportYearPlanSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1 and t.del_flag = '0'
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="wellheadId != null ">
                            and t.wellhead_id = #{wellheadId}
                        </if>
                        <if test="paragraphId != null ">
                            and t.paragraph_id = #{paragraphId}
                        </if>
                        <if test="workAreaId != null ">
                            and t.work_area_id = #{workAreaId}
                        </if>
                        <if test="projectItemId != null ">
                            and t.project_item_id = #{projectItemId}
                        </if>
                        <if test="supportTypeValue != null  and supportTypeValue != ''">
                            and t.support_type_value = #{supportTypeValue}
                        </if>
                        <if test="specifications != null  and specifications != ''">
                            and t.specifications = #{specifications}
                        </if>
                        <if test="unitValue != null  and unitValue != ''">
                            and t.unit_value = #{unitValue}
                        </if>
                        <if test="planVolume != null ">
                            and t.plan_volume = #{planVolume}
                        </if>
                        <if test="supportMethodValue != null  and supportMethodValue != ''">
                            and t.support_method_value = #{supportMethodValue}
                        </if>
                        <if test="boltNumber != null ">
                            and t.bolt_number = #{boltNumber}
                        </if>
                        <if test="beltNumber != null ">
                            and t.belt_number = #{beltNumber}
                        </if>
                        <if test="netArea != null ">
                            and t.net_area = #{netArea}
                        </if>
                        <if test="concreteVolume != null ">
                            and t.concrete_volume = #{concreteVolume}
                        </if>
                        <if test="usteelNumber != null ">
                            and t.usteel_number = #{usteelNumber}
                        </if>
                        <if test="boltCost != null ">
                            and t.bolt_cost = #{boltCost}
                        </if>
                        <if test="beltCost != null ">
                            and t.belt_cost = #{beltCost}
                        </if>
                        <if test="netCost != null ">
                            and t.net_cost = #{netCost}
                        </if>
                        <if test="concreteCost != null ">
                            and t.concrete_cost = #{concreteCost}
                        </if>
                        <if test="usteelCost != null ">
                            and t.usteel_cost = #{usteelCost}
                        </if>
                        <if test="totalSupportCost != null ">
                            and t.total_support_cost = #{totalSupportCost}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="createBy != null  and createBy != ''">
                            and t.create_by = #{createBy}
                        </if>
                        <if test="createTime != null ">
                            and t.create_time = #{createTime}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="updateBy != null  and updateBy != ''">
                            and t.update_by = #{updateBy}
                        </if>
                        <if test="updateTime != null ">
                            and t.update_time = #{updateTime}
                        </if>
                        <if test="remark != null  and remark != ''">
                            and t.remark = #{remark}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

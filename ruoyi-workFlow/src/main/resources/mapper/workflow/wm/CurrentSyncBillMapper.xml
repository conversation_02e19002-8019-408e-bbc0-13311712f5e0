<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.kingdeeInterface.mapper.CurrentSyncBillMapper">
    <resultMap type="com.ruoyi.wm.kingdeeInterface.domain.currentSyncBill.CurrentSyncBill" id="CurrentSynchronizeBillResult">
            <result property="id" column="id"/>
            <result property="billNo" column="bill_no"/>
            <result property="data" column="data"/>
            <result property="operatorType" column="operator_type"/>
            <result property="operatorResult" column="operator_result"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="createById" column="create_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="auditTime" column="audit_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.bill_no,
            t.data,
            t.operator_type,
            t.operator_result,
            t.create_time,
            t.update_time,
            t.audit_time,
            t.remark
        from biz_current_sync_bill t
    </sql>
    <select id="list" parameterType="com.ruoyi.wm.kingdeeInterface.domain.currentSyncBill.CurrentSyncBill" resultMap="CurrentSynchronizeBillResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="billNo != null  and billNo != ''">
                and t.bill_no = #{billNo}
            </if>
            <if test="data != null  and data != ''">
                and t.data = #{data}
            </if>
            <if test="operatorType != null  and operatorType != ''">
                and t.operator_type = #{operatorType}
            </if>
            <if test="startTime != null  and endTime != null">
                and t.audit_time between #{startTime} and #{endTime}
            </if>
            <if test="operatorResult!= null">
                and  t.operator_result =#{operatorResult}
            </if>
        </where>
       order by  t.bill_no DESC
    </select>
<!--    <insert id="batchUpsertAtomically" parameterType="java.util.List">-->
<!--        INSERT INTO-->
<!--            dbo.biz_current_sync_bill-->
<!--            (-->
<!--                bill_no,-->
<!--                data,-->
<!--                operator_type,-->
<!--                operator_result,-->
<!--                create_time,-->
<!--                update_time,-->
<!--                audit_time-->
<!--            )-->
<!--         <choose>-->
<!--            &lt;!&ndash; 高版本优先使用VALUES语法 &ndash;&gt;-->
<!--            <when test="_databaseId == 'sqlserver' and _databaseVersion >= 10">-->
<!--                VALUES-->
<!--                <foreach collection="list" item="item" separator=",">-->
<!--                    (-->
<!--                        #{item.billNo},-->
<!--                        #{item.data},-->
<!--                        #{item.operatorType},-->
<!--                        #{item.operatorResult},-->
<!--                        #{item.createTime},-->
<!--                        #{item.updateTime},-->
<!--                        #{item.auditTime}-->
<!--                    )-->
<!--                </foreach>-->
<!--            </when>-->
<!--            &lt;!&ndash; 低版本使用SELECT UNION ALL &ndash;&gt;-->
<!--            <otherwise>-->
<!--                <foreach collection="list" item="item" index="index" separator=" UNION ALL ">-->
<!--                SELECT-->
<!--                    #{item.billNo},-->
<!--                    #{item.data},-->
<!--                    #{item.operatorType},-->
<!--                    #{item.operatorResult},-->
<!--                    #{item.createTime},-->
<!--                    #{item.updateTime},-->
<!--                    #{item.auditTime}-->
<!--                FROM (SELECT 1) AS dummy-->
<!--        </foreach>-->
<!--            </otherwise>-->
<!--        </choose>-->
<!--    </insert>-->
    <delete id="deleteBill" parameterType="com.ruoyi.wm.kingdeeInterface.domain.currentSyncBill.CurrentSyncBill">
        delete  from biz_current_sync_bill
        <where>
        1=1
        <if test="startTime!= null and endTime != null">
            and biz_current_sync_bill.audit_time between #{startTime} and #{endTime}
        </if>
        </where>
    </delete>
</mapper>

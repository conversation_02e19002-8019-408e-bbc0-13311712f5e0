<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.currentStock.mapper.CurrentStockMapper">
    <resultMap type="com.ruoyi.wm.currentStock.domain.CurrentStock" id="CurrentStockResult">
        <result property="id" column="id"/>
        <result property="materialId" column="material_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="qty" column="qty"/>
        <result property="price" column="price"/>
        <result property="amount" column="amount"/>
        <result property="calculationPrice" column="calculation_price"/>
        <result property="calculationAmount" column="calculation_amount"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectCurrentStockVo">
        select distinct
               t.id,
               t.material_id,
               m.code as material_code,
               m.name as material_name,
               t.warehouse_id,
               w.code as warehouse_code,
               w.name as warehouse_name,
               t.qty,
               t.price,
               t.amount,
               t.calculation_price,
               t.calculation_amount,
               t.del_flag,
               t.create_by,
               t.create_by_id,
               t.create_time,
               t.update_by,
               t.update_by_id,
               t.update_time,
               t.remark
        from
            wm_current_stock t
                left join biz_material m on t.material_id = m.id
                left join biz_warehouse w  on w.id = t.warehouse_id
        <!-- 数据范围过滤 -->
        <if test="params != null and params.dataScope != null and params.dataScope != '' ">
                left join sys_dept d on d.dept_id = w.dept_id
        </if>
    </sql>

    <select id="list" parameterType="com.ruoyi.wm.currentStock.domain.CurrentStock" resultMap="CurrentStockResult">
        <include refid="selectCurrentStockVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="materialCode != null and materialCode != ''">AND
                m.code = #{materialCode}
            </if>
            <if test="materialId != null and materialId != ''">AND
                t.material_id = #{materialId}
            </if>
            <if test="warehouseCode != null and warehouseCode != ''">AND
                w.code like CONCAT('%',#{warehouseCode},'%')
            </if>
            <if test="warehouseId != null and warehouseId != ''">AND
                t.warehouse_id = #{warehouseId}
            </if>
            <if test="qty == -0.618">AND
                t.qty != 0
            </if>
            <if test="price != null ">AND
                t.price = #{price}
            </if>
            <if test="amount != null ">AND
                t.amount = #{amount}
            </if>
            <if test="calculationPrice != null ">AND
                t.calculation_price = #{calculationPrice}
            </if>
            <if test="calculationAmount != null ">AND
                t.calculation_amount = #{calculationAmount}
            </if>
            <if test="createById != null ">AND
                t.create_by_id = #{createById}
            </if>
            <if test="updateById != null ">AND
                t.update_by_id = #{updateById}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>

        </where>
        ORDER BY w.code, m.code
    </select>

    <select id="listMaterialRefsFromCurrentStock"
            parameterType="com.ruoyi.wm.currentStock.domain.CurrentStock"
            resultType="com.ruoyi.wm.currentStock.domain.CurrentStock">
        select distinct
            t.material_id,
            m.code as material_code,
            m.name as material_name
        from
            wm_current_stock t
        left join
            biz_material m on m.id = t.material_id
        left join
            biz_warehouse w on w.id = t.warehouse_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != ''">
            left join
                sys_dept d on d.dept_id = w.dept_id
        </if>
        <where>
            <if test="params != null and params.dataScope != null and params.dataScope != ''">
                ${params.dataScope}
            </if>
            <if test="materialCode != null and materialCode != ''">
                and m.code like CONCAT('%', #{materialCode}, '%')
            </if>
            <if test="materialName != null and materialName != ''">
                and m.name like CONCAT('%', #{materialName}, '%')
            </if>
            <if test="warehouseId != null and warehouseId != ''">
                and t.warehouse_id = ${warehouseId}
            </if>
        </where>

    </select>

    <delete id="truncateTable">
        delete
        from wm_current_stock
    </delete>

    <insert id="batchUpsertIncrementallyAtomically" parameterType="com.ruoyi.wm.currentStock.domain.CurrentStock">
        INSERT INTO wm_current_stock (
        material_id,
        warehouse_id,
        <if test="list[0].qty != null">
            qty,
        </if>
        <if test="list[0].calculationPrice != null">
            calculation_price,
        </if>
        <if test="list[0].qty != null or list[0].calculationPrice != null">
            calculation_amount,
        </if>
        version
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.materialId},
            #{item.warehouseId},
            <if test="item.qty != null">
                #{item.qty},
            </if>
            <if test="item.calculationPrice != null">
                #{item.calculationPrice},
            </if>
             <if test="item.qty != null and item.calculationPrice == null">
                #{item.qty} * calculation_price,
            </if>
             <if test="item.qty == null and item.calculationPrice != null">
                qty * #{item.calculationPrice},
            </if>
            <if test="item.qty != null and item.calculationPrice != null">
                #{item.qty} * #{item.calculationPrice},
            </if>
            1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        <if test="list[0].qty != null">
            qty = qty + VALUES(qty),
        </if>
        <if test="list[0].calculationPrice != null">
            calculation_price = VALUES(calculation_price),
        </if>
          <if test="list[0].qty != null and list[0].calculationPrice == null">
            calculation_amount = (qty + VALUES(qty)) * calculation_price,
        </if>
        <if test="list[0].qty == null and list[0].calculationPrice != null">
            calculation_amount = qty * VALUES(calculation_price),
        </if>
        <if test="list[0].qty != null and list[0].calculationPrice != null">
            calculation_amount = (qty + VALUES(qty)) * VALUES(calculation_price),
        </if>
        version = version + 1
    </insert>

    <insert id="batchUpsertAtomically" parameterType="com.ruoyi.wm.currentStock.domain.CurrentStock">
        INSERT INTO wm_current_stock (
        material_id,
        warehouse_id,
        <if test="list[0].qty != null">
            qty,
        </if>
        <if test="list[0].calculationPrice != null">
            calculation_price,
        </if>
        <if test="list[0].qty != null or list[0].calculationPrice != null">
            calculation_amount,
        </if>
        version
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.materialId},
            #{item.warehouseId},
            <if test="item.qty != null">
                #{item.qty},
            </if>
            <if test="item.calculationPrice != null">
                #{item.calculationPrice},
            </if>
            <if test="item.qty != null and item.calculationPrice == null">
                #{item.qty} * calculation_price,
            </if>
            <if test="item.qty == null and item.calculationPrice != null">
                qty * #{item.calculationPrice},
            </if>
            <if test="item.qty != null and item.calculationPrice != null">
                #{item.qty} * #{item.calculationPrice},
            </if>
            1
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        <if test="list[0].qty!= null">
            qty = VALUES(qty),
        </if>
        <if test="list[0].calculationPrice!= null">
            calculation_price = VALUES(calculation_price),
        </if>
        <if test="list[0].qty != null and list[0].calculationPrice == null">
            calculation_amount = VALUES(qty) * calculation_price,
        </if>
        <if test="list[0].qty == null and list[0].calculationPrice != null">
            calculation_amount = qty * VALUES(calculation_price),
        </if>
        <if test="list[0].qty != null and list[0].calculationPrice != null">
            calculation_amount = VALUES(qty) * VALUES(calculation_price),
        </if>
        version = version + 1
    </insert>

</mapper>

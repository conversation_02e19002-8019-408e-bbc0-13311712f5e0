<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.transfer.mapper.TransferMapper">
    <resultMap type="com.ruoyi.wm.transfer.domain.Transfer" id="TransferResult">
            <result property="id" column="id"/>
            <result property="billNo" column="bill_no"/>
            <result property="date" column="date"/>
            <result property="requestDeptId" column="request_dept_id"/>
            <result property="outWarehouseId" column="out_warehouse_id"/>
            <result property="inWarehouseId" column="in_warehouse_id"/>
            <result property="refUserId" column="ref_user_id"/>
            <result property="managerId" column="manager_id"/>
            <result property="billStatus" column="bill_status"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.bill_no,
            t.date,
            t.request_dept_id,
            t.out_warehouse_id,
            t.in_warehouse_id,
            t.ref_user_id,
            t.manager_id,
            t.bill_status,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.source_bill_type,
            t.source_bill_id,
            t.remark,
            tr.bill_no as sourceBillNo
        from wm_transfer t
        LEFT JOIN wm_transfer_request tr on tr.id = t.source_bill_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = t.request_dept_id
            LEFT JOIN sys_user u ON u.user_id = t.create_by_id AND u.user_name = t.create_by
        </if>
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.wm.transfer.domain.Transfer" resultMap="TransferResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="billNo != null  and billNo != ''">
                and t.bill_no LIKE concat('%', #{billNo}, '%')
            </if>
            <if test="startDate != null ">AND
                t.date BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="requestDeptId != null ">
                and t.request_dept_id = #{requestDeptId}
            </if>
            <if test="outWarehouseId != null ">
                and t.out_warehouse_id = #{outWarehouseId}
            </if>
            <if test="inWarehouseId != null ">
                and t.in_warehouse_id = #{inWarehouseId}
            </if>
            <if test="refUserId != null ">
                and t.ref_user_id = #{refUserId}
            </if>
            <choose>
                <when test="billStatus != null and billStatus != ''">
                    AND t.bill_status = #{billStatus}
                </when>
                <otherwise>
                    AND t.bill_status != -1
                </otherwise>
            </choose>
            <if test="delFlag != null  and delFlag != ''">
                and t.del_flag = #{delFlag}
            </if>
            <if test="createBy != null  and createBy != ''">
                and t.create_by = #{createBy}
            </if>
            <if test="createById != null ">
                and t.create_by_id = #{createById}
            </if>
            <if test="createTime != null ">
                and t.create_time = #{createTime}
            </if>
            <if test="updateBy != null  and updateBy != ''">
                and t.update_by = #{updateBy}
            </if>
            <if test="updateById != null ">
                and t.update_by_id = #{updateById}
            </if>
            <if test="updateTime != null ">
                and t.update_time = #{updateTime}
            </if>
            <if test="remark != null  and remark != ''">
                and t.remark = #{remark}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
<!--        <include refid="groupOrder"/>-->
    </select>
</mapper>

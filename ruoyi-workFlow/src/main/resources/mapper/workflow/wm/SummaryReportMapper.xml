<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.report.summaryReport.mapper.SummaryReportMapper">
    <resultMap type="com.ruoyi.wm.report.summaryReport.domain.SummaryReport" id="SummaryReportResult">
            <result property="startAccountPeriodId" column="start_account_period_id"/>
            <result property="endAccountPeriodId" column="end_account_period_id"/>
            <result property="warehouseId" column="warehouse_id"/>
            <result property="warehouseCode" column="warehouse_code"/>
            <result property="materialId" column="material_id"/>
            <result property="materialCode" column="material_code"/>
            <result property="startQty" column="start_qty"/>
            <result property="startAmount" column="start_amount"/>
            <result property="increaseQty" column="increase_qty"/>
            <result property="increaseAmount" column="increase_amount"/>
            <result property="reduceQty" column="reduce_qty"/>
            <result property="reduceAmount" column="reduce_amount"/>
            <result property="endQty" column="end_qty"/>
            <result property="endAmount" column="end_amount"/>
            <result property="startDate" column="start_date"/>
            <result property="endDate" column="end_date"/>
    </resultMap>

    <select id="listSummaryReportForAllPeriodCheckedOut" resultType="com.ruoyi.wm.report.summaryReport.domain.SummaryReport">
        SELECT
            ${selectFields},
            cast(SUM(coalesce(start_qty, 0)) as decimal(16,4)) AS start_qty,
            cast(SUM(coalesce(start_amount, 0)) as decimal(16,4)) AS start_amount,
            cast(SUM(coalesce(increase_qty, 0)) as decimal(16,4)) AS increase_qty,
            cast(SUM(coalesce(increase_amount, 0)) as decimal(16,4)) AS increase_amount,
            cast(SUM(coalesce(reduce_qty, 0)) as decimal(16,4)) AS reduce_qty,
            cast(SUM(coalesce(reduce_amount, 0)) as decimal(16,4)) AS reduce_amount,
            cast(SUM(coalesce(end_qty, 0)) as decimal(16,4)) AS end_qty,
            cast(SUM(coalesce(end_amount, 0)) as decimal(16,4)) AS end_amount
        FROM (
            SELECT
                main.warehouse_id AS warehouse_id,    -- 仓库id
                main.material_id AS material_id,     -- 物料id
                coalesce(start_b.start_qty, 0) AS start_qty,       -- 期初数量
                coalesce(start_b.start_amount, 0) AS start_amount, -- 期初金额
                coalesce(start_b.start_increase_qty, 0) + coalesce(end_b.start_increase_qty, 0) AS increase_qty,          -- 期间增加数量
                coalesce(start_b.start_increase_amount, 0) + coalesce(end_b.start_increase_amount, 0) AS increase_amount, -- 期间增加金额
                coalesce(start_b.start_reduce_qty, 0) + coalesce(end_b.start_reduce_qty, 0) AS reduce_qty,                -- 期间减少数量
                coalesce(start_b.start_reduce_amount, 0) + coalesce(end_b.start_reduce_amount, 0) AS reduce_amount,       -- 期间减少金额
                coalesce(end_b.end_qty, 0) AS end_qty,       -- 期末数量
                coalesce(end_b.end_amount, 0) AS end_amount  -- 期末金额
            FROM (
                -- 结存表
                SELECT DISTINCT
                    warehouse_id,
                    material_id
                FROM
                    wm_balance
                WHERE
                    account_period_id IN (#{startAccountPeriodId}, #{endAccountPeriodId})
            ) AS main

            -- 开始会计期间的结存表
            LEFT JOIN wm_balance start_b
                ON start_b.account_period_id = #{startAccountPeriodId}
                    AND start_b.material_id = main.material_id
                    AND start_b.warehouse_id = main.warehouse_id

            -- 开始会计期间的结存表
            LEFT JOIN wm_balance end_b
                ON end_b.account_period_id = #{endAccountPeriodId}
                    AND end_b.material_id = main.material_id
                    AND end_b.warehouse_id = main.warehouse_id
        )  AS master_table
        LEFT JOIN biz_material m
            ON m.id = master_table.material_id
        LEFT JOIN biz_warehouse w
            ON w.id = master_table.warehouse_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = w.dept_id
        </if>
        <where>
            <if test="warehouseId != null  and warehouseId != ''">AND
                master_table.warehouse_id = #{warehouseId}
            </if>
            <if test="materialId != null  and materialId != ''">AND
                master_table.material_id = #{materialId}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>

        GROUP BY
           ${selectFields}
    </select>

    <select id="listSummaryReportForEndPeriodNotCheckedOut" resultType="com.ruoyi.wm.report.summaryReport.domain.SummaryReport">
        SELECT
            ${selectFields},
            cast(SUM(coalesce(start_qty, 0)) as decimal(16,4)) AS start_qty,
            cast(SUM(coalesce(start_amount, 0)) as decimal(16,4)) AS start_amount,
            cast(SUM(coalesce(increase_qty, 0)) as decimal(16,4)) AS increase_qty,
            cast(SUM(coalesce(increase_amount, 0)) as decimal(16,4)) AS increase_amount,
            cast(SUM(coalesce(reduce_qty, 0)) as decimal(16,4)) AS reduce_qty,
            cast(SUM(coalesce(reduce_amount, 0)) as decimal(16,4)) AS reduce_amount,
            cast(SUM(coalesce(end_qty, 0)) as decimal(16,4)) AS end_qty,
            cast(SUM(coalesce(end_amount, 0)) as decimal(16,4)) AS end_amount
        FROM (
            SELECT
                main.material_id AS material_id,     -- 物料id
                main.warehouse_id AS warehouse_id,    -- 仓库id
                coalesce(start_b.start_qty, 0) AS start_qty,       -- 期初数量
                coalesce(start_b.start_amount, 0) AS start_amount, -- 期初金额
                coalesce(start_b.start_increase_qty, 0) + coalesce(stock_in.in_qty, 0) + coalesce(transfer_in.transfer_in_qty, 0) AS increase_qty,        -- 期间增加数量
                coalesce(start_b.start_increase_amount, 0) + coalesce(stock_in.in_amount, 0) + coalesce(transfer_in.transfer_in_amount, 0) AS increase_amount,     -- 期间增加金额
                coalesce(start_b.start_reduce_qty, 0) + coalesce(stock_out.out_qty, 0) + coalesce(transfer_out.transfer_out_qty, 0) AS reduce_qty,      -- 期间减少数量
                coalesce(start_b.start_reduce_amount, 0) + coalesce(stock_out.out_amount, 0) + coalesce(transfer_out.transfer_out_amount, 0) AS reduce_amount,   -- 期间减少金额
                coalesce(start_b.start_qty, 0)
                    + coalesce(start_b.start_increase_qty, 0)
                    - coalesce(start_b.start_reduce_qty, 0)
                    + coalesce(stock_in.in_qty, 0)
                    - coalesce(stock_out.out_qty, 0)
                    + coalesce(transfer_in.transfer_in_qty, 0)
                    - coalesce(transfer_out.transfer_out_qty, 0) AS end_qty,                -- 期末数量
                coalesce(start_b.start_amount, 0)
                    + coalesce(start_b.start_increase_amount, 0)
                    - coalesce(start_b.start_reduce_amount, 0)
                    + coalesce(stock_in.in_amount, 0)
                    - coalesce(stock_out.out_amount, 0)
                    + coalesce(transfer_in.transfer_in_amount, 0)
                    - coalesce(transfer_out.transfer_out_amount, 0) AS end_amount  -- 期末金额
            FROM (
                -- 结存表
                SELECT DISTINCT
                    warehouse_id, material_id
                FROM
                    wm_balance
                WHERE
                    account_period_id = #{startAccountPeriodId}

                UNION

                -- 出库单
                SELECT DISTINCT
                    so.warehouse_id,
                    sod.material_id
                FROM
                    wm_stock_out so
                    LEFT JOIN wm_stock_out_detail sod ON so.id = sod.parent_id
                WHERE
                    so.bill_status IN ('17', '7') -- 已出库/已确认
                    AND so.date BETWEEN #{startDate} and #{endDate}

                UNION

                -- 入库单
                SELECT DISTINCT
                    si.warehouse_id,
                    sid.material_id
                FROM
                    wm_stock_in si
                    LEFT JOIN wm_stock_in_detail sid ON si.id = sid.parent_id
                WHERE
                    si.bill_status IN ('27', '7') -- 已入库/已确认
                    AND si.date BETWEEN #{startDate} and #{endDate}

                UNION

                -- 调拨单（调拨入）
                SELECT DISTINCT
                    t.in_warehouse_id AS warehouse_id,
                    td.material_id
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON t.id = td.parent_id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{startDate} and #{endDate}

                UNION

                -- 调拨单（调拨出）
                SELECT DISTINCT
                    t.out_warehouse_id AS warehouse_id,
                    td.material_id
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON t.id = td.parent_id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{startDate} and #{endDate}

                ) AS main

                -- 开始会计期间的结存表
                LEFT JOIN wm_balance start_b
                    ON start_b.account_period_id = #{startAccountPeriodId}
                        AND start_b.material_id = main.material_id
                        AND start_b.warehouse_id = main.warehouse_id

                -- 出库单
                LEFT JOIN (
                    SELECT
                        so.warehouse_id,
                        sod.material_id,
                        sum(coalesce(sod.qty, 0)) AS out_qty,
                        sum(coalesce(sod.calculation_amount, 0)) AS out_amount
                    FROM
                        wm_stock_out so
                        LEFT JOIN wm_stock_out_detail sod ON sod.parent_id = so.id
                    WHERE
                         so.bill_status IN ('17', '7') -- 已出库/已确认
                        AND so.date BETWEEN #{startDate} and #{endDate}
                    GROUP BY
                        so.warehouse_id,
                        sod.material_id
                ) AS stock_out
                    ON stock_out.warehouse_id = main.warehouse_id
                        AND stock_out.material_id = main.material_id

                -- 入库单
                LEFT JOIN (
                    SELECT
                        si.warehouse_id,
                        sid.material_id,
                        sum(coalesce(sid.qty, 0)) AS in_qty,
                        sum(coalesce(sid.calculation_amount, 0)) AS in_amount
                    FROM
                        wm_stock_in si
                        LEFT JOIN wm_stock_in_detail sid ON sid.parent_id = si.id
                    WHERE
                        si.bill_status IN ('27', '7') -- 已入库/已确认
                        AND si.date BETWEEN #{startDate} and #{endDate}
                    GROUP BY
                        si.warehouse_id,
                        sid.material_id
                ) AS stock_in
                    ON stock_in.warehouse_id = main.warehouse_id
                        AND stock_in.material_id = main.material_id

                -- 调拨出库
                LEFT JOIN (
                    SELECT
                        t.out_warehouse_id AS warehouse_id,
                        td.material_id,
                        sum(coalesce(td.qty_out, 0)) AS transfer_out_qty,
                        sum(coalesce(td.calculation_amount_out, 0)) AS transfer_out_amount
                    FROM
                        wm_transfer t
                        LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                    WHERE
                        t.bill_status = '57' -- 已调拨
                        AND t.date BETWEEN #{startDate} and #{endDate}
                    GROUP BY
                        t.out_warehouse_id,
                        td.material_id
                ) AS transfer_out
                    ON transfer_out.warehouse_id = main.warehouse_id
                        AND transfer_out.material_id = main.material_id

                -- 调拨入库
                LEFT JOIN (
                    SELECT
                        t.in_warehouse_id AS warehouse_id,
                        td.material_id,
                        sum(coalesce(td.qty_in, 0)) AS transfer_in_qty,
                        sum(coalesce(td.calculation_amount_in, 0)) AS transfer_in_amount
                    FROM
                        wm_transfer t
                        LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                    WHERE
                        t.bill_status = '57' -- 已调拨
                        AND t.date BETWEEN #{startDate} and #{endDate}
                    GROUP BY
                        t.in_warehouse_id,
                        td.material_id
                ) AS transfer_in
                    ON transfer_in.warehouse_id = main.warehouse_id
                        AND transfer_in.material_id = main.material_id
        )  AS master_table
        LEFT JOIN biz_material m
            ON m.id = master_table.material_id
        LEFT JOIN biz_warehouse w
            ON w.id = master_table.warehouse_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = w.dept_id
        </if>

        <where>
            <if test="warehouseId != null  and warehouseId != ''">AND
                master_table.warehouse_id = #{warehouseId}
            </if>
            <if test="materialId != null  and materialId != ''">AND
                master_table.material_id = #{materialId}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>

        GROUP BY
            ${selectFields}
    </select>



    <select id="listSummaryReportForAllPeriodNotCheckedOut" resultType="com.ruoyi.wm.report.summaryReport.domain.SummaryReport">
        SELECT
            ${selectFields},
            cast(SUM(coalesce(start_qty, 0)) as decimal(16,4)) AS start_qty,
            cast(SUM(coalesce(start_amount, 0)) as decimal(16,4)) AS start_amount,
            cast(SUM(coalesce(increase_qty, 0)) as decimal(16,4)) AS increase_qty,
            cast(SUM(coalesce(increase_amount, 0)) as decimal(16,4)) AS increase_amount,
            cast(SUM(coalesce(reduce_qty, 0)) as decimal(16,4)) AS reduce_qty,
            cast(SUM(coalesce(reduce_amount, 0)) as decimal(16,4)) AS reduce_amount,
            cast(SUM(coalesce(end_qty, 0)) as decimal(16,4)) AS end_qty,
            cast(SUM(coalesce(end_amount, 0)) as decimal(16,4)) AS end_amount
        FROM (
            SELECT
                main.material_id,     -- 物料id
                main.warehouse_id,    -- 仓库id
                coalesce(b.end_qty, 0)
                    + coalesce(stock_in.in_qty, 0) - coalesce(stock_out.out_qty, 0)
                    + coalesce(transfer_in.transfer_in_qty, 0) - coalesce(transfer_out.transfer_out_qty, 0) AS start_qty,        -- 期初数量

                coalesce(b.end_amount, 0)
                    + coalesce(stock_in.in_amount, 0) - coalesce(stock_out.out_amount, 0)
                    + coalesce(transfer_in.transfer_in_amount, 0) - coalesce(transfer_out.transfer_out_amount, 0) AS start_amount,  -- 期初金额

                coalesce(stock_in_all.in_qty, 0) + coalesce(transfer_in_all.transfer_in_qty, 0) AS increase_qty,                          -- 期间增加数量
                coalesce(stock_in_all.in_amount, 0) + coalesce(transfer_in_all.transfer_in_amount, 0) AS increase_amount,                   -- 期间增加金额
                coalesce(stock_out_all.out_qty, 0) + coalesce(transfer_out_all.transfer_out_qty, 0) AS reduce_qty,            -- 期间减少数量
                coalesce(stock_out_all.out_amount, 0) + coalesce(transfer_out_all.transfer_out_amount, 0) AS reduce_amount,   -- 期间减少金额

                coalesce(b.end_qty, 0)
                    + coalesce(stock_in.in_qty, 0) - coalesce(stock_out.out_qty, 0)
                    + coalesce(transfer_in.transfer_in_qty, 0) - coalesce(transfer_out.transfer_out_qty, 0)
                    + (coalesce(stock_in_all.in_qty, 0) + coalesce(transfer_in_all.transfer_in_qty, 0))
                    - coalesce(stock_out_all.out_qty, 0) + coalesce(transfer_out_all.transfer_out_qty, 0) AS end_qty,           -- 期末数量

                coalesce(b.end_amount, 0)
                    + coalesce(stock_in.in_amount, 0) - coalesce(stock_out.out_amount, 0)
                    + coalesce(transfer_in.transfer_in_amount, 0) - coalesce(transfer_out.transfer_out_amount, 0)
                    + (coalesce(stock_in_all.in_amount, 0) + coalesce(transfer_in_all.transfer_in_amount, 0))
                    - (coalesce(stock_out_all.out_amount, 0) + coalesce(transfer_out_all.transfer_out_amount, 0)) AS end_amount         -- 期末金额

            FROM (
                -- 结存表
                SELECT DISTINCT
                    warehouse_id, material_id
                FROM
                    wm_balance
                WHERE
                    account_period_id = #{lastCheckedOutAccountPeriodId}

                UNION

                -- 出库单
                SELECT DISTINCT
                    so.warehouse_id,
                    sod.material_id
                FROM
                    wm_stock_out so
                    LEFT JOIN wm_stock_out_detail sod ON so.id = sod.parent_id
                WHERE
                     so.bill_status IN ('17', '7') -- 已出库/已确认
                    AND so.date BETWEEN #{lastEndDate} and #{endDate}

                UNION

                -- 入库单
                SELECT DISTINCT
                    si.warehouse_id,
                    sid.material_id
                FROM
                    wm_stock_in si
                    LEFT JOIN wm_stock_in_detail sid ON si.id = sid.parent_id
                WHERE
                    si.bill_status IN ('27', '7') -- 已入库/已确认
                    AND si.date BETWEEN #{lastEndDate} and #{endDate}

                UNION

                -- 调拨单（调拨入）
                SELECT DISTINCT
                    t.in_warehouse_id AS warehouse_id,
                    td.material_id
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON t.id = td.parent_id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{lastEndDate} and #{endDate}

                UNION

                -- 调拨单（调拨出）
                SELECT DISTINCT
                    t.out_warehouse_id AS warehouse_id,
                    td.material_id
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON t.id = td.parent_id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{lastEndDate} and #{endDate}

            ) AS main

            -- 结存表
            LEFT JOIN wm_balance b
                ON b.account_period_id = #{lastCheckedOutAccountPeriodId}
                    AND b.material_id = main.material_id
                    AND b.warehouse_id = main.warehouse_id


            -- 最晚已结账会计期间的结束时间 到 开始会计期间的开始时间
            -- 出库单
            LEFT JOIN (
                SELECT
                    so.warehouse_id,
                    sod.material_id,
                    sum(coalesce(sod.qty, 0)) AS out_qty,
                    sum(coalesce(sod.calculation_amount, 0)) AS out_amount
                FROM
                    wm_stock_out so
                    LEFT JOIN wm_stock_out_detail sod ON sod.parent_id = so.id
                WHERE
                    so.bill_status IN ('17', '7') -- 已出库/已确认
                    AND so.date BETWEEN #{lastEndDate} and #{startDate}
                GROUP BY
                    so.warehouse_id,
                    sod.material_id
            ) AS stock_out
                ON stock_out.warehouse_id = main.warehouse_id
                    AND stock_out.material_id = main.material_id

            -- 入库单
            LEFT JOIN (
                SELECT
                    si.warehouse_id,
                    sid.material_id,
                    sum(coalesce(sid.qty, 0)) AS in_qty,
                    sum(coalesce(sid.calculation_amount, 0)) AS in_amount
                FROM
                    wm_stock_in si
                    LEFT JOIN wm_stock_in_detail sid ON sid.parent_id = si.id
                WHERE
                    si.bill_status IN ('27', '7') -- 已入库/已确认
                    AND si.date BETWEEN #{lastEndDate} and #{startDate}
                GROUP BY
                    si.warehouse_id,
                    sid.material_id
            ) AS stock_in
                ON stock_in.warehouse_id = main.warehouse_id
                    AND stock_in.material_id = main.material_id

            -- 调拨出库
            LEFT JOIN (
                SELECT
                    t.out_warehouse_id AS warehouse_id,
                    td.material_id,
                    sum(coalesce(td.qty_out, 0)) AS transfer_out_qty,
                    sum(coalesce(td.calculation_amount_out, 0)) AS transfer_out_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{lastEndDate} and #{startDate}
                GROUP BY
                    t.out_warehouse_id,
                    td.material_id
            ) AS transfer_out
                ON transfer_out.warehouse_id = main.warehouse_id
                    AND transfer_out.material_id = main.material_id

            -- 调拨入库
            LEFT JOIN (
                SELECT
                    t.in_warehouse_id AS warehouse_id,
                    td.material_id,
                    sum(coalesce(td.qty_in, 0)) AS transfer_in_qty,
                    sum(coalesce(td.calculation_amount_in, 0)) AS transfer_in_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{lastEndDate} and #{startDate}
                GROUP BY
                    t.in_warehouse_id,
                    td.material_id
            ) AS transfer_in
                ON transfer_in.warehouse_id = main.warehouse_id
                    AND transfer_in.material_id = main.material_id

            -- 开始会计期间的开始时间 到 结束会计期间的结束时间
            -- 出库单
            LEFT JOIN (
                SELECT
                    so.warehouse_id,
                    sod.material_id,
                    sum(coalesce(sod.qty, 0)) AS out_qty,
                    sum(coalesce(sod.calculation_amount, 0)) AS out_amount
                FROM
                    wm_stock_out so
                    LEFT JOIN wm_stock_out_detail sod ON sod.parent_id = so.id
                WHERE
                    so.bill_status IN ('17', '7') -- 已出库/已确认
                    AND so.date BETWEEN #{startDate} and #{endDate}
                GROUP BY
                    so.warehouse_id,
                    sod.material_id
            ) AS stock_out_all
                ON stock_out_all.warehouse_id = main.warehouse_id
                    AND stock_out_all.material_id = main.material_id

            -- 入库单
            LEFT JOIN (
                SELECT
                    si.warehouse_id,
                    sid.material_id,
                    sum(coalesce(sid.qty, 0)) AS in_qty,
                    sum(coalesce(sid.calculation_amount, 0)) AS in_amount
                FROM
                    wm_stock_in si
                    LEFT JOIN wm_stock_in_detail sid ON sid.parent_id = si.id
                WHERE
                    si.bill_status IN ('27', '7') -- 已入库/已确认
                    AND si.date BETWEEN #{startDate} and #{endDate}
                GROUP BY
                    si.warehouse_id,
                    sid.material_id
            ) AS stock_in_all
                ON stock_in_all.warehouse_id = main.warehouse_id
                    AND stock_in_all.material_id = main.material_id

            -- 调拨出库
            LEFT JOIN (
                SELECT
                    t.out_warehouse_id AS warehouse_id,
                    td.material_id,
                    sum(coalesce(td.qty_out, 0)) AS transfer_out_qty,
                    sum(coalesce(td.calculation_amount_out, 0)) AS transfer_out_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{startDate} and #{endDate}
                GROUP BY
                    t.out_warehouse_id,
                    td.material_id
            ) AS transfer_out_all
                ON transfer_out_all.warehouse_id = main.warehouse_id
                    AND transfer_out_all.material_id = main.material_id

            -- 调拨入库
            LEFT JOIN (
                SELECT
                    t.in_warehouse_id AS warehouse_id,
                    td.material_id,
                    sum(coalesce(td.qty_in, 0)) AS transfer_in_qty,
                    sum(coalesce(td.calculation_amount_in, 0)) AS transfer_in_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = '57' -- 已调拨
                    AND t.date BETWEEN #{startDate} and #{endDate}
                GROUP BY
                    t.in_warehouse_id,
                    td.material_id
            ) AS transfer_in_all
                ON transfer_in_all.warehouse_id = main.warehouse_id
                    AND transfer_in_all.material_id = main.material_id
        )  AS master_table
        LEFT JOIN biz_material m
            ON m.id = master_table.material_id
        LEFT JOIN biz_warehouse w
            ON w.id = master_table.warehouse_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = w.dept_id
        </if>
        <where>
            <if test="warehouseId != null  and warehouseId != ''">AND
                master_table.warehouse_id = #{warehouseId}
            </if>
            <if test="materialId != null  and materialId != ''">AND
                master_table.material_id = #{materialId}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>

        GROUP BY
            ${selectFields}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.flowChart.mapper.FlowChartMapper">

    <select id="listFlowChart" parameterType="com.ruoyi.wm.flowChart.domain.FlowChart" resultType="com.ruoyi.wm.flowChart.domain.FlowChart">
        WITH
            -- 出库单、入库单、调拨单流水数据
            wm_bill_ledger_statistics AS (
                SELECT
                    sod.id                 AS detail_id,
                    so.warehouse_id        AS warehouse_id,
                    sod.material_id        AS material_id,
                    so.date                AS bill_date,
                    so.bill_no             AS bill_no,
                    so.origin_bill_type    AS bill_type,
                    0                      AS bill_in_qty,
                    0                      AS bill_in_amount,
                    sod.qty                AS bill_out_qty,
                    sod.calculation_amount AS bill_out_amount,
                    0                      AS end_qty,
                    0                      AS end_amount
                FROM
                    wm_stock_out so
                    LEFT JOIN wm_stock_out_detail sod ON so.id = sod.parent_id
                WHERE
                    (so.bill_status = 17 OR so.bill_status = 7) -- 已出库（蓝单）、已确认（红单）
                    AND date BETWEEN #{startDate} AND #{endDate}
                    <if test="warehouseId != null">
                        AND so.warehouse_id = #{warehouseId}
                    </if>
                    <if test="materialId != null">
                        AND sod.material_id = #{materialId}
                    </if>

                UNION

                SELECT DISTINCT
                    sid.id              AS detail_id,
                    si.warehouse_id     AS warehouse_id,
                    sid.material_id     AS material_id,
                    si.date             AS bill_date,
                    si.bill_no          AS bill_no,
                    si.origin_bill_type AS bill_type,
                    sid.qty             AS bill_in_qty,
                    sid.amount          AS bill_in_amount,
                    0                   AS bill_out_qty,
                    0                   AS bill_out_amount,
                    0                   AS end_qty,
                    0                   AS end_amount
                FROM
                    wm_stock_in si
                    LEFT JOIN wm_stock_in_detail sid ON si.id = sid.parent_id
                WHERE
                    (si.bill_status = 27 OR si.bill_status = 7) -- 已入库（蓝单）、已确认（红单）
                    AND date BETWEEN #{startDate} AND #{endDate}
                    <if test="warehouseId != null">
                        AND si.warehouse_id = #{warehouseId}
                    </if>
                    <if test="materialId != null">
                        AND sid.material_id = #{materialId}
                    </if>

                UNION

                SELECT DISTINCT
                    td.id                    AS detail_id,
                    t.in_warehouse_id        AS warehouse_id,
                    td.material_id           AS material_id,
                    t.date                   AS bill_date,
                    t.bill_no                AS bill_no,
                    t.origin_bill_type       AS bill_type,
                    td.qty_in                AS bill_in_qty,
                    td.calculation_amount_in AS bill_in_amount,
                    0                        AS bill_out_qty,
                    0                        AS bill_out_amount,
                    0                        AS end_qty,
                    0                        AS end_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = 57  -- 已调拨
                    AND date BETWEEN #{startDate} AND #{endDate}
                    <if test="warehouseId != null">
                        AND t.in_warehouse_id = #{warehouseId}
                    </if>
                    <if test="materialId != null">
                        AND td.material_id = #{materialId}
                    </if>


                UNION

                SELECT DISTINCT
                    td.id                     AS detail_id,
                    t.out_warehouse_id        AS warehouse_id,
                    td.material_id            AS material_id,
                    t.date                    AS bill_date,
                    t.bill_no                 AS bill_no,
                    t.origin_bill_type        AS bill_type,
                    0                         AS bill_in_qty,
                    0                         AS bill_in_amount,
                    td.qty_out                AS bill_out_qty,
                    td.calculation_amount_out AS bill_out_amount,
                    0                         AS end_qty,
                    0                         AS end_amount
                FROM
                    wm_transfer t
                    LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
                WHERE
                    t.bill_status = 57  -- 已调拨
                    AND date BETWEEN #{startDate} AND #{endDate}
                    <if test="warehouseId != null">
                        AND t.out_warehouse_id = #{warehouseId}
                    </if>
                    <if test="materialId != null">
                        AND td.material_id = #{materialId}
                    </if>
            ),
            wm_warehouse_material_dimension AS (
                SELECT DISTINCT
                    warehouse_id,
                    material_id
                FROM
                    wm_balance b
                WHERE
                    account_period_id = #{accountPeriodId}
                    <if test="warehouseId != null">
                        AND warehouse_id = #{warehouseId}
                    </if>
                    <if test="materialId != null">
                        AND material_id = #{materialId}
                    </if>

                UNION

                SELECT DISTINCT
                    warehouse_id,
                    material_id
                FROM
                    wm_bill_ledger_statistics
            ),
            wm_whole_balance_statistics AS (
                SELECT
                    md.warehouse_id,
                    md.material_id,
                    ap.start_date   AS bill_date,
                    '期初'      AS bill_no,
                    ''           AS bill_type,
                    0            AS bill_in_qty,
                    0            AS bill_in_amount,
                    0            AS bill_out_qty,
                    0            AS bill_out_amount,
                    b.end_qty    AS end_qty,
                    b.end_amount AS end_amount,
                    0            AS row_no
            FROM
                wm_warehouse_material_dimension md
                LEFT JOIN wm_balance b ON md.warehouse_id = b.warehouse_id AND md.material_id = b.material_id
                LEFT JOIN biz_account_period ap ON ap.id = #{accountPeriodId}
        ),
        wm_whole_ledger_statistics AS (
            SELECT
                0 as detail_id,
                warehouse_id,
                material_id,
                bill_date,
                bill_no,
                bill_type,
                bill_in_qty,
                bill_in_amount,
                bill_out_qty,
                bill_out_amount,
                end_qty,
                end_amount,
                row_no
                FROM
                    wm_whole_balance_statistics

                UNION

                SELECT
                    detail_id,
                    warehouse_id,
                    material_id,
                    bill_date,
                    bill_no,
                    bill_type,
                    bill_in_qty,
                    bill_in_amount,
                    bill_out_qty,
                    bill_out_amount,
                    end_qty,
                    end_amount,
                    ROW_NUMBER() OVER (PARTITION BY warehouse_id, material_id ORDER BY bill_date) AS row_no
                FROM
                    wm_bill_ledger_statistics
            )
        SELECT
            d.dept_id,
            d.dept_name,
            t1.warehouse_id,
            w.code AS warehouse_code,
            w.name AS warehouse_name,
            t1.material_id,
            m.code AS material_code,
            m.name AS material_name,
            m.unit AS unit,
            m.model AS model,
            t1.bill_date,
            t1.bill_no,
            t1.bill_type,
            t1.bill_in_qty,
            t1.bill_in_amount,
            t1.bill_out_qty,
            t1.bill_out_amount,
            CASE WHEN t1.row_no = 0 THEN t1.end_qty ELSE SUM(coalesce(t2.end_qty, 0)) + SUM(coalesce(t2.bill_in_qty, 0) + coalesce(t2.bill_out_qty, 0) * -1) END           AS end_qty,
            CASE WHEN t1.row_no = 0 THEN t1.end_amount ELSE SUM(coalesce(t2.end_amount, 0)) +  SUM(coalesce(t2.bill_in_amount, 0) + coalesce(t2.bill_out_amount, 0) * -1) END  AS end_amount
        FROM
            wm_whole_ledger_statistics t1
            LEFT JOIN wm_whole_ledger_statistics t2
            ON t1.material_id = t2.material_id AND t1.warehouse_id = t2.warehouse_id AND t2.row_no <![CDATA[<=]]> t1.row_no
            LEFT JOIN biz_material m ON m.id = t1.material_id
            LEFT JOIN biz_warehouse w ON w.id = t1.warehouse_id
            LEFT JOIN sys_dept d ON d.dept_id = w.dept_id
        <where>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        GROUP BY
            t1.row_no,
            d.dept_id,
            t1.warehouse_id,
            w.code,
            w.name,
            t1.material_id,
            t1.detail_id,
            m.code,
            m.name,
            t1.bill_date,
            t1.bill_no,
            t1.bill_type,
            t1.bill_in_qty,
            t1.bill_in_amount,
            t1.bill_out_qty,
            t1.bill_out_amount,
            t1.end_qty,
            t1.end_amount
        HAVING
            t1.bill_no = '期初'
            OR end_qty != 0
            OR end_amount != 0
            OR t1.bill_in_qty != 0
            OR t1.bill_in_amount != 0
            OR t1.bill_out_qty != 0
            OR t1.bill_out_amount != 0
        ORDER BY
            d.dept_id,
            w.code,
            m.code,
            t1.bill_date
    </select>

</mapper>

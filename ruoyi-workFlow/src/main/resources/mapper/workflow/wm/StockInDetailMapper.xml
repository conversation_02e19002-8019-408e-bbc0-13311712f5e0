<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.stockIn.mapper.StockInDetailMapper">
    <resultMap type="com.ruoyi.wm.stockIn.domain.StockInDetail" id="StockInDetailResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="expectedQty" column="expected_qty"/>
        <result property="qty" column="qty"/>
        <result property="price" column="price"/>
        <result property="amount" column="amount"/>
        <result property="calculationPrice" column="calculation_price"/>
        <result property="calculationAmount" column="calculation_amount"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectStockInDetailVo">
        SELECT distinct t.id,
                        t.parent_id,
                        t.material_code,
                        t.expected_qty,
                        t.qty,
                        t.price,
                        t.amount,
                        t.calculation_price,
                        t.calculation_amount,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        FROM wm_stock_in_detail t
    </sql>
    <select id="list" parameterType="StockInDetail" resultMap="StockInDetailResult">
        <include refid="selectStockInDetailVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="parentId != null ">AND
                t.parent_id = #{parentId}
            </if>
            <if test="materialCode != null ">AND
                t.material_code = #{materialCode}
            </if>
            <if test="expectedQty != null ">AND
                t.expected_qty = #{expectedQty}
            </if>
            <if test="qty != null ">AND
                t.qty = #{qty}
            </if>
            <if test="price != null ">AND
                t.price = #{price}
            </if>
            <if test="amount != null ">AND
                t.amount = #{amount}
            </if>
            <if test="calculationPrice != null ">AND
                t.calculation_price = #{calculationPrice}
            </if>
            <if test="calculationAmount != null ">AND
                t.calculation_amount = #{calculationAmount}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
    <select id="getAllStockInDetailQty" resultType="com.ruoyi.wm.stockIn.domain.StockInDetail" resultMap="StockInDetailResult">
        select
            sum(t.qty) as qty
        from wm_stock_in_detail t
        <where>
            t.source_bill_type = '11' and t.del_flag = '0'
            <if test="sourceBillId != null ">AND
                t.source_bill_id = #{sourceBillId}
            </if>
            <if test="id !=null">AND
                t.id != #{id}
            </if>
        </where>
        ORDER BY t.source_bill_type,t.source_bill_id
    </select>
    <update id="adjustAmountOfLastStockInDetailByAcctPeriodIdWhIdMatId">
        update
            wm_stock_in_detail
        set
            calculation_amount = calculation_amount + #{adjustAmount}
        where
            id = (
                select
                    sid.id
                from
                    wm_stock_in si
                left join
                    wm_stock_in_detail sid on sid.parent_id = si.id
                where
                    si.date between (
                            select start_date from biz_account_period where id = #{accountPeriodId}
                        ) and (
                            select end_date from biz_account_period where id = #{accountPeriodId}
                        )
                    and (si.bill_status = '7' or si.bill_status = '27')
                    and si.warehouse_id = #{warehouseId}
                    and sid.material_id = #{materialId}
                order by
                    si.date desc
                limit 1
            )
    </update>
</mapper>

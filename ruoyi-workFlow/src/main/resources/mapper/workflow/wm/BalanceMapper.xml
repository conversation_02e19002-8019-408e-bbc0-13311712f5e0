<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.balance.mapper.BalanceMapper">
    <resultMap type="com.ruoyi.wm.balance.domain.Balance" id="BalanceResult">
            <result property="id" column="id"/>
            <result property="accountPeriodId" column="account_period_id"/>
            <result property="startDate" column="start_date"/>
            <result property="endDate" column="end_date"/>
            <result property="warehouseId" column="warehouse_id"/>
            <result property="warehouseCode" column="warehouse_code"/>
            <result property="warehouseName" column="warehouse_name"/>
            <result property="materialId" column="material_id"/>
            <result property="materialCode" column="material_code"/>
            <result property="materialName" column="material_name"/>
            <result property="unit" column="unit"/>
            <result property="model" column="model"/>
            <result property="startQty" column="start_qty"/>
            <result property="startAmount" column="start_amount"/>
            <result property="startIncreaseQty" column="start_increase_qty"/>
            <result property="startIncreaseAmount" column="start_increase_amount"/>
            <result property="startReduceQty" column="start_reduce_qty"/>
            <result property="startReduceAmount" column="start_reduce_amount"/>
            <result property="endQty" column="end_qty"/>
            <result property="endAmount" column="end_amount"/>
            <result property="remark" column="remark"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.account_period_id,
            t.start_date,
            t.end_date,
            t.warehouse_id,
            w.code as warehouse_code,
            w.name as warehouse_name,
            t.material_id,
            m.code as material_code,
            m.name as material_name,
            m.unit as unit,
            m.model as model,
            t.start_qty,
            t.start_amount,
            t.start_increase_qty,
            t.start_increase_amount,
            t.start_reduce_qty,
            t.start_reduce_amount,
            t.end_qty,
            t.end_amount,
            t.remark,
            t.del_flag,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time
        from wm_balance t
        left join biz_warehouse w on t.warehouse_id = w.id
        left join biz_material m on t.material_id = m.id
        <!-- 数据范围过滤 -->
        <if test="params != null and params.dataScope != null and params.dataScope != '' ">
                left join sys_dept d on d.dept_id = w.dept_id
        </if>
    </sql>
    <select id="list" parameterType="com.ruoyi.wm.balance.domain.Balance" resultMap="BalanceResult">
        <include refid="selectFrom"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="accountPeriodId != null ">
                and t.account_period_id = #{accountPeriodId}
            </if>
            <if test="startDate != null ">
                and   start_date >= DATE_SUB( #{startDate}, INTERVAL DAY ( #{startDate} ) - 1 DAY )
            </if>
            <if test="endDate != null ">
                and end_date <![CDATA[<=]]>  DATE_ADD(LAST_DAY(#{endDate}), INTERVAL 1 DAY)
            </if>
            <if test="warehouseId != null ">
                and t.warehouse_id = #{warehouseId}
            </if>
            <if test="warehouseCode != null  and warehouseCode != ''">
                and t.warehouse_code like concat('%', #{warehouseCode}, '%')
            </if>
            <if test="materialId != null ">
                and t.material_id = #{materialId}
            </if>
            <if test="materialCode != null  and materialCode != ''">
                and t.material_code like concat('%', #{materialCode}, '%')
            </if>
            <if test="startQty != null ">
                and t.start_qty = #{startQty}
            </if>
            <if test="startAmount != null ">
                and t.start_amount = #{startAmount}
            </if>
            <if test="startIncreaseQty != null ">
                and t.start_increase_qty = #{startIncreaseQty}
            </if>
            <if test="startIncreaseAmount != null ">
                and t.start_increase_amount = #{startIncreaseAmount}
            </if>
            <if test="startReduceQty != null ">
                and t.start_reduce_qty = #{startReduceQty}
            </if>
            <if test="startReduceAmount != null ">
                and t.start_reduce_amount = #{startReduceAmount}
            </if>
            <if test="endQty != null ">
                and t.end_qty = #{endQty}
            </if>
            <if test="endAmount != null ">
                and t.end_amount = #{endAmount}
            </if>
            <if test="remark != null  and remark != ''">
                and t.remark = #{remark}
            </if>
            <if test="delFlag != null  and delFlag != ''">
                and t.del_flag = #{delFlag}
            </if>
            <if test="createById != null ">
                and t.create_by_id = #{createById}
            </if>
            <if test="createBy != null  and createBy != ''">
                and t.create_by = #{createBy}
            </if>
            <if test="createTime != null ">
                and t.create_time = #{createTime}
            </if>
            <if test="updateById != null ">
                and t.update_by_id = #{updateById}
            </if>
            <if test="updateBy != null  and updateBy != ''">
                and t.update_by = #{updateBy}
            </if>
            <if test="updateTime != null ">
                and t.update_time = #{updateTime}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="listBalanceByBaseAccountPeriodAndBillDateDuration" resultType="com.ruoyi.wm.balance.domain.Balance" resultMap="BalanceResult">
        SELECT
            main.material_id,
            main.warehouse_id,
            b.end_qty AS start_qty,
            b.end_amount AS start_amount,
            coalesce(stock_out.out_qty, 0) + coalesce(transfer_out.transfer_out_qty, 0) AS start_reduce_qty,
            coalesce(stock_out.out_amount, 0) + coalesce(transfer_out.transfer_out_amount, 0) AS start_reduce_amount,
            coalesce(stock_in.in_qty, 0) + coalesce(transfer_in_qty, 0) AS start_increase_qty,
            coalesce(stock_in.in_amount, 0) + coalesce(transfer_in_amount, 0) AS start_increase_amount,
            coalesce(b.end_qty, 0) - coalesce(stock_out.out_qty, 0) + coalesce(stock_in.in_qty, 0) - coalesce(transfer_out.transfer_out_qty, 0) + coalesce(transfer_in_qty, 0) AS end_qty,
            coalesce(b.end_amount, 0) - coalesce(stock_out.out_amount, 0) + coalesce(stock_in.in_amount, 0) - coalesce(transfer_out.transfer_out_amount, 0) + coalesce(transfer_in_amount, 0) AS end_amount
        -- 主维度表 （warehouse_id + material_id），结存表 union 出库单 union 入库单 union 调拨单
        FROM (
            -- 结存表
            SELECT DISTINCT
                warehouse_id,
                material_id
            FROM
                wm_balance
            WHERE
                account_period_id = #{baseAccountPeriodId}

            UNION

            -- 出库单
            SELECT DISTINCT
                so.warehouse_id,
                sod.material_id
            FROM
                wm_stock_out so
                LEFT JOIN wm_stock_out_detail sod ON so.id = sod.parent_id
            WHERE
                so.bill_status in ('17', '7')   -- 已出库 / 已确认
                AND so.date BETWEEN #{startDate} and #{endDate}
                AND so.warehouse_id is not null
                AND sod.material_id is not null

            UNION

            -- 入库单
            SELECT DISTINCT
                si.warehouse_id,
                sid.material_id
            FROM
                wm_stock_in si
                LEFT JOIN wm_stock_in_detail sid ON si.id = sid.parent_id
            WHERE
                si.bill_status in ('27', '7')  -- 已入库 / 已确认
                AND si.date BETWEEN #{startDate} and #{endDate}
                AND si.warehouse_id is not null
                AND sid.material_id is not null

            UNION

            -- 调拨单（调拨入）
            SELECT DISTINCT
                t.in_warehouse_id AS warehouse_id,
                td.material_id
            FROM
                wm_transfer t
                LEFT JOIN wm_transfer_detail td ON td.parent_id
            WHERE
                t.bill_status = '57' -- 已调拨
                AND t.date BETWEEN #{startDate} and #{endDate}
                AND t.in_warehouse_id is not null
                AND td.material_id is not null

            UNION

            -- 调拨单（调拨出）
            SELECT DISTINCT
                t.out_warehouse_id AS warehouse_id,
                td.material_id
            FROM
                wm_transfer t
                LEFT JOIN wm_transfer_detail td ON td.parent_id
            WHERE
                t.bill_status = '57' -- 已调拨
                AND t.date BETWEEN #{startDate} and #{endDate}
                AND t.out_warehouse_id is not null
                AND td.material_id is not null

        ) AS main

        -- 结存表
        LEFT JOIN wm_balance b
            ON b.account_period_id = #{baseAccountPeriodId}
                AND b.material_id = main.material_id
                AND b.warehouse_id = main.warehouse_id

        -- 出库单
        LEFT JOIN (
            SELECT
                so.warehouse_id,
                sod.material_id,
                sum(coalesce(sod.qty, 0)) AS out_qty,
                sum(coalesce(sod.calculation_amount, 0)) AS out_amount
            FROM
                wm_stock_out so
            LEFT JOIN wm_stock_out_detail sod ON sod.parent_id = so.id
            WHERE
                so.bill_status in ('17', '7') -- 已出库
                AND so.date BETWEEN #{startDate} and #{endDate}
            GROUP BY
                so.warehouse_id,
                sod.material_id
        ) AS stock_out
            ON stock_out.warehouse_id = main.warehouse_id
                AND stock_out.material_id = main.material_id

        -- 入库单
        LEFT JOIN (
            SELECT
                si.warehouse_id,
                sid.material_id,
                sum(coalesce(sid.qty, 0)) AS in_qty,
                sum(coalesce(sid.calculation_amount, 0)) AS in_amount
            FROM
                wm_stock_in si
            LEFT JOIN wm_stock_in_detail sid ON sid.parent_id = si.id
            WHERE
                si.bill_status in ('27', '7') -- 已入库
                AND si.date BETWEEN #{startDate} and #{endDate}
            GROUP BY
                si.warehouse_id,
                sid.material_id
        ) AS stock_in
            ON stock_in.warehouse_id = main.warehouse_id
                AND stock_in.material_id = main.material_id

        -- 调拨出库
        LEFT JOIN (
            SELECT
                t.out_warehouse_id AS warehouse_id,
                td.material_id,
                sum(coalesce(td.qty_out, 0)) AS transfer_out_qty,
                sum(coalesce(td.calculation_amount_out, 0)) AS transfer_out_amount
            FROM
                wm_transfer t
            LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
            WHERE
                t.bill_status = '57' -- 已调拨
                AND t.date BETWEEN #{startDate} and #{endDate}
            GROUP BY
                t.out_warehouse_id,
                td.material_id
        ) AS transfer_out
            ON transfer_out.warehouse_id = main.warehouse_id
                AND transfer_out.material_id = main.material_id

        -- 调拨入库
        LEFT JOIN (
            SELECT
                t.in_warehouse_id AS warehouse_id,
                td.material_id,
                sum(coalesce(td.qty_in, 0)) AS transfer_in_qty,
                sum(coalesce(td.calculation_amount_in, 0)) AS transfer_in_amount
            FROM
                wm_transfer t
            LEFT JOIN wm_transfer_detail td ON td.parent_id = t.id
            WHERE
                t.bill_status = '57' -- 已调拨
                AND t.date BETWEEN #{startDate} and #{endDate}
            GROUP BY
                t.in_warehouse_id,
                td.material_id
        ) AS transfer_in
            ON transfer_in.warehouse_id = main.warehouse_id
                AND transfer_in.material_id = main.material_id
    </select>

</mapper>

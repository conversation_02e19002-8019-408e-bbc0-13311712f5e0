<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.checkoutRecord.mapper.CheckoutRecordMapper">
    <resultMap type="com.ruoyi.wm.checkoutRecord.domain.CheckoutRecord" id="CheckoutRecordResult">
        <result property="id" column="id"/>
        <result property="date" column="date"/>
        <result property="accountPeriodId" column="account_period_id"/>
        <result property="materialCode" column="material_code"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="startQty" column="start_qty"/>
        <result property="startPrice" column="start_price"/>
        <result property="startAmount" column="start_amount"/>
        <result property="endQty" column="end_qty"/>
        <result property="endPrice" column="end_price"/>
        <result property="endAmount" column="end_amount"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="materialName" column="material_name"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="accountPeriodPeriodName" column="account_period_period_name"/>
    </resultMap>
    <sql id="selectCheckoutRecordVo">
        SELECT distinct t.id,
                        t.date,
                        t.account_period_id,
                        t.material_code,
                        t.warehouse_code,
                        t.start_qty,
                        t.start_price,
                        t.start_amount,
                        t.end_qty,
                        t.end_price,
                        t.end_amount,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark,
                        m.name as material_name,
                        w.name as warehouse_name,
                        p.name as account_period_period_name
        FROM wm_checkout_record t
                 LEFT JOIN biz_warehouse w ON t.warehouse_code = w.id
                 LEFT JOIN biz_material m ON t.material_code = m.id
                 LEFT JOIN biz_account_period p ON t.account_period_id = p.id
    </sql>
    <select id="list" parameterType="com.ruoyi.wm.checkoutRecord.domain.CheckoutRecord"
            resultMap="CheckoutRecordResult">
        <include refid="selectCheckoutRecordVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">
                AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="id != null ">
                AND
                t.id = #{id}
            </if>
            <if test="date != null ">
                AND
                t.date = #{date}
            </if>
            <if test="accountPeriodId != null ">
                AND
                t.account_period_id = #{accountPeriodId}
            </if>
            <if test="materialCode != null  and materialCode != ''">
                AND
                t.material_code LIKE CONCAT('%',#{materialCode},'%')
            </if>
            <if test="warehouseCode != null  and warehouseCode != ''">
                AND
                t.warehouse_code like CONCAT('%',#{warehouseCode},'%')
            </if>
            <if test="startQty != null ">
                AND
                t.start_qty = #{startQty}
            </if>
            <if test="startPrice != null ">
                AND
                t.start_price = #{startPrice}
            </if>
            <if test="startAmount != null ">
                AND
                t.start_amount = #{startAmount}
            </if>
            <if test="endQty != null ">
                AND
                t.end_qty = #{endQty}
            </if>
            <if test="endPrice != null ">
                AND
                t.end_price = #{endPrice}
            </if>
            <if test="endAmount != null ">
                AND
                t.end_amount = #{endAmount}
            </if>
            <if test="delFlag != null  and delFlag != ''">
                AND
                t.del_flag = #{delFlag}
            </if>
            <if test="createBy != null  and createBy != ''">
                AND
                t.create_by = #{createBy}
            </if>
            <if test="createById != null ">
                AND
                t.create_by_id = #{createById}
            </if>
            <if test="createTime != null ">
                AND
                t.create_time = #{createTime}
            </if>
            <if test="updateBy != null  and updateBy != ''">
                AND
                t.update_by = #{updateBy}
            </if>
            <if test="updateById != null ">
                AND
                t.update_by_id = #{updateById}
            </if>
            <if test="updateTime != null ">
                AND
                t.update_time = #{updateTime}
            </if>
            <if test="remark != null  and remark != ''">
                AND
                t.remark = #{remark}
            </if>
            <if test="materialName !=null and materialName !=''">
                AND
                m.name like CONCAT('%',#{materialName},'%')
            </if>
            <if test="warehouseName !=null and warehouseName !=''">
                AND
                w.name like CONCAT('%',#{warehouseName},'%')
            </if>
        </where>
        ORDER BY id DESC
--         , t.warehouse_code ASC , t.material_code ASC
    </select>
    <!--    最早的未结账的会计期间   -->
    <select id="findFirstByStatusAndBillStatusOrderByCreateTimeAsc"
            resultType="com.ruoyi.archives.accountPeriod.domain.AccountPeriod">
        select *
        from biz_account_period
        where checkout_flag = 0
        order by end_date asc limit 1;
    </select>

    <!--    最早的未结账的会计期间的上一条   -->
    <select id="selectLastByAccountPeriodId" resultType="com.ruoyi.archives.accountPeriod.domain.AccountPeriod">
        select *
        from biz_account_period
        where id = (select max(id) from biz_account_period where id &lt; #{id})
    </select>
    <!--    结账-->
    <select id="checkoutRecord" resultMap="CheckoutRecordResult">
        drop TEMPORARY TABLE if exists previous_accounting_period;
        drop TEMPORARY TABLE if exists initial_price_quantity_amount;
        drop TEMPORARY TABLE if exists initial_qty_amount_by_dept;
        drop TEMPORARY TABLE if exists first_stock_in_record;
        drop TEMPORARY TABLE if exists first_stock_in_amount_qty_by_dept;
        drop TEMPORARY TABLE if exists stock_in_calculation_price_by_dept;
        drop TEMPORARY TABLE if exists stock_in_record;
        drop TEMPORARY TABLE if exists stock_out_record;
        drop TEMPORARY TABLE if exists stock_in_qty;
        drop TEMPORARY TABLE if exists stock_out_qty;
        drop TEMPORARY TABLE if exists end_qty;
        drop TEMPORARY TABLE if exists end_price_by_dept;
        <!--        表若存在则清空表-->

        CREATE TEMPORARY TABLE previous_accounting_period (
        select * from wm_checkout_record
        where account_period_id =#{previousAccounting}
        );
        CREATE TEMPORARY TABLE initial_price_quantity_amount (
        select
        wm_current_stock.material_code as material_code,
        wm_current_stock.warehouse_code as warehouse_code,
        biz_warehouse.dept_id as dept_id,
        COALESCE(previous_accounting_period.end_qty,0 ) as initial_start_qty,
        COALESCE(previous_accounting_period.end_price,0 ) as initial_start_price,
        COALESCE(previous_accounting_period.end_amount ,0) as initial_start_amount
        from wm_current_stock
        left join previous_accounting_period on previous_accounting_period.material_code =wm_current_stock.material_code
        and previous_accounting_period.warehouse_code = wm_current_stock.warehouse_code
        left join biz_warehouse on biz_warehouse.code = wm_current_stock.warehouse_code
        );
        CREATE TEMPORARY TABLE initial_qty_amount_by_dept (
        select
        initial_price_quantity_amount.material_code as material_code,
        initial_price_quantity_amount.dept_id as dept_id,
        initial_price_quantity_amount.warehouse_code as warehouse_code,
        sum(COALESCE(previous_accounting_period.end_qty,0)) as initial_dept_start_qty,
        sum(COALESCE(previous_accounting_period.end_amount,0)) as initial_dept_start_amount
        from initial_price_quantity_amount
        left join previous_accounting_period on previous_accounting_period.material_code
        =initial_price_quantity_amount.material_code
        and previous_accounting_period.warehouse_code = initial_price_quantity_amount.warehouse_code
        left join biz_warehouse on biz_warehouse.code = previous_accounting_period.warehouse_code
        GROUP BY initial_price_quantity_amount.material_code,biz_warehouse.dept_id
        );
        <!--        # 完成-->
        <!--        #     ${本期大库领料单入库记录} = 根据当前会计期间开始结束时间获取的来源单据类型为大库领料单入库的且状态为已入库的入库单-->
        CREATE TEMPORARY TABLE first_stock_in_record (
        select wm_stock_in.* from wm_stock_in
        left join biz_account_period on biz_account_period.id = #{initial}
        where wm_stock_in.date >= biz_account_period.start_date
        and wm_stock_in.date &lt; biz_account_period.end_date
        and wm_stock_in.bill_status = '27' and wm_stock_in.source_bill_type = '1'
        );
        CREATE TEMPORARY TABLE first_stock_in_amount_qty_by_dept (
        select
        biz_material.code as material_code,
        biz_warehouse.dept_id as dept_id,
        sum(COALESCE(wm_stock_in_detail.amount,0)) as first_stock_in_amount,
        sum(COALESCE(wm_stock_in_detail.qty,0)) as first_stock_in_qty
        from first_stock_in_record
        left join biz_warehouse on biz_warehouse.code = first_stock_in_record.warehouse_code
        left join wm_stock_in_detail on wm_stock_in_detail.parent_id = first_stock_in_record.id
        left join biz_material on biz_material.code = wm_stock_in_detail.material_code
        group by biz_material.code,biz_warehouse.dept_id
        );
        CREATE TEMPORARY TABLE stock_in_calculation_price_by_dept (
        select
        initial_qty_amount_by_dept.material_code as material_code,
        biz_warehouse.dept_id as dept_id,
        initial_qty_amount_by_dept.warehouse_code as warehouse_code,
        CASE COALESCE(first_stock_in_amount_qty_by_dept.first_stock_in_qty,0) +
        COALESCE(initial_qty_amount_by_dept.initial_dept_start_qty,0)
        WHEN 0 THEN 0
        ELSE COALESCE ((COALESCE(first_stock_in_amount_qty_by_dept.first_stock_in_amount,0) +
        COALESCE(initial_qty_amount_by_dept.initial_dept_start_amount,0))/((COALESCE(first_stock_in_amount_qty_by_dept.first_stock_in_qty,0)
        + COALESCE(initial_qty_amount_by_dept.initial_dept_start_qty,0))),0) END as calculation_price
        from initial_qty_amount_by_dept
        left join biz_warehouse on biz_warehouse.code = initial_qty_amount_by_dept.warehouse_code
        left join wm_stock_in_detail on wm_stock_in_detail.material_code = initial_qty_amount_by_dept.material_code
        left join first_stock_in_amount_qty_by_dept on first_stock_in_amount_qty_by_dept.material_code =
        wm_stock_in_detail.material_code and first_stock_in_amount_qty_by_dept.dept_id = biz_warehouse.dept_id
        group by initial_qty_amount_by_dept.material_code,biz_warehouse.dept_id
        );
        <!--        #     ${期末单价} = ${核算单价} = 根据仓库对应部门获取 ${同部门物料的核算单价}-->
        CREATE TEMPORARY TABLE end_price_by_dept (
        select
        stock_in_calculation_price_by_dept.material_code as material_code,
        stock_in_calculation_price_by_dept.calculation_price as end_price,
        biz_warehouse.dept_id as dept_id,
        biz_warehouse.code as warehouse_code

        from stock_in_calculation_price_by_dept
        left join biz_warehouse on biz_warehouse.dept_id = stock_in_calculation_price_by_dept.dept_id
        );
        <!--        # ${本期入库单} = 根据当前会计期间开始结束时间获取的状态为已入库的入库单-->
        CREATE TEMPORARY TABLE stock_in_record (
        select * from wm_stock_in
        where wm_stock_in.date >= (select biz_account_period.start_date FROM biz_account_period where id=#{initial}) and
        wm_stock_in.date &lt; (select biz_account_period.end_date FROM biz_account_period where id=#{initial})
        and wm_stock_in.bill_status = '27'
        );
        <!--        # ${本期出库单} = 根据当前会计期间开始结束时间获取的状态为已出库的出库单-->
        CREATE TEMPORARY TABLE stock_out_record (
        select * from wm_stock_out
        where wm_stock_out.date >= (select biz_account_period.start_date FROM biz_account_period where id=#{initial})
        and wm_stock_out.date &lt; (select biz_account_period.end_date FROM biz_account_period where id=#{initial})
        and wm_stock_out.bill_status = '17'
        );
        <!--        # ${本期入库数量} = ${本期入库单}同仓库物料聚合求和数量-->

        CREATE TEMPORARY TABLE stock_in_qty (
        select
        biz_material.code as material_code,
        biz_warehouse.code as warehouse_code,
        sum(COALESCE(wm_stock_in_detail.qty,0)) as `stock_in_warehouse_qty`
        from stock_in_record
        left join biz_warehouse on biz_warehouse.code = stock_in_record.warehouse_code
        left join wm_stock_in_detail on wm_stock_in_detail.parent_id = stock_in_record.id
        left join biz_material on biz_material.code = wm_stock_in_detail.material_code
        group by biz_material.code,biz_warehouse.code
        );
        <!--        # ${本期出库数量} = ${本期出库单}同仓库物料聚合求和数量-->
        CREATE TEMPORARY TABLE stock_out_qty (
        select
        biz_material.code as material_code,
        biz_warehouse.code as warehouse_code,
        sum(COALESCE(wm_stock_out_detail.qty,0)) as `stock_out_warehouse_qty`
        from stock_out_record
        left join biz_warehouse on biz_warehouse.code = stock_out_record.warehouse_code
        left join wm_stock_out_detail on wm_stock_out_detail.parent_id= stock_out_record.id
        left join biz_material on biz_material.code = wm_stock_out_detail.material_code
        group by biz_material.code,biz_warehouse.code
        );
        <!--        # ${期末数量} = ${期初数量} + ${本期入库数量} - ${本期出库数量}-->
        CREATE TEMPORARY TABLE end_qty (
        select
        initial_price_quantity_amount.material_code as material_code,
        initial_price_quantity_amount.warehouse_code as warehouse_code,
        COALESCE(initial_price_quantity_amount.initial_start_qty,0) + COALESCE(stock_in_qty.`stock_in_warehouse_qty`,0)
        - COALESCE(stock_out_qty.`stock_out_warehouse_qty`,0) as end_qty
        from initial_price_quantity_amount
        left join stock_in_qty on stock_in_qty.material_code = initial_price_quantity_amount.material_code and
        stock_in_qty.warehouse_code = initial_price_quantity_amount.warehouse_code
        left join stock_out_qty on stock_out_qty.material_code = initial_price_quantity_amount.material_code and
        stock_out_qty.warehouse_code = initial_price_quantity_amount.warehouse_code
        );
        select
        initial_price_quantity_amount.material_code as material_code,
        initial_price_quantity_amount.warehouse_code as warehouse_code,
        initial_price_quantity_amount.dept_id as dept_id,
        biz_account_period.id as account_period_id,
        initial_price_quantity_amount.initial_start_qty as start_qty,
        initial_price_quantity_amount.initial_start_price as start_price,
        initial_price_quantity_amount.initial_start_amount as start_amount,
        end_qty.end_qty as end_qty,
        end_price_by_dept.end_price as end_price,
        end_price_by_dept.end_price * end_qty.end_qty as end_amount
        from initial_price_quantity_amount
        left join end_qty on end_qty.material_code = initial_price_quantity_amount.material_code and
        end_qty.warehouse_code = initial_price_quantity_amount.warehouse_code
        left join end_price_by_dept on end_price_by_dept.material_code = initial_price_quantity_amount.material_code and
        end_price_by_dept.warehouse_code = initial_price_quantity_amount.warehouse_code
        left join biz_account_period on biz_account_period.id=#{initial}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.stockIn.mapper.StockInMapper">
    <resultMap type="com.ruoyi.wm.stockIn.domain.StockIn" id="StockInResult">
        <result property="id" column="id"/>
        <result property="billNo" column="bill_no"/>
        <result property="date" column="date"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="refUserId" column="ref_user_id"/>
        <result property="refUserCode" column="ref_user_code"/>
        <result property="managerCode" column="manager_code"/>
        <result property="managerId" column="manager_id"/>
        <result property="blueRedFlag" column="blue_red_flag"/>
        <result property="billStatus" column="bill_status"/>
        <result property="sourceBillType" column="source_bill_type"/>
        <result property="sourceBillId" column="source_bill_id"/>
        <result property="sourceBillNo" column="source_bill_no"/>
        <result property="originBillType" column="origin_bill_type"/>
        <result property="originBillId" column="origin_bill_id"/>
        <result property="originBillNo" column="origin_bill_no"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="requestDeptId" column="request_dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="kingdeeRequserName" column="kingdee_requser_name"/>
    </resultMap>
    <sql id="selectStockInVo">
        SELECT distinct t.id,
                        t.bill_no,
                        t.date,
                        t.warehouse_id,
                        t.warehouse_code,
                        t.ref_user_id,
                        t.ref_user_code,
                        t.manager_code,
                        t.manager_id,
                        t.blue_red_flag,
                        t.bill_status,
                        t.source_bill_type,
                        t.source_bill_id,
                        t.origin_bill_type,
                        t.origin_bill_id,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark,
                        t.request_dept_id,
                        d.dept_name,
                        t.kingdee_requser_name
        FROM wm_stock_in t
        left join sys_dept d on d.dept_id = t.request_dept_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            LEFT JOIN sys_user u ON u.user_id = t.create_by_id AND u.user_name = t.create_by
        </if>
    </sql>

    <select id="list" parameterType="com.ruoyi.wm.stockIn.domain.StockIn" resultMap="StockInResult">
        <include refid="selectStockInVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="billNo != null  and billNo != ''">AND
                t.bill_no LIKE concat('%', #{billNo}, '%')
            </if>
            <if test="date != null ">AND
                t.date = #{date}
            </if>
            <if test="managerId != null  and managerId != ''">AND
                t.manager_id = #{managerId}
            </if>
            <if test="warehouseCode != null  and warehouseCode != ''">AND
                t.warehouse_code = #{warehouseCode}
            </if>
            <if test="refUserCode != null  and refUserCode != ''">AND
                t.ref_user_code = #{refUserCode}
            </if>
            <if test="managerCode != null  and managerCode != ''">AND
                t.manager_code = #{managerCode}
            </if>
            <if test="blueRedFlag != null">AND
                t.blue_red_flag = #{blueRedFlag}
            </if>
            <choose>
                <when test="billStatus != null and billStatus != ''">
                    AND t.bill_status = #{billStatus}
                </when>
                <otherwise>
                    AND t.bill_status != -1
                </otherwise>
            </choose>
            <if test="sourceBillType != null  and sourceBillType != ''">AND
                t.source_bill_type = #{sourceBillType}
            </if>
            <if test="sourceBillId != null ">AND
                t.source_bill_id = #{sourceBillId}
            </if>
            <if test="originBillType != null  and originBillType != ''">AND
                t.origin_bill_type = #{originBillType}
            </if>
            <if test="requestDeptId != null  and requestDeptId != ''">AND
                t.request_dept_id = #{requestDeptId}
            </if>
            <if test="originBillId != null ">AND
                t.origin_bill_id = #{originBillId}
            </if>
            <if test="onTime != null  and endTime != null">AND
                t.date between #{onTime} and #{endTime}
            </if>
            <if test="warehouseId != null">AND
                t.warehouse_id = #{warehouseId}
            </if>
            <if test="refUserId != null">AND
                t.ref_user_id = #{refUserId}
            </if>
            <if test="remark != null">AND
                t.remark like concat('%', #{remark}, '%')
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="getSumAbsQtyOfRedStockInDetailBySourceBillIdOfDetail" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select
            coalesce(sum(abs(coalesce(td.qty, 0))), 0) as qty
        from
            wm_stock_in_detail td
                left join
            wm_stock_in t on t.id = td.parent_id
        where
            td.source_bill_id = #{sourceBillId}
          and td.source_bill_type = 11 -- 出库单明细
          and t.blue_red_flag = 0 -- 蓝单
          and t.bill_status = 7 -- 已确认
    </select>
</mapper>

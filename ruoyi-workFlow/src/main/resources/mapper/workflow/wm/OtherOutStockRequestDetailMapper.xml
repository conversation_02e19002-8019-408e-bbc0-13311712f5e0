<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.otherOutStockRequestDetail.mapper.OtherOutStockRequestDetailMapper">
    <resultMap type="OtherOutStockRequestDetail" id="OtherOutStockRequestDetailResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="materialCode" column="material_code"/>
            <result property="materialId" column="material_id"/>
            <result property="expectedQty" column="expected_qty"/>
            <result property="qty" column="qty"/>
            <result property="price" column="price"/>
            <result property="amount" column="amount"/>
            <result property="calculationPrice" column="calculation_price"/>
            <result property="calculationAmount" column="calculation_amount"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.material_code,
            t.material_id,
            t.expected_qty,
            t.qty,
            t.price,
            t.amount,
            t.calculation_price,
            t.calculation_amount,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from wm_other_out_stock_request_detail t
        where t.id in
              (select distinct t.id
        from wm_other_out_stock_request_detail t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="OtherOutStockRequestDetail" resultMap="OtherOutStockRequestDetailResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="materialCode != null  and materialCode != ''">
                            and t.material_code = #{materialCode}
                        </if>
                        <if test="materialId != null ">
                            and t.material_id = #{materialId}
                        </if>
                        <if test="expectedQty != null ">
                            and t.expected_qty = #{expectedQty}
                        </if>
                        <if test="qty != null ">
                            and t.qty = #{qty}
                        </if>
                        <if test="price != null ">
                            and t.price = #{price}
                        </if>
                        <if test="amount != null ">
                            and t.amount = #{amount}
                        </if>
                        <if test="calculationPrice != null ">
                            and t.calculation_price = #{calculationPrice}
                        </if>
                        <if test="calculationAmount != null ">
                            and t.calculation_amount = #{calculationAmount}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

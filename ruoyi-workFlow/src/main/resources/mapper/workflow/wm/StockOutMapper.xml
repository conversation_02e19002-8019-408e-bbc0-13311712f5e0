<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.stockOut.mapper.StockOutMapper">
    <resultMap type="com.ruoyi.wm.stockOut.domain.StockOut" id="StockOutResult">
        <result property="id" column="id"/>
        <result property="billNo" column="bill_no"/>
        <result property="date" column="date"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="refUserCode" column="ref_user_code"/>
        <result property="managerCode" column="manager_code"/>
        <result property="blueRedFlag" column="blue_red_flag"/>
        <result property="billStatus" column="bill_status"/>
        <result property="sourceBillType" column="source_bill_type"/>
        <result property="sourceBillId" column="source_bill_id"/>
        <result property="sourceBillNo" column="source_bill_no"/>
        <result property="originBillType" column="origin_bill_type"/>
        <result property="originBillId" column="origin_bill_id"/>
        <result property="originBillNo" column="origin_bill_no"/>
        <result property="projectItemCode" column="project_item_code"/>
        <result property="projectItemId" column="project_item_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="warehouseId" column="warehouse_id"/>
        <result property="refUserId" column="ref_user_id"/>
        <result property="managerId" column="manager_id"/>
        <result property="vehicle" column="vehicle"/>
        <result property="costTypeValue" column="cost_type_value"/>
        <result property="workTeamId" column="work_team_id"/>
        <result property="requestDeptId" column="request_dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="kingdeeProjectId" column="kingdee_project_id"/>
        <result property="manufacturingCostId" column="manufacturing_cost_id"/>
    </resultMap>

    <sql id="selectStockOutVo">
        SELECT distinct t.id,
                        t.bill_no,
                        t.date,
                        t.warehouse_code,
                        t.ref_user_code,
                        t.manager_code,
                        t.blue_red_flag,
                        t.bill_status,
                        t.source_bill_type,
                        t.source_bill_id,
                        t.origin_bill_type,
                        t.origin_bill_id,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.project_item_id,
                        t.project_item_code,
                        t.remark,
                        t.warehouse_id,
                        t.ref_user_id,
                        t.manager_id,
                        t.vehicle,
                        t.cost_type_value,
                        t.work_team_id,
                        t.request_dept_id,
                        d.dept_name,
                        t.finance_item_id,
                        t.finance_item_code,
                        t.kingdee_project_id,
                        t.manufacturing_cost_id
            FROM wm_stock_out t
            LEFT JOIN sys_dept d ON d.dept_id = t.request_dept_id
            LEFT JOIN sys_user u ON u.user_id = t.create_by_id AND u.user_name = t.create_by
    </sql>

    <select id="list" parameterType="com.ruoyi.wm.stockOut.domain.StockOut" resultMap="StockOutResult">
        <include refid="selectStockOutVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="billNo != null  and billNo != ''">AND
                t.bill_no LIKE concat('%', #{billNo}, '%')
            </if>
            <if test="date != null ">AND
                t.date = #{date}
            </if>
            <if test="warehouseId != null  and warehouseId != ''">AND
                t.warehouse_id = #{warehouseId}
            </if>
            <if test="refUserId != null  and refUserId != ''">AND
                t.ref_user_id = #{refUserId}
            </if>
            <if test="managerId != null  and managerId != ''">AND
                t.manager_id = #{managerId}
            </if>
            <if test="blueRedFlag != null">AND
                t.blue_red_flag = #{blueRedFlag}
            </if>
            <choose>
                <when test="billStatus != null and billStatus != ''">
                    AND t.bill_status = #{billStatus}
                </when>
                <otherwise>
                    AND t.bill_status != -1
                </otherwise>
            </choose>
            <if test="sourceBillType != null  and sourceBillType != ''">AND
                t.source_bill_type = #{sourceBillType}
            </if>
            <if test="sourceBillId != null ">AND
                t.source_bill_id = #{sourceBillId}
            </if>
            <if test="originBillType != null  and originBillType != ''">AND
                t.origin_bill_type = #{originBillType}
            </if>
            <if test="requestDeptId != null  and requestDeptId != ''">AND
                t.request_dept_id = #{requestDeptId}
            </if>
            <if test="originBillId != null ">AND
                t.origin_bill_id = #{originBillId}
            </if>
            <if test="remark != null  and remark != ''">AND
                t.remark = #{remark}
            </if>
            <if test="onTime != null  and endTime != null">AND
                t.date between #{onTime} and #{endTime}
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="getSumAbsQtyOfRedStockOutDetailBySourceBillIdOfDetail" parameterType="java.lang.Long" resultType="java.math.BigDecimal">
        select
            coalesce(sum(abs(coalesce(td.qty, 0))), 0) as qty
        from
            wm_stock_out_detail td
        left join
            wm_stock_out t on t.id = td.parent_id
        where
            td.source_bill_id = #{sourceBillId}
            and td.source_bill_type = 21 -- 出库单明细
            and t.blue_red_flag = 0 -- 红单
            and t.bill_status = 7 -- 已确认
    </select>
    <select id="getMaterialOutListForExport" parameterType="com.ruoyi.wm.stockOut.domain.StockOut" resultType="com.ruoyi.wm.stockOut.domain.MaterialOutForExport">
        SELECT
            t.id,
            t.bill_no,
            t.date,
            u1.nick_name AS ref_use_name,
            u2.nick_name AS manager_name,
            d1.dept_name AS request_dept_name,
            w.code AS warehouse_code,
            w.name AS warehouse_name,
            wt.name AS work_team_name,
            v.name AS vehicle_name,
            t.cost_type_value AS cost_type,
            kp.project_name AS kingdee_project_name,
            fi.name AS finance_item_name,
            mc.manufacturing_cost_name AS manufacturing_cost_name,
            pi.name AS project_item_name,
            t.remark AS bill_remark,
            m.code AS material_code,
            m.name AS material_name,
            m.model AS model,
            m.unit AS unit,
            td.expected_qty AS expected_qty,
            td.qty AS qty,
            td.calculation_price AS calculation_price,
            td.calculation_amount AS calculation_amount,
            td.remark AS remark
        FROM
            wm_stock_out t
        LEFT JOIN
            wm_stock_out_detail td ON td.parent_id = t.id
        LEFT JOIN
            sys_user u1 ON u1.user_id = t.ref_user_id
        LEFT JOIN
            sys_user u2 ON u2.user_id = t.manager_id
        LEFT JOIN
            sys_dept d1 ON d1.dept_id = t.request_dept_id
        LEFT JOIN
            biz_warehouse w ON w.id = t.warehouse_id
        LEFT JOIN
            biz_work_team wt ON wt.id = t.work_team_id
        LEFT JOIN
            biz_vehicle v ON v.id = t.vehicle
        LEFT JOIN
            biz_kingdee_project kp ON kp.id = t.kingdee_project_id
        LEFT JOIN
            biz_finance_item fi ON fi.id = t.finance_item_id
        LEFT JOIN
            biz_manufacturing_cost mc ON mc.id = t.manufacturing_cost_id
        LEFT JOIN
            biz_project_item pi ON pi.id = t.project_item_id
        LEFT JOIN
            biz_material m ON m.id = td.material_id
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            LEFT JOIN sys_dept d on d.dept_id = t.request_dept_id
            LEFT JOIN sys_user u ON u.user_id = t.create_by_id AND u.user_name = t.create_by
        </if>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="billNo != null  and billNo != ''">AND
                t.bill_no LIKE concat('%', #{billNo}, '%')
            </if>
            <if test="date != null ">AND
                t.date = #{date}
            </if>
            <if test="warehouseId != null  and warehouseId != ''">AND
                t.warehouse_id = #{warehouseId}
            </if>
            <if test="refUserId != null  and refUserId != ''">AND
                t.ref_user_id = #{refUserId}
            </if>
            <if test="managerId != null  and managerId != ''">AND
                t.manager_id = #{managerId}
            </if>
            <if test="blueRedFlag != null">AND
                t.blue_red_flag = #{blueRedFlag}
            </if>
            <choose>
                <when test="billStatus != null and billStatus != ''">
                    AND t.bill_status = #{billStatus}
                </when>
                <otherwise>
                    AND t.bill_status != -1
                </otherwise>
            </choose>
            <if test="sourceBillType != null  and sourceBillType != ''">AND
                t.source_bill_type = #{sourceBillType}
            </if>
            <if test="sourceBillId != null ">AND
                t.source_bill_id = #{sourceBillId}
            </if>
            <if test="originBillType != null  and originBillType != ''">AND
                t.origin_bill_type = #{originBillType}
            </if>
            <if test="requestDeptId != null  and requestDeptId != ''">AND
                t.request_dept_id = #{requestDeptId}
            </if>
            <if test="originBillId != null ">AND
                t.origin_bill_id = #{originBillId}
            </if>
            <if test="remark != null  and remark != ''">AND
                t.remark = #{remark}
            </if>
            <if test="onTime != null  and endTime != null">AND
                t.date between #{onTime} and #{endTime}
            </if>
            <if test="params!=null and params.ids !=null and params.ids != ''">AND
                t.id in (${params.ids})
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
    <select id="monthlyMaterialConsumption" resultType="java.lang.String">
        -- 月度物资耗用
        SELECT
        sum( calculation_amount ) AS amount
        FROM
        wm_stock_out main
        INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
        INNER JOIN sys_dept d ON main.request_dept_id = d.dept_id
        <where>
            bill_status = 17 and
            `DATE` BETWEEN #{startDate}
            AND #{endDate}
<!--            <if test="deptId!=100">-->
<!--                and dept_id = #{deptId}-->
<!--            </if>-->
        </where>
    </select>
    <select id="analysisOfDepartmentalMaterialConsumption" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
            d.dept_name,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
                INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
                INNER JOIN sys_dept d ON main.request_dept_id = d.dept_id
        <where>
            bill_status = 17 and
            `DATE` BETWEEN #{startDate}
            AND #{endDate}
        </where>
        GROUP BY
            d.dept_name
    </select>
    <select id="spareParts" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
            item.`name` as name,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
                INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
                INNER JOIN biz_finance_item item ON main.finance_item_id = item.id
                INNER JOIN biz_material m ON m.id = body.material_id
                INNER JOIN biz_material_type mt ON m.material_type_id = mt.id
        <where>

            bill_status = 17 and
            `DATE` BETWEEN #{startDate}
            AND #{endDate}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
            AND mt.`name` LIKE '%备件%'
        </where>
        GROUP BY
            item.NAME

    </select>
    <select id="materials" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
            item.`name`,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
                INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
                INNER JOIN biz_finance_item item ON main.finance_item_id = item.id
                INNER JOIN biz_material m ON m.id = body.material_id
                INNER JOIN biz_material_type mt ON m.material_type_id = mt.id
        <where>
            bill_status = 17 and
            `DATE` BETWEEN #{startDate}
            AND #{endDate}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
            AND mt.`name` NOT LIKE '%备件%'
        </where>
        GROUP BY
            item.NAME
    </select>
    <select id="consumptionOfBulkMaterials" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
        mt.`name`,
        sum( calculation_amount ) AS amount
        FROM
        wm_stock_out so
        INNER JOIN wm_stock_out_detail sod ON so.id = sod.parent_id
        INNER JOIN biz_material m ON m.id = sod.material_id
        INNER JOIN biz_material_type mt ON m.material_type_id = mt.id
        INNER JOIN sys_dept d ON so.request_dept_id = d.dept_id
        <where>
            bill_status =17 and   (material_classification ='z001' or material_classification='z003' ) and
        DATE BETWEEN #{startDate}
        AND #{endDate}
        and bill_status = 17
            AND ( material_classification = 'z001' OR material_classification = 'z003' )
            <if test="deptId!=100">
            and request_dept_id = #{deptId}
        </if>
        </where>
        GROUP BY
            mt.`name`
        ORDER BY
            amount DESC
        LIMIT #{pageSize}
    </select>
    <!--    大宗物资耗用明细-->
    <select id="consumptionOfBulkMaterialsDetail" resultType="com.ruoyi.wm.stockOut.domain.StockOut">

        SELECT  d.dept_name,wt.`name`,sum(calculation_amount) as amount,sum(qty) as qty  from wm_stock_out main
        INNER JOIN wm_stock_out_detail body on main.id =body.parent_id
        INNER JOIN biz_material m on m.id=body.material_id
        inner JOIN biz_material_type mt on m.material_type_id=mt.id
        INNER JOIN biz_work_team wt on main.work_team_id=wt.id
        INNER JOIN sys_dept d on main.request_dept_id=d.dept_id
        <where>
            bill_status =17 and
            (material_classification ='z001' or material_classification='z003' ) and
             mt.`name`=#{materialTypeName} and
             `DATE` BETWEEN #{startDate} AND #{endDate}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
        </where>
        GROUP BY
        d.dept_name,wt.`name`
        ORDER BY
        d.dept_name,wt.`name`

    </select>
    <select id="machineSpareParts" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
            v.`name`,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
                INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
                INNER JOIN sys_dept d ON main.request_dept_id = d.dept_id
                INNER JOIN biz_equipment v ON v.id = main.vehicle
        <where>
            bill_status = 17 and
            `DATE` BETWEEN #{startDate}
            AND #{endDate}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
        </where>
        GROUP BY
            v.`name`
        ORDER BY
            amount DESC
            LIMIT #{pageSize}
    </select>
    <select id="thisYearList" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT MONTH(DATE) AS `month`,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
            INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
        <where>  bill_status = 17 and
            YEAR (DATE) = #{year}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
        </where>
        GROUP BY
            MONTH (DATE)
    </select>
    <select id="lastYearList" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT MONTH
        ( DATE ) AS `month`,
        sum( calculation_amount ) AS amount
        FROM
        wm_stock_out main
        INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
        <where> bill_status = 17 and
            YEAR ( DATE ) = #{year}
            <if test="deptId!=100">
                and request_dept_id = #{deptId}
            </if>
        </where>
        GROUP BY
        MONTH (DATE)
    </select>
    <select id="departmentalMaterialConsumption" resultType="com.ruoyi.wm.stockOut.domain.StockOut">
        SELECT
            wt.`name`,
            sum( calculation_amount ) AS amount
        FROM
            wm_stock_out main
                INNER JOIN wm_stock_out_detail body ON main.id = body.parent_id
                INNER JOIN biz_work_team wt ON main.work_team_id = wt.id
        WHERE bill_status = 17 and
            `DATE` BETWEEN #{startDate}
          AND #{endDate}
          and request_dept_id= #{deptId}

        GROUP BY
            wt.`name`
    </select>
</mapper>

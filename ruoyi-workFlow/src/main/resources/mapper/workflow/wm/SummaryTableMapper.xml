<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.wm.summaryTable.mapper.SummaryTableMapper">
    <resultMap type="com.ruoyi.wm.summaryTable.domain.SummaryTable" id="SummaryTableResult">
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="warehouseName" column="warehouse_name"/>
        <result property="materialCode" column="material_code"/>
        <result property="materialName" column="material_name"/>
        <result property="initialQuantity" column="initial_quantity"/>
        <result property="initialAmount" column="initial_amount"/>
        <result property="sidQty" column="sid_qty"/>
        <result property="sidAmount" column="sid_amount"/>
        <result property="sodQty" column="sod_qty"/>
        <result property="sodAmount" column="sod_amount"/>
        <result property="endQty" column="end_qty"/>
        <result property="endAmount" column="end_amount"/>
        <result property="accountPeriodId" column="account_period_id"/>
    </resultMap>
    <sql id="selectSummaryTableVo">
        SELECT cs.warehouse_code,
               bh.name                    as warehouse_name,
               cs.material_code,
               m.name                     as material_name,
               CASE
                   WHEN NOW() > ap.end_date THEN ap.id
                   END                    as account_period_id,
               CASE
                   WHEN ap.checkout_flag = 1 THEN COALESCE(cr.start_qty, 0)
                   ELSE COALESCE(lcr.end_qty, 0)
                   END                    as initial_quantity,
               CASE
                   WHEN ap.checkout_flag = 1 THEN COALESCE(cr.start_amount, 0)
                   ELSE COALESCE(lcr.end_amount, 0)
                   END                    as initial_amount,
               COALESCE(ip.sid_qty, 0)    as sid_qty,
               COALESCE(ip.sid_amount, 0) as sid_amount,
               COALESCE(dp.sod_qty, 0)    as sod_qty,
               COALESCE(dp.sod_amount, 0) as sod_amount,
               CASE
                   WHEN ap.checkout_flag = 1 THEN COALESCE(cr.end_qty, 0)
                   ELSE sum(COALESCE(lcr.end_qty, 0) + COALESCE(ip.sid_qty, 0) - COALESCE(dp.sod_qty, 0))
                   END                    as end_qty,
               CASE
                   WHEN ap.checkout_flag = 1 THEN COALESCE(cr.end_amount, 0)
                   ELSE sum(COALESCE(lcr.end_amount, 0) + COALESCE(ip.sid_amount, 0) - COALESCE(dp.sod_amount, 0))
                   END                    as end_amount

        FROM wm_current_stock as cs
                 LEFT JOIN biz_warehouse bh on bh.code = cs.warehouse_code
                 LEFT JOIN biz_material m on m.code = cs.material_code
                 LEFT JOIN wm_checkout_record cr
                           on cs.warehouse_code = cr.warehouse_code and cs.material_code = cr.material_code
                 LEFT JOIN wm_checkout_record lcr
                           on cs.warehouse_code = lcr.warehouse_code and cs.material_code = lcr.material_code and
                              lcr.account_period_id = (SELECT id
                                                       FROM biz_account_period
                                                       WHERE DATE_SUB(NOW(), INTERVAL 1 MONTH) > end_date
                                                         AND end_date > DATE_SUB(NOW(), INTERVAL 2 MONTH))
                 LEFT JOIN biz_account_period ap on ap.id = cr.account_period_id
                 LEFT JOIN (SELECT sid.parent_id,
                                   sid.material_code,
                                   sum(sid.qty)    as sid_qty,
                                   sum(sid.amount) as sid_amount
                            FROM wm_stock_in_detail as sid
                                     LEFT JOIN wm_stock_in as si on si.id = sid.parent_id
                                     LEFT JOIN biz_warehouse as wh on wh.code = si.warehouse_code
                                     LEFT JOIN biz_material as m on m.code = sid.material_code
                            where si.bill_status = 27
                            GROUP BY sid.material_code) as ip on cs.material_code = ip.material_code
                 LEFT JOIN (SELECT sod.parent_id,
                                   sod.material_code,
                                   sum(sod.qty)    as sod_qty,
                                   sum(sod.amount) as sod_amount
                            FROM wm_stock_out_detail as sod
                                     LEFT JOIN wm_stock_out as so on so.id = sod.parent_id
                                     LEFT JOIN biz_warehouse as wh on wh.code = so.warehouse_code
                                     LEFT JOIN biz_material as m on m.code = sod.material_code
                            where so.bill_status = 17
                            GROUP BY sod.material_code) as dp on cs.material_code = dp.material_code

    </sql>
    <select id="list" parameterType="com.ruoyi.wm.summaryTable.domain.SummaryTable" resultMap="SummaryTableResult">
        <include refid="selectSummaryTableVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(warehouse_code,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="warehouseCode != null  and warehouseCode != ''">AND
                cs.warehouse_code LIKE concat('%', #{warehouseCode}, '%')
            </if>
            <if test="warehouseName != null  and warehouseName != ''">AND
                bh.name LIKE concat('%', #{warehouseName}, '%')
            </if>
            <if test="materialCode != null  and materialCode != ''">AND
                cs.material_code LIKE concat('%', #{materialCode}, '%')
            </if>
            <if test="materialName != null  and materialName != ''">AND
                m.name LIKE concat('%', #{materialName}, '%')
            </if>
            <if test="accountPeriodId != null and accountPeriodId!='' ">AND
                cr.account_period_id = #{accountPeriodId}
            </if>
            <if test="accountPeriodId != null and accountPeriodId!='' ">AND
                ap.id = #{accountPeriodId}
            </if>
        </where>
        GROUP BY cs.warehouse_code,cs.material_code
        ORDER BY cs.warehouse_code,cs.material_code DESC
    </select>
</mapper>

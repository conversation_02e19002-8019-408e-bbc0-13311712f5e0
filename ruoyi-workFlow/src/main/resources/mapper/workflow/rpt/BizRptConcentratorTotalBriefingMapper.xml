<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rpt.mapper.BizRptConcentratorTotalBriefingMapper">
    <resultMap type="BizRptConcentratorTotalBriefing" id="BizRptConcentratorTotalBriefingResult">
        <result property="id" column="id"/>
        <result property="dataType" column="data_type"/>
        <result property="deptId" column="dept_id"/>
        <result property="dispatchDate" column="dispatch_date"/>
        <result property="runningTime" column="running_time"/>
        <result property="processingOres" column="processing_ores"/>
        <result property="rawOreGrade" column="raw_ore_grade"/>
        <result property="rawOreMetalAmount" column="raw_ore_metal_amount"/>
        <result property="concentrateAmount" column="concentrate_amount"/>
        <result property="concentrateGrade" column="concentrate_grade"/>
        <result property="concentrateMetalAmount" column="concentrate_metal_amount"/>
        <result property="tailingsAmount" column="tailings_amount"/>
        <result property="tailingsGrade" column="tailings_grade"/>
        <result property="tailingsMetalAmount" column="tailings_metal_amount"/>
        <result property="oreProcessingRecoveryRate" column="ore_processing_recovery_rate"/>
        <result property="goldConcentrateMetalAmount" column="gold_concentrate_metal_amount"/>
        <result property="goldMetalAmount" column="gold_metal_amount"/>
        <result property="remark" column="remark"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.data_type,
                        t.dept_id,
                        t.dispatch_date,
                        t.running_time,
                        t.processing_ores,
                        t.raw_ore_grade,
                        t.raw_ore_metal_amount,
                        t.concentrate_amount,
                        t.concentrate_grade,
                        t.concentrate_metal_amount,
                        t.tailings_amount,
                        t.tailings_grade,
                        t.tailings_metal_amount,
                        t.ore_processing_recovery_rate,
                        t.gold_concentrate_metal_amount,
                        t.gold_metal_amount,
                        t.remark,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.del_flag
        from biz_rpt_concentrator_total_briefing t
        where t.id in (select distinct t.id
                       from biz_rpt_concentrator_total_briefing t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRptConcentratorTotalBriefing" resultMap="BizRptConcentratorTotalBriefingResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="dispatchDate != null ">
                and t.dispatch_date = #{dispatchDate}
            </if>
            <if test="dataType != null and dataType !=''">
                and t.data_type = #{dataType}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>


    <select id="dailyProcessingCapacity" resultType="java.lang.String">
        SELECT
            sum(processing_ores)
        FROM
            `biz_rpt_concentrator_total_briefing`
        <where>
            data_type = 10
            and del_flag = 0
            <if test="deptId != 322 and deptId !=100">
                AND dept_id = #{deptId}
            </if>
            AND dispatch_date = #{dispatchDate}
        </where>

    </select>


    <select id="mineralSelectionGrade" resultType="java.lang.String">
        SELECT
            sum( raw_ore_metal_amount )/ sum( processing_ores )
        FROM
            `biz_rpt_concentrator_total_briefing`
        <where>
            data_type = 10
            and del_flag = 0
            <if test="deptId != 322 and deptId !=100">
                AND dept_id = #{deptId}
            </if>
            AND dispatch_date = #{dispatchDate}
        </where>
    </select>


    <select id="beneficiationRecovery" resultType="java.lang.String">
        SELECT
            sum( concentrate_metal_amount )* 100 / sum( raw_ore_metal_amount )
        FROM
            `biz_rpt_concentrator_total_briefing`
        <where>
            data_type = 10
            and del_flag = 0
            <if test="deptId != 322 and deptId !=100">
                AND dept_id = #{deptId}
            </if>
            AND dispatch_date = #{dispatchDate}
        </where>
    </select>
</mapper>

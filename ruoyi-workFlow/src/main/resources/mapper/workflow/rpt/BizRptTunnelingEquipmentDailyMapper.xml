<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rpt.mapper.BizRptTunnelingEquipmentDailyMapper">
    <resultMap type="BizRptTunnelingEquipmentDaily" id="BizRptTunnelingEquipmentDailyResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="dispatchDate" column="dispatch_date"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="tunnelingEquipmentValue" column="tunneling_equipment_value"/>
        <result property="workShift0Total" column="work_shift0_total"/>
        <result property="workShift1Total" column="work_shift1_total"/>
        <result property="workShift2Total" column="work_shift2_total"/>
        <result property="todayTotal" column="today_total"/>
        <result property="monthTotal" column="month_total"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.dept_id,
                        t.dispatch_date,
                        t.wellhead_id,
                        t.tunneling_equipment_value,
                        t.work_shift0_total,
                        t.work_shift1_total,
                        t.work_shift2_total,
                        t.today_total,
                        t.month_total,
                        t.remark,
                        t.del_flag,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time
        from biz_rpt_tunneling_equipment_daily t
        where t.id in (select distinct t.id
                       from biz_rpt_tunneling_equipment_daily t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRptTunnelingEquipmentDaily" resultMap="BizRptTunnelingEquipmentDailyResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="dispatchDate != null ">
                and t.dispatch_date = #{dispatchDate}
            </if>
            <if test="dispatchDate != null ">
                and t.dispatch_date = #{dispatchDate}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

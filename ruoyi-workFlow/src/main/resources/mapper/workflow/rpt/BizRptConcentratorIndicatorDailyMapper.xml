<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rpt.mapper.BizRptConcentratorIndicatorDailyMapper">
    <resultMap type="BizRptConcentratorIndicatorDaily" id="BizRptConcentratorIndicatorDailyResult">
        <result property="id" column="id"/>
        <result property="dataType" column="data_type"/>
        <result property="deptId" column="dept_id"/>
        <result property="dispatchDate" column="dispatch_date"/>
        <result property="processingOres" column="processing_ores"/>
        <result property="runningTime" column="running_time"/>
        <result property="rawOreGrade" column="raw_ore_grade"/>
        <result property="rawOreMetalAmount" column="raw_ore_metal_amount"/>
        <result property="middlingsAmount" column="middlings_amount"/>
        <result property="middlingsGrade" column="middlings_grade"/>
        <result property="middlingsMetalAmount" column="middlings_metal_amount"/>
        <result property="flotationRawOreAmount" column="flotation_raw_ore_amount"/>
        <result property="flotationRawOreGrade" column="flotation_raw_ore_grade"/>
        <result property="flotationRawOreMetalAmount" column="flotation_raw_ore_metal_amount"/>
        <result property="concentrateAmount" column="concentrate_amount"/>
        <result property="concentrateGrade" column="concentrate_grade"/>
        <result property="concentrateMetalAmount" column="concentrate_metal_amount"/>
        <result property="tailingsAmount" column="tailings_amount"/>
        <result property="tailingsGrade" column="tailings_grade"/>
        <result property="tailingsMetalAmount" column="tailings_metal_amount"/>
        <result property="flotationRawOreRecoveryRate" column="flotation_raw_ore_recovery_rate"/>
        <result property="oreProcessingRecoveryRate" column="ore_processing_recovery_rate"/>
        <result property="goldConcentrateMetalAmount" column="gold_concentrate_metal_amount"/>
        <result property="goldMetalAmount" column="gold_metal_amount"/>
        <result property="remark" column="remark"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.dept_id,
                        t.data_type,
                        t.dispatch_date,
                        t.processing_ores,
                        t.running_time,
                        t.raw_ore_grade,
                        t.raw_ore_metal_amount,
                        t.middlings_amount,
                        t.middlings_grade,
                        t.middlings_metal_amount,
                        t.flotation_raw_ore_amount,
                        t.flotation_raw_ore_grade,
                        t.flotation_raw_ore_metal_amount,
                        t.concentrate_amount,
                        t.concentrate_grade,
                        t.concentrate_metal_amount,
                        t.tailings_amount,
                        t.tailings_grade,
                        t.tailings_metal_amount,
                        t.flotation_raw_ore_recovery_rate,
                        t.ore_processing_recovery_rate,
                        t.gold_concentrate_metal_amount,
                        t.gold_metal_amount,
                        t.remark,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.del_flag
        from biz_rpt_concentrator_indicator_daily t
        where t.id in (select distinct t.id
                       from biz_rpt_concentrator_indicator_daily t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRptConcentratorIndicatorDaily"
            resultMap="BizRptConcentratorIndicatorDailyResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="dispatchDate != null ">
                and t.dispatch_date = #{dispatchDate}
            </if>
            <if test="dataType != null and dataType !=''">
                and t.data_type = #{dataType}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

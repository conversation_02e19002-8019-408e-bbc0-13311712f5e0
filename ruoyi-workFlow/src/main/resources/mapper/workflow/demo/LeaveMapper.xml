<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.demo.leave.mapper.BizLeaveMapper">
    <resultMap type="com.ruoyi.demo.leave.domain.BizLeave" id="BizLeaveResult">
        <result property="id" column="id"/>
        <result property="leaveType" column="leave_type"/>
        <result property="title" column="title"/>
        <result property="reason" column="reason"/>
        <result property="leaveStartTime" column="leave_start_time"/>
        <result property="leaveEndTime" column="leave_end_time"/>
        <result property="totalTime" column="total_time"/>
        <result property="realityStartTime" column="reality_start_time"/>
        <result property="realityEndTime" column="reality_end_time"/>
        <result property="deptId" column="dept_id"/>
        <result property="applyUserId" column="apply_user_id"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="instanceId" column="instance_id"/>
        <result property="processKey" column="process_key"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="notify" column="notify"/>
    </resultMap>

    <sql id="selectBizLeaveVo">
        select b.id, b.leave_type, b.title, b.reason, b.leave_start_time, b.leave_end_time, b.total_time,
        b.reality_start_time, b.reality_end_time, b.dept_id, b.apply_user_id, b.apply_user_name, b.apply_time,
        b.instance_id, b.process_key, b.del_flag, b.create_by_id, b.create_by, b.create_time, b.update_by,
        b.update_time, b.remark, b.status, b.notify
        from biz_leave b
        <!-- 数据范围过滤 -->
        left join sys_user u on u.user_id = b.create_by_id
        left join sys_dept d on d.dept_id = b.dept_id
    </sql>

    <select id="list" parameterType="com.ruoyi.demo.leave.domain.BizLeave" resultMap="BizLeaveResult">
        <include refid="selectBizLeaveVo"/>
        <where>
            <if test="leaveType != null  and leaveType != ''">and leave_type = #{leaveType}</if>
            <if test="title != null  and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="reason != null  and reason != ''">and reason = #{reason}</if>
            <if test="leaveStartTime != null ">and leave_start_time = #{leaveStartTime}</if>
            <if test="leaveEndTime != null ">and leave_end_time = #{leaveEndTime}</if>
            <if test="totalTime != null ">and total_time = #{totalTime}</if>
            <if test="realityStartTime != null ">and reality_start_time = #{realityStartTime}</if>
            <if test="realityEndTime != null ">and reality_end_time = #{realityEndTime}</if>
            <if test="deptId != null ">and dept_id = #{deptId}</if>
            <if test="applyUserId != null  and applyUserId != ''">and apply_user_id = #{applyUserId}</if>
            <if test="applyUserName != null  and applyUserName != ''">and apply_user_name like concat('%',
                #{applyUserName}, '%')
            </if>
            <if test="applyTime != null ">and apply_time = #{applyTime}</if>
            <if test="instanceId != null  and instanceId != ''">and instance_id = #{instanceId}</if>
            <if test="processKey != null  and processKey != ''">and process_key = #{processKey}</if>
            <if test="createById != null  and createById != ''">and create_by_id = #{createById}</if>
            <if test="status != null  and status != ''">and b.status = #{status}</if>
            <if test="notify != null  and notify != ''">and b.notify = #{notify}</if>
            <if test="mobileParams != null and mobileParams != ''">
                AND CONCAT(IFNULL(b.title,"")) LIKE CONCAT('%', #{mobileParams}, '%')
            </if>

            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        order by id desc
    </select>

    <select id="selectBizLeaveById" parameterType="Long"
            resultMap="BizLeaveResult">
        <include refid="selectBizLeaveVo"/>
        where id = #{id}
    </select>

    <insert id="insertBizLeave" parameterType="com.ruoyi.demo.leave.domain.BizLeave" useGeneratedKeys="true"
            keyProperty="id">
        insert into biz_leave
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="leaveType != null">leave_type,</if>
            <if test="title != null">title,</if>
            <if test="reason != null">reason,</if>
            <if test="leaveStartTime != null">leave_start_time,</if>
            <if test="leaveEndTime != null">leave_end_time,</if>
            <if test="totalTime != null">total_time,</if>
            <if test="realityStartTime != null">reality_start_time,</if>
            <if test="realityEndTime != null">reality_end_time,</if>
            <if test="deptId != null">dept_id,</if>
            <if test="applyUserId != null">apply_user_id,</if>
            <if test="applyUserName != null">apply_user_name,</if>
            <if test="applyTime != null">apply_time,</if>
            <if test="instanceId != null">instance_id,</if>
            <if test="processKey != null">process_key,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createById != null">create_by_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="notify != null">notify,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="leaveType != null">#{leaveType},</if>
            <if test="title != null">#{title},</if>
            <if test="reason != null">#{reason},</if>
            <if test="leaveStartTime != null">#{leaveStartTime},</if>
            <if test="leaveEndTime != null">#{leaveEndTime},</if>
            <if test="totalTime != null">#{totalTime},</if>
            <if test="realityStartTime != null">#{realityStartTime},</if>
            <if test="realityEndTime != null">#{realityEndTime},</if>
            <if test="deptId != null">#{deptId},</if>
            <if test="applyUserId != null">#{applyUserId},</if>
            <if test="applyUserName != null">#{applyUserName},</if>
            <if test="applyTime != null">#{applyTime},</if>
            <if test="instanceId != null">#{instanceId},</if>
            <if test="processKey != null">#{processKey},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createById != null">#{createById},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="notify != null">#{notify},</if>
        </trim>
    </insert>

    <update id="updateBizLeave" parameterType="com.ruoyi.demo.leave.domain.BizLeave">
        update biz_leave
        <trim prefix="SET" suffixOverrides=",">
            <if test="leaveType != null">leave_type = #{leaveType},</if>
            <if test="title != null">title = #{title},</if>
            <if test="reason != null">reason = #{reason},</if>
            <if test="leaveStartTime != null">leave_start_time = #{leaveStartTime},</if>
            <if test="leaveEndTime != null">leave_end_time = #{leaveEndTime},</if>
            <if test="totalTime != null">total_time = #{totalTime},</if>
            <if test="realityStartTime != null">reality_start_time = #{realityStartTime},</if>
            <if test="realityEndTime != null">reality_end_time = #{realityEndTime},</if>
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="applyUserId != null">apply_user_id = #{applyUserId},</if>
            <if test="applyUserName != null">apply_user_name = #{applyUserName},</if>
            <if test="applyTime != null">apply_time = #{applyTime},</if>
            <if test="instanceId != null">instance_id = #{instanceId},</if>
            <if test="processKey != null">process_key = #{processKey},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="createById != null">create_by_id = #{createById},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="notify != null">notify = #{notify},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBizLeaveById" parameterType="Long">
        delete
        from biz_leave
        where id = #{id}
    </delete>

    <delete id="deleteBizLeaveByIds" parameterType="String">
        delete from biz_leave where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>

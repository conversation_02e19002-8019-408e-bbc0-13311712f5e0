<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.demo.mobileComponentsTest.mapper.BizMobileComponentsTestMapper">
    <resultMap type="com.ruoyi.demo.mobileComponentsTest.domain.BizMobileComponentsTest"
               id="BizMobileComponentsTestResult">
        <result property="id" column="id"/>
        <result property="textInputField" column="text_input_field"/>
        <result property="textAreaField" column="text_area_field"/>
        <result property="dictSelectField" column="dict_select_field"/>
        <result property="radioButtonField" column="radio_button_field"/>
        <result property="checkBoxField" column="check_box_field"/>
        <result property="dateField" column="date_field"/>
        <result property="deptField" column="dept_field"/>
        <result property="userField" column="user_field"/>
        <result property="attachmentField" column="attachment_field"/>
        <result property="imageField" column="image_field"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="applyUserId" column="apply_user_id"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="instanceId" column="instance_id"/>
        <result property="processKey" column="process_key"/>
        <result property="status" column="status"/>
    </resultMap>
    <sql id="selectBizMobileComponentsTestVo">
        SELECT
        b.id,
        b.text_input_field,
        b.text_area_field,
        b.dict_select_field,
        b.radio_button_field,
        b.check_box_field,
        b.date_field,
        b.dept_field,
        b.user_field,
        b.attachment_field,
        b.image_field,
        b.del_flag,
        b.create_by_id,
        b.create_by,
        b.create_time,
        b.update_by_id,
        b.update_by,
        b.update_time,
        b.remark,
        b.dept_id,
        b.apply_user_id,
        b.apply_user_name,
        b.apply_time,
        b.instance_id,
        b.process_key,
        b.status
        FROM biz_mobile_components_test b
        <!-- 数据范围过滤 -->
        LEFT JOIN sys_user u ON u.user_id = b.create_by_id AND u.user_name = b.create_by
        LEFT JOIN sys_dept d ON d.dept_id = b.dept_id
    </sql>
    <select id="list" parameterType="com.ruoyi.demo.mobileComponentsTest.domain.BizMobileComponentsTest"
            resultMap="BizMobileComponentsTestResult">
        <include refid="selectBizMobileComponentsTestVo"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(b.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="textInputField != null  and textInputField != ''">AND b.text_input_field LIKE concat('%',
                #{textInputField}, '%')
            </if>
            <if test="textAreaField != null  and textAreaField != ''">AND b.text_area_field = #{textAreaField}</if>
            <if test="dictSelectField != null  and dictSelectField != ''">AND b.dict_select_field = #{dictSelectField}
            </if>
            <if test="radioButtonField != null  and radioButtonField != ''">AND b.radio_button_field =
                #{radioButtonField}
            </if>
            <if test="checkBoxField != null  and checkBoxField != ''">AND b.check_box_field = #{checkBoxField}</if>
            <if test="dateField != null ">AND b.date_field = #{dateField}</if>
            <if test="deptField != null ">AND b.dept_field = #{deptField}</if>
            <if test="userField != null ">AND b.user_field = #{userField}</if>
            <if test="attachmentField != null  and attachmentField != ''">AND b.attachment_field = #{attachmentField}
            </if>
            <if test="imageField != null  and imageField != ''">AND b.image_field = #{imageField}</if>
            <if test="createById != null  and createById != ''">AND b.create_by_id = #{createById}</if>
            <if test="updateById != null  and updateById != ''">AND b.update_by_id = #{updateById}</if>
            <if test="deptId != null ">AND b.dept_id = #{deptId}</if>
            <if test="applyUserId != null  and applyUserId != ''">AND b.apply_user_id = #{applyUserId}</if>
            <if test="applyUserName != null  and applyUserName != ''">AND b.apply_user_name LIKE concat('%',
                #{applyUserName}, '%')
            </if>
            <if test="params.beginApplyTime != null and params.beginApplyTime != '' and params.endApplyTime != null and params.endApplyTime != ''">
                AND b.apply_time BETWEEN #{params.beginApplyTime} AND #{params.endApplyTime}
            </if>
            <if test="instanceId != null  and instanceId != ''">AND b.instance_id = #{instanceId}</if>
            <if test="processKey != null  and processKey != ''">AND b.process_key = #{processKey}</if>
            <if test="status != null  and status != ''">AND b.status = #{status}</if>
            <!-- 数据范围过滤 -->
            ${params.dataScope}
        </where>
        ORDER BY b.id DESC
    </select>
</mapper>

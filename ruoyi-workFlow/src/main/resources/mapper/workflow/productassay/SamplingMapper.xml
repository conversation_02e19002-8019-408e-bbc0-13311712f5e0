<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.sampling.mapper.SamplingMapper">
    <resultMap type="com.ruoyi.productassay.sampling.domain.Sampling" id="SamplingResult">
        <result property="id" column="id"/>
        <result property="samplingTypeValue" column="sampling_type_value"/>

        <result property="paperCode" column="paper_code"/>
        <result property="samplingCode" column="sampling_code"/>
        <result property="samplingUserId" column="sampling_user_id"/>
        <result property="samplingTime" column="sampling_time"/>
        <result property="assayTypeValue" column="assay_type_value"/>
        <result property="placeId" column="place_id"/>
        <result property="explorationTypeValue" column="exploration_type_value"/>
        <result property="sampleLength" column="sample_length"/>
        <result property="drillingIs" column="drilling_is"/>
        <result property="sampleDeptId" column="sample_dept_id"/>
        <result property="sampleTypeValue" column="sample_type_value"/>
        <result property="workShiftValue" column="work_shift_value"/>
        <result property="samplingDescribe" column="sampling_describe"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="stopeLevelNum" column="stope_level_num"/>
        <result property="placeName" column="name"/>
        <result property="deptName" column="sampleDeptName"/>
        <result property="stopeLevelNum" column="stope_level_num"/>
        <result property="plangName" column="plangName"/>
        <result property="placeName" column="name"/>
        <result property="remark" column="remark"/>
        <result property="sampleDeptName" column="dept_name"/>
        <result property="assayPurposeValue" column="assay_purpose_value"/>
        <result property="assayMethodName" column="assayMethodName"/>
        <result property="grade" column="grade"/>
        <result property="totalNumber" column="totalNumber"/>
    </resultMap>
    <sql id="selectSamplingVo">
        SELECT distinct t.id,
                        t.sampling_type_value,
                        t.paper_code,
                        t.sampling_code,
                        t.sampling_user_id,
                        t.sampling_time,
                        t.assay_type_value,
                        t.place_id,
                        t.exploration_type_value,
                        t.sample_length,
                        t.drilling_is,
                        t.sample_dept_id,
                        t.sample_type_value,
                        t.work_shift_value,
                        t.sampling_describe,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.assay_purpose_value,
--                         t.audit_status_value,
                        d.dept_name as sampleDeptName
        FROM biz_sampling t
                 LEFT JOIN biz_sampling_assay_result r ON t.id = r.parent_id AND r.del_flag = 0
                 LEFT JOIN biz_sampling_assay_method m ON t.id = m.parent_id AND r.del_flag = 0
                 left join sys_user u on u.user_id = t.sampling_user_id
                 left join sys_dept d on d.dept_id = t.sample_dept_id

    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.sampling.domain.Sampling" resultMap="SamplingResult">
        <include refid="selectSamplingVo"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                       AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
<!--             # 若 samplingTypeValue 存在，则为准确查询采样-->

            <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                AND t.sampling_type_value = #{samplingTypeValue}
        <!--               # 当准确采样为物资时，需有化验结果和化验方法-->
                        <if test="samplingTypeValue == '40'">
                            AND r.parent_id = t.id AND m.parent_id = t.id
                        </if>
                    </if>
            <!---             # 若 samplingTypeValue 不存在，则为化验室化验查询(需有化验结果和化验方法)，需排除物资化验-->
                    <if test="samplingTypeValue == null || samplingTypeValue == ''">
                    AND  t.sampling_type_value != 40
                    AND r.parent_id = t.id AND m.parent_id = t.id
                    </if>
                    <if test="paperCode != null  and paperCode != ''">AND
                        t.paper_code = #{paperCode}
                    </if>
                    <if test="samplingCode != null  and samplingCode != ''">AND
                        t.sampling_code = #{samplingCode}
                    </if>
                    <if test="samplingUserId != null ">AND
                        t.sampling_user_id = #{samplingUserId}
                    </if>
                    <if test="samplingTime != null ">AND
                        t.sampling_time = #{samplingTime}
                    </if>
                    <if test="startSamplingTime != null">AND
                        t.sampling_time >= #{startSamplingTime}
                    </if>
                    <if test="endSamplingTime != null ">AND
                        t.sampling_time &lt;= #{endSamplingTime}
                    </if>
                    <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                        t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
                    </if>
                    <if test="placeId != null ">AND
                        t.place_id = #{placeId}
                    </if>
                    <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                        t.exploration_type_value = #{explorationTypeValue}
                    </if>
                    <if test="sampleLength != null  and sampleLength != ''">AND
                        t.sample_length = #{sampleLength}
                    </if>
                    <if test="drillingIs != null  and drillingIs != ''">AND
                        t.drilling_is = #{drillingIs}
                    </if>
                    <if test="sampleDeptId != null ">AND
                        t.sample_dept_id = #{sampleDeptId}
                    </if>
                    <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                        t.sample_type_value = #{sampleTypeValue}
                    </if>
                    <if test="workShiftValue != null  and workShiftValue != ''">AND
                        t.work_shift_value = #{workShiftValue}
                    </if>
                    <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                        t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
                    </if>
                </where>
                <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                    ${params.dataScope}
                </if>
                ORDER BY t.update_time DESC
            </select>
            <select id="listProspecting" resultType="com.ruoyi.productassay.sampling.domain.Sampling" resultMap="SamplingResult">
                SELECT DISTINCT
                    t.id,
                    t.sampling_type_value,
                    t.paper_code,
                    t.sampling_code,
                    t.sampling_user_id,
                    t.sampling_time,
                    t.assay_type_value,
                    t.place_id,
                    t.exploration_type_value,
                    t.sample_length,
                    t.drilling_is,
                    t.sample_dept_id,
                    t.sample_type_value,
                    t.work_shift_value,
                    t.sampling_describe,
                    t.create_by_id,
                    t.create_by,
                    t.create_time,
                    t.update_by_id,
                    t.update_by,
                    t.update_time,
                t.assay_purpose_value,
        --         t.audit_status_value,
                t.remark,
                    p.`name`,
                d.dept_name
                FROM
                    biz_sampling t
                        LEFT JOIN biz_project_item p ON t.place_id = p.id AND p.del_flag = 0
                        LEFT JOIN biz_place b ON b.id = p.place_id AND b.del_flag = 0
                        left join sys_dept d on d.dept_id = t.sample_dept_id
                <where>
                    t.del_flag = 0
                    <if test="mobileParams!= null and mobileParams!='' ">
                        AND CONCAT(IFNULL(t.id,"")) LIKE
                        CONCAT('%',#{mobileParams},'%')
                    </if>
                    <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                        AND t.sampling_type_value = #{samplingTypeValue}
                        <if test="samplingTypeValue == '40'">
                            AND r.parent_id = t.id AND m.parent_id = t.id
                        </if>
                    </if>
                    <if test="samplingTypeValue == null || samplingTypeValue == ''">AND
                        t.sampling_type_value != "40"
                        AND r.parent_id = t.id AND m.parent_id = t.id
                    </if>
                    <if test="paperCode != null  and paperCode != ''">AND
                        t.paper_code = #{paperCode}
                    </if>
                    <if test="samplingCode != null  and samplingCode != ''">AND
                        t.sampling_code = #{samplingCode}
                    </if>
                    <if test="samplingUserId != null ">AND
                        t.sampling_user_id = #{samplingUserId}
                    </if>
                    <if test="samplingTime != null ">AND
                        t.sampling_time = #{samplingTime}
                    </if>
                    <if test="startSamplingTime != null">AND
                        t.sampling_time >= #{startSamplingTime}
                    </if>
                    <if test="endSamplingTime != null ">AND
                        t.sampling_time &lt;= #{endSamplingTime}
                    </if>
                    <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                        t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
                    </if>
                    <if test="placeId != null ">AND
                        t.place_id = #{placeId}
                    </if>
                    <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                        t.exploration_type_value = #{explorationTypeValue}
                    </if>
                    <if test="sampleLength != null  and sampleLength != ''">AND
                        t.sample_length = #{sampleLength}
                    </if>
                    <if test="drillingIs != null  and drillingIs != ''">AND
                        t.drilling_is = #{drillingIs}
                    </if>
                    <if test="sampleDeptId != null ">AND
                        t.sample_dept_id = #{sampleDeptId}
                    </if>
                    <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                        t.sample_type_value = #{sampleTypeValue}
                    </if>
                    <if test="workShiftValue != null  and workShiftValue != ''">AND
                        t.work_shift_value = #{workShiftValue}
                    </if>
                    <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                        t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
                    </if>
                </where>
                <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                    ${params.dataScope}
                </if>
                ORDER BY t.create_time DESC
            </select>
            <select id="listMining" resultType="com.ruoyi.productassay.sampling.domain.Sampling" resultMap="SamplingResult">
                SELECT DISTINCT
                t.id,
                t.sampling_type_value,
                t.paper_code,
                t.sampling_code,
                t.sampling_user_id,
                t.sampling_time,
                t.assay_type_value,
                t.place_id,
                t.exploration_type_value,
                t.sample_length,
                t.drilling_is,
                t.sample_dept_id,
                t.sample_type_value,
                t.work_shift_value,
                t.sampling_describe,
                t.create_by_id,
                t.create_by,
                t.create_time,
                t.update_by_id,
                t.update_by,
                t.update_time,
                t.remark,
                t.assay_purpose_value,
        --         t.audit_status_value,
                p.`stope_level_num`,
                b.`name` as plangName,
                d.dept_name
                FROM
                biz_sampling t
                LEFT JOIN biz_stope p ON t.place_id = p.id AND p.del_flag = 0
                LEFT JOIN biz_place b ON b.id = p.place_id AND b.del_flag = 0
                left join sys_dept d on d.dept_id = t.sample_dept_id

                <where>
                     t.del_flag = 0
                    <if test="mobileParams!= null and mobileParams!='' ">
                        AND CONCAT(IFNULL(t.id,"")) LIKE
                        CONCAT('%',#{mobileParams},'%')
                    </if>
                    <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                        AND t.sampling_type_value = #{samplingTypeValue}
                        <if test="samplingTypeValue == '40'">
                            AND r.parent_id = t.id AND m.parent_id = t.id
                        </if>
                    </if>
                    <if test="samplingTypeValue == null || samplingTypeValue == ''">AND
                        t.sampling_type_value != "40"
                        AND r.parent_id = t.id AND m.parent_id = t.id
                    </if>
                    <if test="paperCode != null  and paperCode != ''">AND
                        t.paper_code = #{paperCode}
                    </if>
                    <if test="samplingCode != null  and samplingCode != ''">AND
                        t.sampling_code = #{samplingCode}
                    </if>
                    <if test="samplingUserId != null ">AND
                        t.sampling_user_id = #{samplingUserId}
                    </if>
                    <if test="samplingTime != null ">AND
                        t.sampling_time = #{samplingTime}
                    </if>
                    <if test="startSamplingTime != null">AND
                        t.sampling_time >= #{startSamplingTime}
                    </if>
                    <if test="endSamplingTime != null ">AND
                        t.sampling_time &lt;= #{endSamplingTime}
                    </if>
                    <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                        t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
                    </if>
                    <if test="placeId != null ">AND
                        t.place_id = #{placeId}
                    </if>
                    <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                        t.exploration_type_value = #{explorationTypeValue}
                    </if>
                    <if test="sampleLength != null  and sampleLength != ''">AND
                        t.sample_length = #{sampleLength}
                    </if>
                    <if test="drillingIs != null  and drillingIs != ''">AND
                        t.drilling_is = #{drillingIs}
                    </if>
                    <if test="sampleDeptId != null ">AND
                        t.sample_dept_id = #{sampleDeptId}
                    </if>
                    <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                        t.sample_type_value = #{sampleTypeValue}
                    </if>
                    <if test="workShiftValue != null  and workShiftValue != ''">AND
                        t.work_shift_value = #{workShiftValue}
                    </if>
                    <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                        t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
                    </if>
                </where>
                <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                    ${params.dataScope}
                </if>
                ORDER BY t.create_time DESC
            </select>
            <select id="listMineral" resultType="com.ruoyi.productassay.sampling.domain.Sampling"  resultMap="SamplingResult">
                SELECT DISTINCT
                t.id,
                t.sampling_type_value,
                t.paper_code,
                t.sampling_code,
                t.sampling_user_id,
                t.sampling_time,
                t.assay_type_value,
                t.place_id,
                t.exploration_type_value,
                t.sample_length,
                t.drilling_is,
                t.sample_dept_id,
                t.sample_type_value,
                t.work_shift_value,
                t.sampling_describe,
                t.create_by_id,
                t.create_by,
                t.create_time,
                t.update_by_id,
                t.update_by,
                t.update_time,
                t.remark,
                t.assay_purpose_value,
        --         t.audit_status_value,
                d.dept_name
                FROM
                biz_sampling t

                left join sys_dept d on d.dept_id = t.sample_dept_id

                <where>
                    t.del_flag = 0
                    <if test="mobileParams!= null and mobileParams!='' ">
                        AND CONCAT(IFNULL(t.id,"")) LIKE
                        CONCAT('%',#{mobileParams},'%')
                    </if>
                    <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                        AND t.sampling_type_value = #{samplingTypeValue}
                        <if test="samplingTypeValue == '40'">
                            AND r.parent_id = t.id AND m.parent_id = t.id
                        </if>
                    </if>
                    <if test="samplingTypeValue == null || samplingTypeValue == ''">AND
                        t.sampling_type_value != "40"
                        AND r.parent_id = t.id AND m.parent_id = t.id
                    </if>
                    <if test="paperCode != null  and paperCode != ''">AND
                        t.paper_code = #{paperCode}
                    </if>
                    <if test="samplingCode != null  and samplingCode != ''">AND
                        t.sampling_code = #{samplingCode}
                    </if>
                    <if test="samplingUserId != null ">AND
                        t.sampling_user_id = #{samplingUserId}
                    </if>
                    <if test="samplingTime != null ">AND
                        t.sampling_time = #{samplingTime}
                    </if>
                    <if test="startSamplingTime != null">AND
                        t.sampling_time >= #{startSamplingTime}
                    </if>
                    <if test="endSamplingTime != null ">AND
                        t.sampling_time &lt;= #{endSamplingTime}
                    </if>
                    <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                        t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
                    </if>
                    <if test="placeId != null ">AND
                        t.place_id = #{placeId}
                    </if>
                    <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                        t.exploration_type_value = #{explorationTypeValue}
                    </if>
                    <if test="sampleLength != null  and sampleLength != ''">AND
                        t.sample_length = #{sampleLength}
                    </if>
                    <if test="drillingIs != null  and drillingIs != ''">AND
                        t.drilling_is = #{drillingIs}
                    </if>
                    <if test="sampleDeptId != null ">AND
                        t.sample_dept_id = #{sampleDeptId}
                    </if>
                    <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                        t.sample_type_value = #{sampleTypeValue}
                    </if>
                    <if test="workShiftValue != null  and workShiftValue != ''">AND
                        t.work_shift_value = #{workShiftValue}
                    </if>
                    <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                        t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
                    </if>
                </where>
                <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                    ${params.dataScope}
                </if>
                ORDER BY t.create_time DESC
            </select>
            <select id="listBizMaterialAssay" resultType="com.ruoyi.productassay.sampling.domain.Sampling"  resultMap="SamplingResult">
            <!--    SELECT DISTINCT
                t.id,
                t.sampling_type_value,
                d.dept_name,
                t.paper_code,
                t.sampling_code,
                t.sampling_user_id,
                t.sampling_time,
                t.assay_type_value,
                t.place_id,
                t.exploration_type_value,
                t.sample_length,
                t.drilling_is,
                t.sample_dept_id,
                t.sample_type_value,
                t.work_shift_value,
                t.sampling_describe,
                t.create_by_id,
                t.create_by,
                t.create_time,
                t.update_by_id,
                t.update_by,
                t.update_time,
                t.assay_purpose_value,

                t.remark

                FROM
                biz_sampling t

                        left join sys_dept d on d.dept_id = t.sample_dept_id-->


        <include refid="selectSamplingVo"/>

        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                AND t.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="samplingTypeValue == null || samplingTypeValue == ''">AND
                t.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="paperCode != null  and paperCode != ''">AND
                t.paper_code = #{paperCode}
            </if>
            <if test="samplingCode != null  and samplingCode != ''">AND
                t.sampling_code = #{samplingCode}
            </if>
            <if test="samplingUserId != null ">AND
                t.sampling_user_id = #{samplingUserId}
            </if>
            <if test="samplingTime != null ">AND
                t.sampling_time = #{samplingTime}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
            </if>
            <if test="placeId != null ">AND
                t.place_id = #{placeId}
            </if>
            <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                t.exploration_type_value = #{explorationTypeValue}
            </if>
            <if test="sampleLength != null  and sampleLength != ''">AND
                t.sample_length = #{sampleLength}
            </if>
            <if test="drillingIs != null  and drillingIs != ''">AND
                t.drilling_is = #{drillingIs}
            </if>
            <if test="sampleDeptId != null ">AND
                t.sample_dept_id = #{sampleDeptId}
            </if>
            <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                t.sample_type_value = #{sampleTypeValue}
            </if>
            <if test="workShiftValue != null  and workShiftValue != ''">AND
                t.work_shift_value = #{workShiftValue}
            </if>
            <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
            </if>
            <if test="sampleDeptId != null ">AND
                d.sample_dept_id = #{sampleDeptId}
            </if>
        </where>
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        ORDER BY t.create_time DESC
    </select>
    <select id="listByMineralexperiment" resultType="com.ruoyi.productassay.sampling.domain.Sampling"  resultMap="SamplingResult">
        SELECT distinct t.id,
                        t.sampling_type_value,
                        t.paper_code,
                        t.sampling_code,
                        t.sampling_user_id,
                        t.sampling_time,
                        t.assay_type_value,
                        t.place_id,
                        t.exploration_type_value,
                        t.sample_length,
                        t.drilling_is,
                        t.sample_dept_id,
                        t.sample_type_value,
                        t.work_shift_value,
                        t.sampling_describe,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.assay_purpose_value,
--         t.audit_status_value,
                        t.remark,
                        d.dept_name as sampleDeptName
        FROM biz_sampling t
                 LEFT JOIN biz_sampling_assay_result r ON t.id = r.parent_id AND r.del_flag = 0
                 LEFT JOIN biz_sampling_assay_method m ON t.id = m.parent_id AND r.del_flag = 0
                 left join sys_user u on u.user_id = t.sampling_user_id
                 left join sys_dept d on d.dept_id = t.sample_dept_id
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="samplingTypeValue != null  and samplingTypeValue != ''">
                AND t.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="samplingTypeValue == null || samplingTypeValue == ''">AND
                t.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="paperCode != null  and paperCode != ''">AND
                t.paper_code = #{paperCode}
            </if>
            <if test="samplingCode != null  and samplingCode != ''">AND
                t.sampling_code = #{samplingCode}
            </if>
            <if test="samplingUserId != null ">AND
                t.sampling_user_id = #{samplingUserId}
            </if>
            <if test="samplingTime != null ">AND
                t.sampling_time = #{samplingTime}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
            </if>
            <if test="placeId != null ">AND
                t.place_id = #{placeId}
            </if>
            <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                t.exploration_type_value = #{explorationTypeValue}
            </if>
            <if test="sampleLength != null  and sampleLength != ''">AND
                t.sample_length = #{sampleLength}
            </if>
            <if test="drillingIs != null  and drillingIs != ''">AND
                t.drilling_is = #{drillingIs}
            </if>
            <if test="sampleDeptId != null ">AND
                t.sample_dept_id = #{sampleDeptId}
            </if>
            <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                t.sample_type_value = #{sampleTypeValue}
            </if>
            <if test="workShiftValue != null  and workShiftValue != ''">AND
                t.work_shift_value = #{workShiftValue}
            </if>
            <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
            </if>
        </where>
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        ORDER BY t.create_time DESC
    </select>
    <select id="listProductionLaboratoryReport" resultType="com.ruoyi.productassay.sampling.domain.Sampling">

        SELECT DISTINCT
        t.id,
        d.dept_name AS sampleDeptName,
        t.sample_type_value,
        t.sampling_time,
        t.work_shift_value,
        r.assay_type_value,
        r.grade
        FROM
        biz_sampling t
        INNER JOIN biz_sampling_assay_result r on t.id = r.parent_id
        LEFT JOIN sys_user u ON u.user_id = t.sampling_user_id
        LEFT JOIN sys_dept d ON d.dept_id = t.sample_dept_id
        <where>
            t.del_flag = 0 and audit_status_value=1 and
            t.id IN ( SELECT parent_id FROM biz_sampling_assay_result where del_flag = 0  GROUP BY parent_id HAVING COUNT(*) > 1 )
            <if test="mobileParams!= null and mobileParams!='' ">
                AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="month != null  and month != ''">
                AND MONTH ( t.sampling_time ) = #{month}
            </if>
            <if test="year != null  and year != ''">
                AND YEAR ( t.sampling_time ) = #{year}
            </if>

            <if test="samplingTypeValue != null  and samplingTypeValue != ''">
               and t.sampling_type_value in
                <foreach collection="samplingTypeList" open="(" close=")" item="item" separator=",">${item}</foreach>


            </if>

            <if test="paperCode != null  and paperCode != ''">AND
                t.paper_code = #{paperCode}
            </if>
            <if test="samplingCode != null  and samplingCode != ''">AND
                t.sampling_code = #{samplingCode}
            </if>
            <if test="samplingUserId != null ">AND
                t.sampling_user_id = #{samplingUserId}
            </if>
            <if test="samplingTime != null ">AND
                t.sampling_time = #{samplingTime}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
            </if>
            <if test="placeId != null ">AND
                t.place_id = #{placeId}
            </if>
            <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                t.exploration_type_value = #{explorationTypeValue}
            </if>
            <if test="sampleLength != null  and sampleLength != ''">AND
                t.sample_length = #{sampleLength}
            </if>
            <if test="drillingIs != null  and drillingIs != ''">AND
                t.drilling_is = #{drillingIs}
            </if>
            <if test="sampleDeptId != null ">AND
                t.sample_dept_id = #{sampleDeptId}
            </if>
            <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                t.sample_type_value = #{sampleTypeValue}
            </if>
            <if test="workShiftValue != null  and workShiftValue != ''">AND
                t.work_shift_value = #{workShiftValue}
            </if>
            <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
            </if>
        </where>

        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        order by t.id  desc,t.sampling_time desc

    </select>
    <select id="listByMineralexperimentReport" resultType="com.ruoyi.productassay.sampling.domain.Sampling">
        SELECT
        am.`name` AS assayMethodName,
        d.dept_name,
        t.sampling_time,
        SUM(m.number) AS totalNumber
        FROM
        biz_sampling t
        INNER JOIN biz_sampling_assay_method m ON t.id = m.parent_id
        LEFT JOIN biz_assay_method am ON am.id = m.assay_method_id
        LEFT JOIN sys_dept d ON d.dept_id = t.sample_dept_id
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">
                AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="month != null  and month != ''">
                AND MONTH ( t.sampling_time ) = #{month}
            </if>
            <if test="year != null  and year != ''">
                AND YEAR ( t.sampling_time ) = #{year}
            </if>
            <if test="paperCode != null  and paperCode != ''">AND
                t.paper_code = #{paperCode}
            </if>
            <if test="samplingCode != null  and samplingCode != ''">AND
                t.sampling_code = #{samplingCode}
            </if>
            <if test="samplingUserId != null ">AND
                t.sampling_user_id = #{samplingUserId}
            </if>
            <if test="samplingTime != null ">AND
                t.sampling_time = #{samplingTime}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                t.assay_type_value LIKE CONCAT('%',#{assayTypeValue},'%')
            </if>
            <if test="placeId != null ">AND
                t.place_id = #{placeId}
            </if>
            <if test="explorationTypeValue != null  and explorationTypeValue != ''">AND
                t.exploration_type_value = #{explorationTypeValue}
            </if>
            <if test="sampleLength != null  and sampleLength != ''">AND
                t.sample_length = #{sampleLength}
            </if>
            <if test="drillingIs != null  and drillingIs != ''">AND
                t.drilling_is = #{drillingIs}
            </if>
            <if test="sampleDeptId != null ">AND
                t.sample_dept_id = #{sampleDeptId}
            </if>
            <if test="sampleTypeValue != null  and sampleTypeValue != ''">AND
                t.sample_type_value = #{sampleTypeValue}
            </if>
            <if test="workShiftValue != null  and workShiftValue != ''">AND
                t.work_shift_value = #{workShiftValue}
            </if>
            <if test="samplingDescribe != null  and samplingDescribe != ''">AND
                t.sampling_describe LIKE CONCAT('%',#{samplingDescribe},'%')
            </if>
            <if test="startDate !=null and endDate !=null ">
               AND t.sampling_time BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        GROUP BY
            am.`name`,
            d.dept_name,
            t.sampling_time
    </select>
    <select id="listSamplingTime" resultType="java.lang.String">
        SELECT DISTINCT sampling_time FROM sampling_assay_method_view
        <where>
           sampling_time &gt;= #{startDate} and sampling_time &lt;= #{endDate}

    </where>
        order by sampling_time asc
    </select>

    <select id="listByMineralexperimentForReport" resultType="com.alibaba.fastjson2.JSONObject">
        <![CDATA[
        SELECT IFNULL(name_deptname, '合计') AS "部门  |  化验方法",
    ]]>
        <foreach collection="columnsList" item="item" separator=",">
            SUM(IF(sampling_time = #{item}, totalNumber, 0)) AS #{item}
        </foreach>
        <![CDATA[
        , SUM(totalNumber) AS 合计
        FROM sampling_assay_method_view
    ]]>
        <where>
            <!-- 确保 name_deptname 不为空 -->
            AND name_deptname IS NOT NULL
            <if test="startDate != null">
                AND sampling_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND sampling_time <![CDATA[<=]]> #{endDate}
            </if>
        </where>
        <![CDATA[
        GROUP BY name_deptname WITH ROLLUP
    ]]>
    </select>

    <!--     where  sampling_time between #{startDate} and  #{endDate} group by name_deptname WITH ROLLUP  ]]>
SET @EE='';'2025-01-01'between '2025-01-01' and '2025-01-31'  #{startDate}#{endDate}

where sampling_time between '2025-01-01' and '2025-01-31'

select @EE :=CONCAT(@EE,'sum(if(sampling_time= ''',sampling_time,''',totalNumber,0)) as ''',sampling_time, ''',') AS aa FROM (SELECT DISTINCT sampling_time FROM biz_sampling_view) A ;

SET @QQ = CONCAT('select ifnull(name_deptname,''TOTAL1'')as name_deptname,',@EE,' sum(totalNumber) as TOTAL2 from biz_sampling_view group by name_deptname WITH ROLLUP');


PREPARE stmt FROM @QQ;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;


CREATE VIEW biz_sampling_view as (

SELECT
CONCAT(d.dept_name,'-',am.`name`)AS name_deptname,

 d.dept_name,am.`name`,
	t.sampling_time,
	SUM( m.number ) AS totalNumber
FROM
	biz_sampling t
	INNER JOIN biz_sampling_assay_method m ON t.id = m.parent_id
	LEFT JOIN biz_assay_method am ON am.id = m.assay_method_id
	LEFT JOIN sys_dept d ON d.dept_id = t.sample_dept_id
	WHERE  t.del_flag = 0
GROUP BY
name_deptname,d.dept_name,am.`name`,
	t.sampling_time

)



SET @EE='';

select @EE :=CONCAT(@EE,'sum(if(sampling_time= ''',sampling_time,''',totalNumber,0)) as ''',sampling_time, ''',') AS aa FROM (SELECT DISTINCT sampling_time FROM biz_sampling_view) A ;

SET @QQ = CONCAT('select ifnull(name_deptname,''TOTAL1'')as name_deptname,',@EE,' sum(totalNumber) as TOTAL2 from biz_sampling_view group by name_deptname WITH ROLLUP');


PREPARE stmt FROM @QQ;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
    -->



</mapper>

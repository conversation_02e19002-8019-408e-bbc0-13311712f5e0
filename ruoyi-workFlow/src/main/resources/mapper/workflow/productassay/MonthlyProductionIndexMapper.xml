<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.bizMonthlyProductionIndex.mapper.MonthlyProductionIndexMapper">
    <resultMap type="com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex" id="MonthlyProductionIndexResult">
            <result property="id" column="id"/>
            <result property="name" column="name"/>
            <result property="typeName" column="type_name"/>
            <result property="measurementUnitValue" column="measurement_unit_value"/>
            <result property="currentUseValue" column="current_use_value"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="deptType" column="dept_type"/>
            <result property="createTime" column="create_time"/>
            <result property="orderIndex" column="order_index"/>
            <result property="groupPlanTarget" column="group_plan_target"/>
            <result property="internalTarget" column="internal_target"/>
            <result property="lastLastMonthEndPassdownPlan" column="last_last_month_end_passdown_plan"/>
            <result property="lastLastMonthEndPassdownCompl" column="last_last_month_end_passdown_compl"/>
            <result property="lastMonthPlan" column="last_month_plan"/>
            <result property="lastMonthExpectCompl" column="last_month_expect_compl"/>
            <result property="lastMonthComplRate" column="last_month_compl_rate"/>
            <result property="lastMonthEndExpectTotalCompl" column="last_month_end_expect_total_compl"/>
            <result property="lastMonthEndExpectTotalComplRate" column="last_month_end_expect_total_compl_rate"/>
            <result property="curMonthTask" column="cur_month_task"/>
            <result property="curMonthFightTarget" column="cur_month_fight_target"/>
            <result property="curMonthEndExpectTotalCompl" column="cur_month_end_expect_total_compl"/>
            <result property="curMonthEndExpectTotalComplRate" column="cur_month_end_expect_total_comp_rate"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.name,
            t.type_name,
            t.dept_type,
            t.current_use_value,
            t.measurement_unit_value,
            t.del_flag,
            t.group_plan_target,
            t.internal_target,
            t.last_last_month_end_passdown_plan,
            t.last_last_month_end_passdown_compl,
            t.last_month_plan,
            t.last_month_expect_compl,
            t.last_month_compl_rate,
            t.last_month_end_expect_total_compl,
            t.last_month_end_expect_total_compl_rate,
            t.cur_month_task,
            t.cur_month_fight_target,
            t.cur_month_end_expect_total_compl,
            t.cur_month_end_expect_total_compl_rate,
            t.create_by,
            t.create_by_id,
            t.order_index,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from biz_monthly_production_index t
        where t.id in
              (select distinct t.id
        from biz_monthly_production_index t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.order_index asc
        )
        group by t.id
        order by t.order_index asc
    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex" resultMap="MonthlyProductionIndexResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
                        <if test="typeName != null  and typeName != ''">
                            and t.type_name = #{typeName}
                        </if>
                        <if test="measurementUnitValue != null  and measurementUnitValue != ''">
                            and t.measurement_unit_value = #{measurementUnitValue}
                        </if>
                        <if test="delFlag != null  and delFlag != ''">
                            and t.del_flag = #{delFlag}
                        </if>
                        <if test="orderIndex != null  and orderIndex != ''">
                            and t.order_index = #{orderIndex}
                        </if>
                        <if test="createBy != null  and createBy != ''">
                            and t.create_by = #{createBy}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="createTime != null ">
                            and t.create_time = #{createTime}
                        </if>
                        <if test="updateBy != null  and updateBy != ''">
                            and t.update_by = #{updateBy}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="updateTime != null ">
                            and t.update_time = #{updateTime}
                        </if>
                        <if test="remark != null  and remark != ''">
                            and t.remark = #{remark}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

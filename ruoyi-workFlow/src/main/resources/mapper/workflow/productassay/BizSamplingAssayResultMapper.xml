<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.bizSamplingAssayResult.mapper.BizSamplingAssayResultMapper">
    <resultMap type="com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult"
               id="BizSamplingAssayResultResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="assayTypeValue" column="assay_type_value"/>
        <result property="assayUserId" column="assay_user_id"/>
        <result property="assayTime" column="assay_time"/>
        <result property="auditStatusValue" column="audit_status_value"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditById" column="audit_by_id"/>
        <result property="auditBy" column="audit_by"/>
        <result property="unauditTime" column="unaudit_time"/>
        <result property="unauditById" column="unaudit_by_id"/>
        <result property="unauditBy" column="unaudit_by"/>
        <result property="grade" column="grade"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectBizSamplingAssayResultVo">
        SELECT distinct t.id,
                        t.parent_id,
                        t.assay_type_value,
                        t.assay_user_id,
                        t.assay_time,
                        t.grade,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag,
                        s.sampling_type_value as samplingTypeValue,
                        s.paper_code as paperCode,
                        s.sampling_code as samplingCode,
                        s.sampling_user_id as samplingUserId,
                        s.sampling_time as samplingTime
        FROM biz_sampling_assay_result t
        inner join biz_sampling s on t.parent_id=s.id and s.del_flag=0
        LEFT OUTER JOIN sys_dept d ON d.dept_id = (
            SELECT p.dept_id
            FROM biz_place p
            WHERE p.id = s.place_id
        )
    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult"
            resultMap="BizSamplingAssayResultResult">
        <include refid="selectBizSamplingAssayResultVo"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="parentId != null ">AND
                t.parent_id = #{parentId}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                t.assay_type_value = #{assayTypeValue}
            </if>
            <if test="assayUserId != null  and assayUserId != ''">AND
                t.assay_user_id = #{assayUserId}
            </if>
            <if test="assayTime != null ">AND
                t.assay_time = #{assayTime}
            </if>
            <if test="grade != null ">AND
                t.grade = #{grade}
            </if>
            <if test="samplingCode != null and samplingCode!=''">AND
                s.sampling_code = #{samplingCode}
            </if>
            <if test="paperCode != null and paperCode!=''">AND
                s.paper_code LIKE CONCAT('%',#{paperCode},'%')
            </if>
            <if test="samplingUserId != null ">AND
                s.sampling_user_id = #{samplingUserId}
            </if>
            <if test="samplingTime != null ">AND
                s.sampling_time = #{samplingTime}
            </if>
            <if test="samplingTypeValue != null  and samplingTypeValue != ''">AND
                s.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="deptId != null">AND
                d.dept_id = #{deptId}
            </if>
        </where>
        ORDER BY s.paper_code DESC
    </select>

    <select id="listSamplingAssayResult" resultType="com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult">

        SELECT
            r.id,
            t.sampling_type_value,
            t.paper_code,
            t.sampling_code,
            t.sampling_user_id,
            t.sample_type_value,
            u.nick_name as sampling_user_name,
t.work_shift_value,
            d.dept_name as sampling_dept_name,
            u3.nick_name as assay_user_name,
            t.sampling_time,
            t.place_id,
            t.exploration_type_value,
            t.sample_length,
            t.drilling_is,
            t.sample_dept_id,
            t.sample_type_value,
            t.work_shift_value,
            t.sampling_describe,
            t.assay_purpose_value,
            r.audit_status_value,
            r.assay_type_value,
            r.grade,
            r.assay_time,

            r.audit_by_id,
            r.audit_time,
            u1.nick_name as audit_by,
            r.unaudit_by_id,
            r.unaudit_time,
            u2.nick_name as unaudit_by,
            t.remark
        FROM
            biz_sampling t
                INNER JOIN biz_sampling_assay_result r ON t.id = r.parent_id
                left join sys_user u on u.user_id=t.sampling_user_id
                left join sys_user u1 on u1.user_id=r.audit_by_id
                left join sys_user u2 on u2.user_id=r.unaudit_by_id
                left join sys_user u3 on u3.user_id=r.assay_user_id
        left join sys_dept d on d.dept_id=t.sample_dept_id
        <where>
<!--             sampling_type_value != 40 and-->
              r.del_flag = 0
            AND t.del_flag = 0
            <if test="paperCode != null  and paperCode != ''">AND
                t.paper_code = #{paperCode}
            </if>
            <if test="samplingCode != null  and samplingCode != ''">AND
                t.sampling_code = #{samplingCode}
            </if>
            <if test="samplingUserId != null ">AND
                t.sampling_user_id = #{samplingUserId}
            </if>
            <if test="sampleDeptId != null ">AND
                t.sample_dept_id = #{sampleDeptId}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
            <if test="assayTypeValue != null  and assayTypeValue != ''">AND
                r.assay_type_value in (#{assayTypeValue})
            </if>
            <if test="samplingTypeValue != null  and samplingTypeValue != ''">AND
                t.sampling_type_value = #{samplingTypeValue}
            </if>
            <if test="auditStatusValue != null  and auditStatusValue != ''">AND
                r.audit_status_value in (#{auditStatusValue})
            </if>

        </where>

        order by  t.id desc,r.id asc

    </select>
</mapper>

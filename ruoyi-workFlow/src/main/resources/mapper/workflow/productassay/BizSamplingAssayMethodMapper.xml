<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.bizSamplingAssayMethod.mapper.BizSamplingAssayMethodMapper">
    <resultMap type="com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod"
               id="BizSamplingAssayMethodResult">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="assayMethodId" column="assay_method_id"/>
        <result property="cost" column="cost"/>
        <result property="number" column="number"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectBizSamplingAssayMethodVo">
        SELECT distinct t.id,
                        t.parent_id,
                        t.assay_method_id,
                        t.cost,
                        t.number,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag
        FROM biz_sampling_assay_method t
    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod"
            resultMap="BizSamplingAssayMethodResult">
        <include refid="selectBizSamplingAssayMethodVo"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="parentId != null ">AND
                t.parent_id = #{parentId}
            </if>
            <if test="assayMethodId != null  and assayMethodId != ''">AND
                t.assay_method_id = #{assayMethodId}
            </if>
            <if test="cost != null ">AND
                t.cost = #{cost}
            </if>
            <if test="number != null ">AND
                t.number = #{number}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.mapper.RptMonthlyProductionIndexAnalysisSubMapper">
    <resultMap type="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub" id="RptMonthlyProductionIndexAnalysisSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="proIndexId" column="pro_index_id"/>
            <result property="groupPlanTarget" column="group_plan_target"/>
            <result property="internalTarget" column="internal_target"/>
            <result property="lastLastMonthEndPassdownPlan" column="last_last_month_end_passdown_plan"/>
            <result property="lastLastMonthEndPassdownCompl" column="last_last_month_end_passdown_compl"/>
            <result property="lastMonthPlan" column="last_month_plan"/>
            <result property="lastMonthExpectCompl" column="last_month_expect_compl"/>
            <result property="lastMonthComplRate" column="last_month_compl_rate"/>
            <result property="lastMonthEndExpectTotalCompl" column="last_month_end_expect_total_compl"/>
            <result property="lastMonthEndExpectTotalComplRate" column="last_month_end_expect_total_compl_rate"/>
            <result property="curMonthTask" column="cur_month_task"/>
            <result property="curMonthFightTarget" column="cur_month_fight_target"/>
            <result property="curMonthEndExpectTotalCompl" column="cur_month_end_expect_total_compl"/>
            <result property="curMonthEndExpectTotalComplRate" column="cur_month_end_expect_total_compl_rate"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
        <result property="proIndexTypeName" column="pro_index_type_name"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="proIndexName" column="pro_index_name"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.pro_index_id,
            t.pro_index_type_name,
            t.pro_index_name,
            t.group_plan_target,
            t.internal_target,
            t.last_last_month_end_passdown_plan,
            t.last_last_month_end_passdown_compl,
            t.last_month_plan,
            t.last_month_expect_compl,
            t.last_month_compl_rate,
            t.last_month_end_expect_total_compl,
            t.last_month_end_expect_total_compl_rate,
            t.cur_month_task,
            t.cur_month_fight_target,
            t.cur_month_end_expect_total_compl,
            t.cur_month_end_expect_total_compl_rate,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from biz_rpt_monthly_production_index_analysis_sub t
        where t.id in
              (select distinct t.id
        from biz_rpt_monthly_production_index_analysis_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub" resultMap="RptMonthlyProductionIndexAnalysisSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="proIndexId != null ">
                            and t.pro_index_id = #{proIndexId}
                        </if>
                        <if test="groupPlanTarget != null ">
                            and t.group_plan_target = #{groupPlanTarget}
                        </if>
                        <if test="internalTarget != null ">
                            and t.internal_target = #{internalTarget}
                        </if>
                        <if test="lastLastMonthEndPassdownPlan != null ">
                            and t.last_last_month_end_passdown_plan = #{lastLastMonthEndPassdownPlan}
                        </if>
                        <if test="lastLastMonthEndPassdownCompl != null ">
                            and t.last_last_month_end_passdown_compl = #{lastLastMonthEndPassdownCompl}
                        </if>
                        <if test="lastMonthPlan != null ">
                            and t.last_month_plan = #{lastMonthPlan}
                        </if>
                        <if test="lastMonthExpectCompl != null ">
                            and t.last_month_expect_compl = #{lastMonthExpectCompl}
                        </if>
                        <if test="lastMonthComplRate != null ">
                            and t.last_month_compl_rate = #{lastMonthComplRate}
                        </if>
                        <if test="lastMonthEndExpectTotalCompl != null ">
                            and t.last_month_end_expect_total_compl = #{lastMonthEndExpectTotalCompl}
                        </if>
                        <if test="lastMonthEndExpectTotalComplRate != null ">
                            and t.last_month_end_expect_total_compl_rate = #{lastMonthEndExpectTotalComplRate}
                        </if>
                        <if test="curMonthTask != null ">
                            and t.cur_month_task = #{curMonthTask}
                        </if>
                        <if test="curMonthFightTarget != null ">
                            and t.cur_month_fight_target = #{curMonthFightTarget}
                        </if>
                        <if test="curMonthEndExpectTotalCompl != null ">
                            and t.cur_month_end_expect_total_compl = #{curMonthEndExpectTotalCompl}
                        </if>
                        <if test="curMonthEndExpectTotalComplRate != null ">
                            and t.cur_month_end_expect_total_compl_rate = #{curMonthEndExpectTotalComplRate}
                        </if>
                        <if test="delFlag != null  and delFlag != ''">
                            and t.del_flag = #{delFlag}
                        </if>
                        <if test="createBy != null  and createBy != ''">
                            and t.create_by = #{createBy}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="createTime != null ">
                            and t.create_time = #{createTime}
                        </if>
                        <if test="updateBy != null  and updateBy != ''">
                            and t.update_by = #{updateBy}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="updateTime != null ">
                            and t.update_time = #{updateTime}
                        </if>
                        <if test="remark != null  and remark != ''">
                            and t.remark = #{remark}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.mapper.RptMonthlyProductionIndexAnalysisMapper">
    <resultMap type="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis" id="RptMonthlyProductionIndexAnalysisResult">
            <result property="id" column="id"/>
            <result property="deptId" column="dept_id"/>
            <result property="year" column="year"/>
            <result property="month" column="month"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.dept_id,
            t.year,
            t.month,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from biz_rpt_monthly_production_index_analysis t
        where t.id in
              (select distinct t.id
        from biz_rpt_monthly_production_index_analysis t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis" resultMap="RptMonthlyProductionIndexAnalysisResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="year != null ">
                            and t.year = #{year}
                        </if>
                        <if test="month != null ">
                            and t.month = #{month}
                        </if>
                        <if test="delFlag != null  and delFlag != ''">
                            and t.del_flag = #{delFlag}
                        </if>
                        <if test="createBy != null  and createBy != ''">
                            and t.create_by = #{createBy}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="createTime != null ">
                            and t.create_time = #{createTime}
                        </if>
                        <if test="updateBy != null  and updateBy != ''">
                            and t.update_by = #{updateBy}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="updateTime != null ">
                            and t.update_time = #{updateTime}
                        </if>
                        <if test="remark != null  and remark != ''">
                            and t.remark = #{remark}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
    <select id="getProductMonthIndex"
            statementType="CALLABLE"
            resultType="com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis">
        {
            CALL product_month_index(
                #{year, mode=IN,jdbcType=BIGINT},
                #{month, mode=IN,jdbcType=BIGINT},
                #{deptId, mode=IN,jdbcType=BIGINT}
                 )
            }
    </select>


</mapper>

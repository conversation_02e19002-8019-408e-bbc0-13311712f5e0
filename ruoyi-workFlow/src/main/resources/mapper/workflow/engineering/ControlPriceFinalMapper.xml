<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.ControlPriceFinalMapper">
    <resultMap type="com.ruoyi.engineering.domain.ControlPriceFinal" id="ControlPriceFinalResult">
        <result property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="projectCode" column="project_code"/>
        <result property="projectName" column="project_name"/>
        <result property="year" column="year"/>
        <result property="deptId" column="dept_id"/>
        <result property="status" column="status"/>
        <result property="applyUserId" column="apply_user_id"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="instanceId" column="instance_id"/>
        <result property="processKey" column="process_key"/>
        <result property="notify" column="notify"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="attachment" column="attachment"/>
        <result property="delFlag" column="del_flag"/>
        <result property="amount" column="amount"/>
        <result property="actualCompleteDate" column="actual_complete_date"/>
        <result property="unlockFlag" column="unlock_flag"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.project_id,
                        t.project_code,
                        t.project_name,
                        t.year,
                        t.dept_id,
                        t.status,
                        t.apply_user_id,
                        t.apply_user_name,
                        t.apply_time,
                        t.instance_id,
                        t.process_key,
                        t.notify,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.attachment,
                        t.del_flag,
                        t.amount,
                        t.actual_complete_date,
                        t.unlock_flag
        from biz_control_price_final t
        where t.id in
              (select distinct t.id
               from biz_control_price_final t
                        left join sys_user u on (t.create_by, t.create_by_id) = (u.user_name, u.user_id)
                        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.year desc, t.id desc
        )
        group by t.id
        order by t.year desc, t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.engineering.domain.ControlPriceFinal"
            resultMap="ControlPriceFinalResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="projectId != null ">
                and t.project_id = #{projectId}
            </if>
            <if test="projectCode != null  and projectCode != ''">
                and t.project_code LIKE concat('%', #{projectCode}, '%')
            </if>
            <if test="projectName != null  and projectName != ''">
                and t.project_name LIKE concat('%', #{projectName}, '%')
            </if>
            <if test="year != null ">
                and t.year = #{year}
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="status != null  and status != ''">
                and t.status = #{status}
            </if>
            <if test="applyUserId != null  and applyUserId != ''">
                and t.apply_user_id = #{applyUserId}
            </if>
            <if test="applyUserName != null  and applyUserName != ''">
                and t.apply_user_name LIKE concat('%', #{applyUserName}, '%')
            </if>
            <if test="applyTime != null ">
                and t.apply_time = #{applyTime}
            </if>
            <if test="instanceId != null  and instanceId != ''">
                and t.instance_id = #{instanceId}
            </if>
            <if test="processKey != null  and processKey != ''">
                and t.process_key = #{processKey}
            </if>
            <if test="notify != null  and notify != ''">
                and t.notify = #{notify}
            </if>
            <if test="createById != null ">
                and t.create_by_id = #{createById}
            </if>
            <if test="updateById != null ">
                and t.update_by_id = #{updateById}
            </if>
            <if test="attachment != null  and attachment != ''">
                and t.attachment = #{attachment}
            </if>
            <if test="amount != null ">
                and t.amount = #{amount}
            </if>
            <if test="actualCompleteDate != null ">
                and t.actual_complete_date = #{actualCompleteDate}
            </if>
            ${params.dataScope}
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

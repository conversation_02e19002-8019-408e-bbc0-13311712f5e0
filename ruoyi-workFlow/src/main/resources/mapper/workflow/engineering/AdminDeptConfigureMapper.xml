<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.AdminDeptConfigureMapper">
    <resultMap type="AdminDeptConfigure" id="AdminDeptConfigureResult">
        <result property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="deptIds" column="dept_ids"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.type,
                        t.dept_ids,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        from biz_admin_dept_configure t
        where t.id in
              (select distinct t.id
               from biz_admin_dept_configure t
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="AdminDeptConfigure" resultMap="AdminDeptConfigureResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="type != null and type != ''">
                and t.type = #{type}
            </if>
            <if test="deptIds != null ">
                and t.dept_ids = #{deptIds}
            </if>
            <if test="createById != null ">
                and t.create_by_id = #{createById}
            </if>
            <if test="updateById != null ">
                and t.update_by_id = #{updateById}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

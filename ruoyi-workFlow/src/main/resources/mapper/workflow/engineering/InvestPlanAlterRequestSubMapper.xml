<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.InvestPlanAlterRequestSubMapper">
    <resultMap type="InvestPlanAlterRequestSub" id="InvestPlanAlterRequestSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="type" column="type"/>
            <result property="planAmount" column="plan_amount"/>
            <result property="registerDate" column="register_date"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
            <result property="projectCode" column="project_code"/>
            <result property="projectName" column="project_name"/>
            <result property="deptId" column="dept_id"/>
            <result property="validateAmount" column="validate_amount"/>
            <result property="validateStatusValue" column="validate_status_value"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.type,
            t.plan_amount,
            t.register_date,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark,
            t.project_code,
            t.project_name,
            t.dept_id,
            t.validate_amount,
            t.validate_status_value
        from biz_invest_plan_alter_request_sub t
        where t.id in
              (select distinct t.id
        from biz_invest_plan_alter_request_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="InvestPlanAlterRequestSub" resultMap="InvestPlanAlterRequestSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="type != null  and type != ''">
                            and t.type = #{type}
                        </if>
                        <if test="planAmount != null ">
                            and t.plan_amount = #{planAmount}
                        </if>
                        <if test="registerDate != null ">
                            and t.register_date = #{registerDate}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="projectCode != null  and projectCode != ''">
                            and t.project_code = #{projectCode}
                        </if>
                        <if test="projectName != null  and projectName != ''">
                            and t.project_name LIKE concat('%', #{projectName}, '%')
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="validateAmount != null ">
                            and t.validate_amount = #{validateAmount}
                        </if>
                        <if test="validateStatusValue != null  and validateStatusValue != ''">
                            and t.validate_status_value = #{validateStatusValue}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

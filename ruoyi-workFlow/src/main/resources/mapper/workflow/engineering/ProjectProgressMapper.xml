<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.ProjectProgressMapper">
    <resultMap type="com.ruoyi.engineering.domain.ProjectProgress" id="ProjectProgressResult">
        <!-- 基础字段 -->
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="year" column="year"/>
        <result property="planTypeValue" column="plan_type_value"/>
        <result property="projectName" column="project_name"/>
        <result property="applyDeptName" column="apply_dept_name"/>
        <result property="applyProjectCode" column="apply_project_code"/>
        <result property="applyProjectName" column="apply_project_name"/>
        <result property="totalPlanAmount" column="plan_invest_amount"/>
        <result property="applyUserName" column="apply_user_name"/>
        <result property="applyTime" column="apply_time"/>
        <result property="status" column="status"/>
        <result property="nodeStatus" column="node_status"/>
    </resultMap>

    <select id="selectProjectProgressList" parameterType="com.ruoyi.engineering.domain.ProjectProgress"
            resultMap="ProjectProgressResult">
        SELECT
            main.id as id, <!--投资计划id-->
            main.`year` as `year`, <!--年度-->
            main.`dept_id` as dept_id,  <!--投资计划分管单位id-->
            d.`dept_name` as dept_name, <!--投资计划分管单位名称-->
            main.`plan_type_value` as plan_type_value, <!--投资计划类别-->
            main.`project_name` as project_name, <!--投资计划项目名称-->
            main.`status` as status, <!--单据状态-->
            main.apply_id as apply_id, <!--实施项目id-->
            main.`apply_dept_id` as apply_dept_id,  <!--项目主管单位id-->
            da.`dept_name` as apply_dept_name,  <!--项目主管单位名称-->
            main.`apply_project_code` as apply_project_code,  <!--实施项目编号-->
            main.`apply_project_name` as apply_project_name,  <!--实施项目名称-->
            main.`plan_invest_amount` as plan_invest_amount, <!--取自项目实施表--> <!--合计计划投资额(万元)-->
            main.`apply_user_name` as apply_user_name, <!--申请人-->
            main.`apply_time` as apply_time, <!--申请时间-->
            main.`node_status` AS node_status  <!--节点状态-->
        FROM
        (
            SELECT DISTINCT
                p.id as id,
                p.`year` as `year`, <!--年度-->
                p.dept_id as dept_id, <!--投资计划分管单位id-->
                p.plan_type_value as plan_type_value, <!--投资计划类别-->
                p.project_name as project_name, <!--投资计划项目名称-->
                a.status as status, <!--单据状态-->
                a.plan_invest_amount as plan_invest_amount, <!--取自项目实施表--> <!--合计计划投资额(万元)-->
                a.id as apply_id,  <!--项目实施申请id-->
                a.`dept_id` as apply_dept_id,  <!--项目主管单位id-->
                a.`project_code` as apply_project_code,  <!--实施项目编号-->
                a.`project_name` as apply_project_name,  <!--实施项目名称-->
                a.`apply_user_name`, <!--申请人-->
                a.`apply_time`, <!--申请时间-->
                COALESCE(n.node_status,'0') AS node_status  <!--节点状态-->
            FROM
            biz_invest_plan p
            LEFT JOIN biz_project_implement_apply a ON a.invest_plan_id = p.id
            LEFT JOIN (
                SELECT
                    project_code,
                    CASE
                    WHEN SUM(CASE WHEN status != '6' THEN 1 ELSE 0 END) > 0 THEN '2'  -- 有非 '6' 的 status，返回 '2'
                    ELSE
                    CASE WHEN COUNT(status) = 10 THEN '1' ELSE '2' END  -- 如果 status 为 '6' 且记录数为 10，返回 '1'，否则返回 '2'
                    END AS node_status
                FROM (
                    SELECT DISTINCT project_code, status FROM biz_project_implement_apply where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_control_price where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_control_price_final where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_bid_document where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_bid_opening where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_bid_winning_notice where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_contract where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_start_work_report where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_completion_acceptance where del_flag = '0'
                    UNION ALL
                    SELECT DISTINCT project_code, status FROM biz_settlement where del_flag = '0'
                ) all_nodes
                GROUP BY project_code
            ) as n
            ON a.project_code = n.project_code
            WHERE p.del_flag = '0'
        ) as main
        LEFT JOIN sys_dept d on (main.dept_id) = (d.dept_id)
        LEFT JOIN sys_dept da on (main.apply_dept_id) = (da.dept_id)
        <where>
            <!--动态查询条件-->
            <if test="year != null">
                AND main.`year` = #{year}  <!--年度-->
            </if>
            <if test="deptId != null">
                AND main.`dept_id` = #{deptId} <!--投资计划分管单位-->
            </if>
            <if test="planTypeValue != null and planTypeValue != ''">
                AND main.`plan_type_value` LIKE CONCAT('%', #{planTypeValue}, '%') <!--投资计划类别-->
            </if>
            <if test="projectName != null and projectName != ''">
                AND main.`project_name` LIKE CONCAT('%', #{projectName}, '%') <!--投资计划名称-->
            </if>
            <if test="applyDeptId != null and applyDeptId != ''">
                AND main.`apply_dept_id` = #{applyDeptId} <!--项目主管单位-->
            </if>
            <if test="applyProjectCode != null and applyProjectCode != ''">
                AND main.`apply_project_code` LIKE CONCAT('%', #{applyProjectCode}, '%') <!--实施项目编号-->
            </if>
            <if test="applyProjectName != null and applyProjectName != ''">
                AND main.`apply_project_name` LIKE CONCAT('%', #{applyProjectName}, '%') <!--实施项目名称-->
            </if>
            <if test="nodeStatus != null and nodeStatus != ''">
                AND main.`node_status` = #{nodeStatus}   <!--节点状态-->
            </if>
        </where>
        ORDER BY
        main.`year` DESC, main.`id` DESC, main.`apply_id` DESC
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.engineering.mapper.ConstructionProgressFeedbackSubMapper">
    <resultMap type="com.ruoyi.engineering.domain.ConstructionProgressFeedbackSub" id="ConstructionProgressFeedbackSubResult">
            <result property="id" column="id"/>
            <result property="parentId" column="parent_id"/>
            <result property="recordDate" column="record_date"/>
            <result property="constructionNodus" column="construction_nodus"/>
            <result property="uploadImage" column="upload_image"/>
            <result property="delFlag" column="del_flag"/>
            <result property="createBy" column="create_by"/>
            <result property="createById" column="create_by_id"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateTime" column="update_time"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.parent_id,
            t.record_date,
            t.construction_nodus,
            t.upload_image,
            t.del_flag,
            t.create_by,
            t.create_by_id,
            t.create_time,
            t.update_by,
            t.update_by_id,
            t.update_time,
            t.remark
        from biz_construction_progress_feedback_sub t
        where t.id in
              (select distinct t.id
        from biz_construction_progress_feedback_sub t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="ConstructionProgressFeedbackSub" resultMap="ConstructionProgressFeedbackSubResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="parentId != null ">
                            and t.parent_id = #{parentId}
                        </if>
                        <if test="recordDate != null ">
                            and t.record_date = #{recordDate}
                        </if>
                        <if test="constructionNodus != null  and constructionNodus != ''">
                            and t.construction_nodus = #{constructionNodus}
                        </if>
                        <if test="uploadImage != null  and uploadImage != ''">
                            and t.upload_image = #{uploadImage}
                        </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                ${params.dataScope}
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

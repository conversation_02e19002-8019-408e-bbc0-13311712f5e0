<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.kingdeeProject.mapper.KingdeeProjectMapper">
    <resultMap type="com.ruoyi.archives.kingdeeProject.domain.KingdeeProject" id="KingdeeProjectResult">
        <result property="id" column="id"/>
        <result property="projectNumber" column="project_number"/>
        <result property="projectName" column="project_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="projectStatus" column="project_status"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.project_number,
                        t.project_name,
                        t.dept_id,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark,
                        t.project_status
        from biz_kingdee_project t
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.archives.kingdeeProject.domain.KingdeeProject"
            resultMap="KingdeeProjectResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <!--            <if test="mobileParams!= null and mobileParams!='' ">-->
            <!--                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')-->
            <!--            </if>-->
            <if test="projectNumber != null  and projectNumber != ''">
                and t.project_number = #{projectNumber}
            </if>
            <if test="projectName != null  and projectName != ''">
                and t.project_name LIKE concat('%', #{projectName}, '%')
            </if>
            <if test="deptId != null ">
                and t.dept_id = #{deptId}
            </if>
            <if test="projectStatus != null  and projectStatus != ''">
                and t.project_status = #{projectStatus}
            </if>
        </where>
        order by t.id desc
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.sysVisualConfig.mapper.SysVisualConfigMapper">
    <resultMap type="com.ruoyi.archives.sysVisualConfig.domain.SysVisualConfig" id="SysVisualConfigResult">
            <result property="id" column="id"/>
            <result property="type" column="type"/>
            <result property="cover" column="cover"/>
            <result property="address" column="address"/>
            <result property="name" column="name"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="createById" column="create_by_id"/>
            <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.type,
            t.cover,
            t.address,
            t.name,
            t.create_by,
            t.create_time,
            t.update_by,
            t.update_time,
            t.create_by_id,
            t.update_by_id,
            t.remark,
            t.orderno
        from sys_visual_config t
        where t.id in
              (select distinct t.id
        from sys_visual_config t
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.orderno asc
    </sql>
    <select id="list" parameterType="com.ruoyi.archives.sysVisualConfig.domain.SysVisualConfig" resultMap="SysVisualConfigResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="type != null  and type != ''">
                            and t.type = #{type}
                        </if>
                        <if test="cover != null  and cover != ''">
                            and t.cover = #{cover}
                        </if>
                        <if test="address != null ">
                            and t.address = #{address}
                        </if>
                        <if test="name != null  and name != ''">
                            and t.name LIKE concat('%', #{name}, '%')
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.BizAssayGoldConcentrate.mapper.BizAssayGoldConcentrateMapper">
    <resultMap type="com.ruoyi.archives.BizAssayGoldConcentrate.domain.BizAssayGoldConcentrate"
               id="BizAssayGoldConcentrateResult">
        <result property="id" column="id"/>
        <result property="year" column="year"/>
        <result property="deptId" column="dept_id"/>
        <result property="numbertimes" column="numbertimes"/>
        <result property="wagonNumber" column="wagon_number"/>
        <result property="wetOreQuantity" column="wet_ore_quantity"/>
        <result property="moistureContent" column="moisture_content"/>
        <result property="dryOreQuantity" column="dry_ore_quantity"/>
        <result property="gold" column="gold"/>
        <result property="goldMetallicity" column="gold_metallicity"/>
        <result property="returnGoldRate" column="return_gold_rate"/>
        <result property="returnGoldMetallicity" column="return_gold_metallicity"/>
        <result property="silver" column="silver"/>
        <result property="silverMetallicity" column="silver_metallicity"/>
        <result property="returnSilverRate" column="return_silver_rate"/>
        <result property="returnSilverMetallicity" column="return_silver_metallicity"/>
        <result property="processingFee" column="processing_fee"/>
        <result property="samplingTime" column="sampling_time"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>
    <sql id="selectBizAssayGoldConcentrateVo">
        SELECT distinct t.id,
                        t.year,
                        t.dept_id,
                        d.dept_name,
                        t.numbertimes,
                        t.wagon_number,
                        t.wet_ore_quantity,
                        t.moisture_content,
                        t.dry_ore_quantity,
                        t.gold,
                        t.gold_metallicity,
                        t.return_gold_rate,
                        t.return_gold_metallicity,
                        t.silver,
                        t.silver_metallicity,
                        t.return_silver_rate,
                        t.return_silver_metallicity,
                        t.processing_fee,
                        t.sampling_time,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.remark,
                        t.del_flag
        FROM biz_assay_gold_concentrate t left join sys_dept d on t.dept_id = d.dept_id
    </sql>
    <select id="list" parameterType="com.ruoyi.archives.BizAssayGoldConcentrate.domain.BizAssayGoldConcentrate"
            resultMap="BizAssayGoldConcentrateResult">
        <include refid="selectBizAssayGoldConcentrateVo"/>
        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="year != null ">AND
                t.year = #{year}
            </if>

            <if test="deptId != null ">AND
                t.dept_id = #{deptId}
            </if>

            <if test="numbertimes != null ">AND
                t.numbertimes = #{numbertimes}
            </if>
            <if test="wagonNumber != null  and wagonNumber != ''">AND
                t.wagon_number = #{wagonNumber}
            </if>
            <if test="wetOreQuantity != null ">AND
                t.wet_ore_quantity = #{wetOreQuantity}
            </if>
            <if test="moistureContent != null ">AND
                t.moisture_content = #{moistureContent}
            </if>
            <if test="dryOreQuantity != null ">AND
                t.dry_ore_quantity = #{dryOreQuantity}
            </if>
            <if test="gold != null ">AND
                t.gold = #{gold}
            </if>
            <if test="goldMetallicity != null ">AND
                t.gold_metallicity = #{goldMetallicity}
            </if>
            <if test="returnGoldRate != null ">AND
                t.return_gold_rate = #{returnGoldRate}
            </if>
            <if test="returnGoldMetallicity != null ">AND
                t.return_gold_metallicity = #{returnGoldMetallicity}
            </if>
            <if test="silver != null ">AND
                t.silver = #{silver}
            </if>
            <if test="silverMetallicity != null ">AND
                t.silver_metallicity = #{silverMetallicity}
            </if>
            <if test="returnSilverRate != null ">AND
                t.return_silver_rate = #{returnSilverRate}
            </if>
            <if test="returnSilverMetallicity != null ">AND
                t.return_silver_metallicity = #{returnSilverMetallicity}
            </if>
            <if test="processingFee != null ">AND
                t.processing_fee = #{processingFee}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>
        </where>
        ORDER BY t.sampling_time asc,t.numbertimes
    </select>

    <select id="sumStatistics" resultType="com.ruoyi.archives.BizAssayGoldConcentrate.domain.BizAssayGoldConcentrate">

        SELECT

        SUM( wet_ore_quantity ) AS wet_ore_quantity,
        SUM( dry_ore_quantity ) AS dry_ore_quantity,
        SUM( gold ) AS gold,
        SUM( gold_metallicity ) AS gold_metallicity,
        SUM( return_gold_metallicity ) AS return_gold_metallicity,
        SUM( silver ) AS silver,
        SUM( silver_metallicity ) AS silver_metallicity,
        SUM( return_silver_metallicity ) AS return_silver_metallicity,
        SUM( processing_fee ) AS processing_fee
        from
        biz_assay_gold_concentrate t

        <where>
            t.del_flag = 0
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="year != null ">AND
                t.year = #{year}
            </if>

            <if test="deptId != null ">AND
                t.dept_id = #{deptId}
            </if>

            <if test="numbertimes != null ">AND
                t.numbertimes = #{numbertimes}
            </if>
            <if test="wagonNumber != null  and wagonNumber != ''">AND
                t.wagon_number = #{wagonNumber}
            </if>
            <if test="wetOreQuantity != null ">AND
                t.wet_ore_quantity = #{wetOreQuantity}
            </if>
            <if test="moistureContent != null ">AND
                t.moisture_content = #{moistureContent}
            </if>
            <if test="dryOreQuantity != null ">AND
                t.dry_ore_quantity = #{dryOreQuantity}
            </if>
            <if test="gold != null ">AND
                t.gold = #{gold}
            </if>
            <if test="goldMetallicity != null ">AND
                t.gold_metallicity = #{goldMetallicity}
            </if>
            <if test="returnGoldRate != null ">AND
                t.return_gold_rate = #{returnGoldRate}
            </if>
            <if test="returnGoldMetallicity != null ">AND
                t.return_gold_metallicity = #{returnGoldMetallicity}
            </if>
            <if test="silver != null ">AND
                t.silver = #{silver}
            </if>
            <if test="silverMetallicity != null ">AND
                t.silver_metallicity = #{silverMetallicity}
            </if>
            <if test="returnSilverRate != null ">AND
                t.return_silver_rate = #{returnSilverRate}
            </if>
            <if test="returnSilverMetallicity != null ">AND
                t.return_silver_metallicity = #{returnSilverMetallicity}
            </if>
            <if test="processingFee != null ">AND
                t.processing_fee = #{processingFee}
            </if>
            <if test="startSamplingTime != null">AND
                t.sampling_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null ">AND
                t.sampling_time &lt;= #{endSamplingTime}
            </if>



        </where>
    </select>
</mapper>

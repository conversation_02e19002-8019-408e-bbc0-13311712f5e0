<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.warehouse.mapper.WarehouseMapper">
    <resultMap type="com.ruoyi.archives.warehouse.domain.Warehouse" id="BizWarehouseResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="fullName" column="full_name"/>
        <result property="deptId" column="dept_id"/>
        <result property="mainFlag" column="main_flag"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>
    <sql id="selectBizWarehouseVo">
        SELECT distinct t.id,
                        t.code,
                        t.name,
                        t.full_name,
                        t.dept_id,
                        t.main_flag,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        FROM biz_warehouse t
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = t.dept_id
        </if>
    </sql>
    <select id="list" parameterType="com.ruoyi.archives.warehouse.domain.Warehouse" resultMap="BizWarehouseResult">
        <include refid="selectBizWarehouseVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="code != null  and code != ''">AND
                t.code LIKE concat('%', #{code}, '%')
            </if>
            <if test="name != null  and name != ''">AND
                t.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="fullName != null  and fullName != ''">AND
                t.full_name LIKE concat('%', #{fullName}, '%')
            </if>
            <if test="deptId != null ">AND
                t.dept_id = #{deptId} OR t.dept_id IN ( SELECT d.dept_id FROM sys_dept d WHERE
                find_in_set(#{deptId},ancestors) )
            </if>
            <if test="mainFlag != null  and mainFlag != ''">AND
                t.main_flag = #{mainFlag}
            </if>
            <if test="createById != null ">AND
                t.create_by_id = #{createById}
            </if>
            <if test="updateById != null ">AND
                t.update_by_id = #{updateById}
            </if>
            <if test="remark != null  and remark != ''">AND
                t.remark LIKE concat('%', #{remark}, '%')
            </if>
            <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
                ${params.dataScope}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
</mapper>

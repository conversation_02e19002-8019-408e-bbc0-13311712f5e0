<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.place.mapper.PlaceMapper">
    <resultMap type="com.ruoyi.archives.place.domain.Place" id="PlaceResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="fullName" column="full_name"/>
        <result property="paragraphId" column="paragraph_id"/>
        <result property="explorationLine" column="exploration_line"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="wellheadId" column="wellhead_id"/>
    </resultMap>
    <sql id="selectPlaceVo">
        SELECT distinct t.id,
                        t.code,
                        t.name,
                        t.full_name,
                        t.paragraph_id,
                        t.exploration_line,
                        t.wellhead_id,
                        t.dept_id,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.remark
        from biz_place t
        where t.id in (select distinct t.id
        from biz_place t
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id = t.dept_id
        </if>
    </sql>
    <sql id="groupOrder">
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="com.ruoyi.archives.place.domain.Place" resultMap="PlaceResult">
        <include refid="selectPlaceVo"/>
        <where>
            t.del_flag = '0'
            <if test="mobileParams!= null and mobileParams!='' ">
                AND concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="code != null  and code != ''">AND
                t.code = #{code}
            </if>
            <if test="name != null  and name != ''">AND
                t.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="fullName != null  and fullName != ''">AND
                t.full_name LIKE concat('%', #{fullName}, '%')
            </if>
            <if test="paragraphId != null ">AND
                t.paragraph_id = #{paragraphId}
            </if>
            <if test="explorationLine != null  and explorationLine != ''">AND
                t.exploration_line = #{explorationLine}
            </if>
            <if test="remark != null  and remark != ''">AND
                t.remark LIKE concat('%', #{remark}, '%')
            </if>
            <if test="deptId != null ">
                AND (t.dept_id = #{deptId}
                OR t.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},ancestors)))
            </if>
            <if test="wellheadId != null ">AND
                t.wellhead_id = #{wellheadId}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

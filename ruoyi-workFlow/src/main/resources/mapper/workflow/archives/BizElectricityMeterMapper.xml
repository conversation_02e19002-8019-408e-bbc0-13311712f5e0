<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.archives.BizElectricityMeter.mapper.BizElectricityMeterMapper">
    <resultMap type="BizElectricityMeter" id="BizElectricityMeterResult">
        <result property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="placeId" column="place_id"/>
        <result property="isMeter" column="is_meter"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="powerSupplyArea" column="power_supply_area"/>
        <result property="isTimeSpan" column="is_time_span"/>
        <result property="status" column="status"/>
    </resultMap>
    <sql id="selectBizElectricityMeterVo">
        SELECT distinct t.id,
                        t.code,
                        t.name,
                        t.place_id,
                        t.is_meter,
                        t.del_flag,
                        t.create_by,
                        t.create_by_id,
                        t.create_time,
                        t.update_by,
                        t.update_by_id,
                        t.update_time,
                        t.dept_id,
                        t.power_supply_area,
                        t.is_time_span,
                        t.status,
                        t.remark
        FROM biz_electricity_meter t
    </sql>
    <select id="list" parameterType="BizElectricityMeter" resultMap="BizElectricityMeterResult">
        <include refid="selectBizElectricityMeterVo"/>
        <where>
            <if test="mobileParams!= null and mobileParams!='' ">AND CONCAT(IFNULL(t.id,"")) LIKE
                CONCAT('%',#{mobileParams},'%')
            </if>
            <if test="code != null  and code != ''">AND
                t.code = #{code}
            </if>
            <if test="name != null  and name != ''">AND
                t.name LIKE concat('%', #{name}, '%')
            </if>
            <if test="placeId != null ">AND
                t.place_id = #{placeId}
            </if>
            <if test="isMeter != null  and isMeter != ''">AND
                t.is_meter = #{isMeter}
            </if>
            <if test="deptId != null ">AND
                t.dept_id = #{deptId}
            </if>
            <if test="powerSupplyArea != null  and powerSupplyArea != ''">AND
                t.power_supply_area = #{powerSupplyArea}
            </if>
            <if test="isTimeSpan != null  and isTimeSpan != ''">AND
                t.is_time_span = #{isTimeSpan}
            </if>
            <if test="status != null  and status != ''">AND
                t.status = #{status}
            </if>
        </where>
        ORDER BY t.id DESC
    </select>
</mapper>

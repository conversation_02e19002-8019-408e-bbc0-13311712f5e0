<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecShovelingTransportMapper">
    <resultMap type="com.ruoyi.rec.domain.BizRecShovelingTransport" id="BizRecShovelingTransportResult">
            <result property="id" column="id"/>
            <result property="createById" column="create_by_id"/>
            <result property="createBy" column="create_by"/>
            <result property="createTime" column="create_time"/>
            <result property="updateById" column="update_by_id"/>
            <result property="updateBy" column="update_by"/>
            <result property="updateTime" column="update_time"/>
            <result property="delFlag" column="del_flag"/>
            <result property="remark" column="remark"/>
            <result property="deptId" column="dept_id"/>
            <result property="recTime" column="rec_time"/>
            <result property="submitStatusValue" column="submit_status_value"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct
            t.id,
            t.create_by_id,
            t.create_by,
            t.create_time,
            t.update_by_id,
            t.update_by,
            t.update_time,
            t.del_flag,
            t.remark,
            t.dept_id,
            t.rec_time,
            t.submit_status_value
        from biz_rec_shoveling_transport t
        where t.id in
              (select distinct t.id
        from biz_rec_shoveling_transport t
        left join sys_user u on (t.create_by,t.create_by_id)=(u.user_name,u.user_id)
        left join sys_dept d on (t.dept_id) = (d.dept_id)
    </sql>
    <sql id="groupOrder">
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecShovelingTransport" resultMap="BizRecShovelingTransportResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
                        <if test="createById != null ">
                            and t.create_by_id = #{createById}
                        </if>
                        <if test="updateById != null ">
                            and t.update_by_id = #{updateById}
                        </if>
                        <if test="deptId != null ">
                            and t.dept_id = #{deptId}
                        </if>
                        <if test="recTime != null ">
                            and t.rec_time = #{recTime}
                        </if>
                        <if test="startSamplingTime != null">AND
                            t.rec_time >= #{startSamplingTime}
                        </if>
                        <if test="endSamplingTime != null ">AND
                            t.rec_time &lt;= #{endSamplingTime}
                        </if>
                        <if test="submitStatusValue != null  and submitStatusValue != ''">
                            and t.submit_status_value = #{submitStatusValue}
                        </if>
        </where>
        <include refid="groupOrder"/>
    </select>


   <!--铲运作业量分析-->
    <select id="listRptShovelingTransport" parameterType="BizRecShovelingTransport" resultMap="BizRecShovelingTransportResult">
        SELECT u.nick_name as nick_name
        ,e.`name` as vehicle_name
        ,d.dept_name,job_type_value as job_type_value
        ,sum(coalesce (sub.count,0))  as `count`
        ,sum(coalesce (sub.`hour`,0)) as `hour`
        from biz_rec_shoveling_transport tran INNER JOIN biz_rec_shoveling_transport_sub sub on tran.id=sub.parent_id
            left join sys_user u on u.user_id=sub.driver_id
            left join sys_dept d on d.dept_id =sub.aim_dept_id
            left join biz_equipment e on e.id=sub.vehicle
        <where>
              and tran.submit_status_value =1
            <if test="driverId != null">
                and  driver_id= #{driverId}
            </if>
            <if test="vehicle != null">
                and vehicle= #{vehicle}
            </if>
            <if test="jobTypeValue!=null">
                and job_type_value= #{jobTypeValue}
            </if>
            <if test="startSamplingTime != null">
                and tran.rec_time >= #{startSamplingTime}
            </if>
            <if test="endSamplingTime != null">
                and tran.rec_time &lt;= #{endSamplingTime}
            </if>
            <if test="aimDeptId != null">
                and aim_dept_id= #{aimDeptId}
            </if>
        </where>
        GROUP BY driver_id,u.user_name, sub.vehicle,aim_dept_id,d.dept_name,e.`name`,job_type_value

    </select>

</mapper>

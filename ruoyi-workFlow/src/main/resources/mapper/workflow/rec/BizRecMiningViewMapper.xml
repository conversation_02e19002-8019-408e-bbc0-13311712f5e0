<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecMiningViewMapper">
    <resultMap type="BizRecMiningView" id="BizRecMiningViewResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="recTime" column="rec_time"/>
        <result property="submitStatusValue" column="submit_status_value"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="wellheadCode" column="wellhead_code"/>
        <result property="wellheadName" column="wellhead_name"/>
        <result property="paragraphId" column="paragraph_id"/>
        <result property="paragraphCode" column="paragraph_code"/>
        <result property="paragraphName" column="paragraph_name"/>
        <result property="projectItemId" column="project_item_id"/>
        <result property="projectItemCode" column="project_item_code"/>
        <result property="projectItemName" column="project_item_name"/>
        <result property="workShiftValue0" column="work_shift_value0"/>
        <result property="workShiftValue1" column="work_shift_value1"/>
        <result property="workShiftValue2" column="work_shift_value2"/>
        <result property="amount" column="amount"/>
        <result property="metalMeshAmount" column="metal_mesh_amount"/>
        <result property="resinBoltAmount" column="resin_bolt_amount"/>
        <result property="slitWedgeBolt1Amount" column="slit_wedge_bolt1_amount"/>
        <result property="slitWedgeBolt2Amount" column="slit_wedge_bolt2_amount"/>
        <result property="concreteAmount" column="concrete_amount"/>
        <result property="miningEquipmentValue" column="mining_equipment_value"/>
    </resultMap>
    <sql id="selectFrom">
        select t.id,
               t.dept_id,
               t.dept_name,
               t.rec_time,
               t.submit_status_value,
               t.wellhead_id,
               t.wellhead_name,
               t.wellhead_code,
               t.paragraph_id,
               t.paragraph_name,
               t.paragraph_code,
               t.project_item_id,
               t.project_item_name,
               t.project_item_code,
               t.work_shift_value0,
               t.work_shift_value1,
               t.work_shift_value2,
               t.amount,
               t.metal_mesh_amount,
               t.resin_bolt_amount,
               t.slit_wedge_bolt1_amount,
               t.slit_wedge_bolt2_amount,
               t.concrete_amount
        from biz_rec_mining_view t
        where t.id in (select distinct t.id
                       from biz_rec_mining_view t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecMiningView" resultMap="BizRecMiningViewResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="id != null">and t.id=#{id}</if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecTunnelingEquipmentApplySubMapper">
    <resultMap type="BizRecTunnelingEquipmentApplySub" id="BizRecTunnelingEquipmentApplySubResult">
        <result property="id" column="id"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="parentId" column="parent_id"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="workShiftValue" column="work_shift_value"/>
        <result property="tunnelingEquipmentValue" column="tunneling_equipment_value"/>
        <result property="amount" column="amount"/>
        <result property="jobDetails" column="job_details"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
                        t.create_by_id,
                        t.create_by,
                        t.create_time,
                        t.update_by_id,
                        t.update_by,
                        t.update_time,
                        t.del_flag,
                        t.remark,
                        t.parent_id,
                        t.wellhead_id,
                        t.work_shift_value,
                        t.tunneling_equipment_value,
                        t.amount,
                        t.job_details
        from biz_rec_tunneling_equipment_apply_sub t
        where t.id in (select distinct t.id
                       from biz_rec_tunneling_equipment_apply_sub t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecTunnelingEquipmentApplySub"
            resultMap="BizRecTunnelingEquipmentApplySubResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="recTime != null ">
                and t.rec_time = #{recTime}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

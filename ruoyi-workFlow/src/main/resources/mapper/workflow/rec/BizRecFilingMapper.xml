<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecFilingMapper">
    <resultMap type="BizRecFiling" id="BizRecFilingResult">
        <result property="id" column="id"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="recTime" column="rec_time"/>
        <result property="submitStatusValue" column="submit_status_value"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="projectItemId" column="project_item_id"/>
        <result property="startime" column="startime"/>
        <result property="endtime" column="endtime"/>
        <result property="concentration" column="concentration"/>
        <result property="mortar" column="mortar"/>
        <result property="materialC" column="material_c"/>
        <result property="currentFill" column="current_fill"/>
        <result property="jobDetails" column="job_details"/>
        <result property="bz" column="bz"/>
        <result property="jxtcl" column="jxtcl"/>
        <result property="tailingsAmount" column="tailings_amount"/>
        <result property="wkkdcl" column="wkkdcl"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.dept_id,
        t.rec_time,
        t.submit_status_value,
        t.wellhead_id,
        t.project_item_id,
        t.startime,
        t.endtime,
        t.concentration,
        t.mortar,
        t.material_c,
        t.current_fill,
        t.job_details,
        t.id,
        t.create_by_id,
        t.create_by,
        t.create_time,
        t.update_by_id,
        t.update_by,
        t.update_time,
        t.del_flag,
        t.remark
        from biz_rec_filing t
        where t.id in (select distinct t.id
        from biz_rec_filing t
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id=t.dept_id
        </if>
    </sql>
    <sql id="groupOrder">
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecFiling" resultMap="BizRecFilingResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="recTime != null ">
                and t.rec_time = #{recTime}
            </if>
            <if test="startFilingTime != null">AND
                t.rec_time >= #{startFilingTime}
            </if>
            <if test="endFilingTime != null ">AND
                t.rec_time &lt;= #{endFilingTime}
            </if>

            <if test="submitStatusValue != null and submitStatusValue != ''">
                and t.submit_status_value = #{submitStatusValue}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
    <select id="listReduceWellhead" parameterType="BizRecFiling" resultMap="BizRecFilingResult">
        select distinct
        t.wellhead_id,
        sum(t.concentration) as concentration,
        sum(t.mortar) as mortar,
        sum(t.material_c) as material_c,
        sum(t.current_fill) as current_fill
        from biz_rec_filing t
        where t.id in (select distinct t.id
        from biz_rec_filing t
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_user u on u.user_name=t.create_by
            left join sys_dept d on d.dept_id=t.dept_id
        </if>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="recTime != null ">
                and t.rec_time = #{recTime}
            </if>
            <if test="submitStatusValue != null and submitStatusValue != ''">
                and t.submit_status_value = #{submitStatusValue}
            </if>
        </where>
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.wellhead_id
    </select>
    <select id="getlist" resultMap="BizRecFilingResult">
        SELECT
        fill.rec_time,
        rpt.tailings_amount,
        fill.concentration,
        ROUND(270 / (270 - 2.7 * fill.concentration + fill.concentration), 2) AS 'bz',
        ROUND(fill.current_fill * 1.6, 2) AS 'jxtcl',
        fill.current_fill,
        CASE
        WHEN rpt.tailings_amount = 0 OR rpt.tailings_amount IS null THEN 0
        WHEN rpt.tailings_amount - (fill.current_fill * 1.6) &lt;= 0 THEN 0
        WHEN rpt.tailings_amount - (fill.current_fill * 1.6) >= 0 THEN rpt.tailings_amount - (fill.current_fill * 1.6)
        END AS 'wkkdcl'
        FROM
        biz_rec_filing fill
        LEFT JOIN
        biz_rpt_concentrator_total_briefing rpt
        ON
        fill.dept_id = rpt.dept_id AND rpt.dispatch_date = fill.rec_time
        <where>
            fill.submit_status_value = 1
            <if test="startFilingTime != null">AND
                fill.rec_time >= #{startFilingTime}
            </if>
            <if test="endFilingTime != null ">AND
                fill.rec_time &lt;= #{endFilingTime}
            </if>
            <if test="deptId !=0 and deptId!=null">
             AND  fill.dept_id=#{deptId}
            </if>
        </where>

    </select>
    <select id="dailyIncrease" resultType="java.lang.String">
        SELECT
            sum( current_fill ) AS current_fill
        FROM
            biz_rec_filing
        <where>
            submit_status_value = 1
            <if test="deptId != null and deptId !=100">
                AND dept_id = #{deptId}
            </if>
            AND rec_time = #{formattedDate}
        </where>

    </select>
    <select id="monthlyIncrease" resultType="java.lang.String">
        SELECT
            sum( current_fill ) AS current_fill
        FROM
            biz_rec_filing
        <where>
            submit_status_value = 1
            <if test="deptId != null and deptId !=100">
                AND dept_id = #{deptId}
            </if>

            AND rec_time BETWEEN #{startDate}
            AND #{formattedDate}
        </where>

    </select>
    <select id="annualIncrease" resultType="java.lang.String">
        SELECT
            sum( current_fill ) AS current_fill
        FROM
            biz_rec_filing
        <where>
            submit_status_value = 1
            <if test="deptId != null and deptId !=100">
                AND dept_id = #{deptId}
            </if>

            AND rec_time BETWEEN #{firstDayOfYear}
            AND #{formattedDate}
        </where>

    </select>
    <select id="dailyIncreaseAmount" resultType="com.ruoyi.rec.domain.BizRecFiling">
        SELECT
            sum( current_fill ) AS current_fill,
            rec_time
        FROM
            biz_rec_filing
        <where>
            submit_status_value = 1
            <if test="deptId != null and deptId !=100">
                AND dept_id = #{deptId}
            </if>
            AND rec_time BETWEEN #{firstDayOfMonth}
            AND  #{formattedDate}
            GROUP BY
            rec_time
        </where>

    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecEquipmentOperationViewMapper">
    <resultMap type="BizRecEquipmentOperationView" id="BizRecEquipmentOperationViewResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="recTime" column="rec_time"/>
        <result property="submitStatusValue" column="submit_status_value"/>
        <result property="wellheadId" column="wellhead_id"/>
        <result property="wellheadCode" column="wellhead_code"/>
        <result property="wellheadName" column="wellhead_name"/>
        <result property="paragraphId" column="paragraph_id"/>
        <result property="paragraphCode" column="paragraph_code"/>
        <result property="paragraphName" column="paragraph_name"/>
        <result property="equipmentId" column="equipment_id"/>
        <result property="equipmentCode" column="equipment_code"/>
        <result property="equipmentName" column="equipment_name"/>
        <result property="workShiftValue0" column="work_shift_value0"/>
        <result property="workShiftValue1" column="work_shift_value1"/>
        <result property="workShiftValue2" column="work_shift_value2"/>
        <result property="jobDetails" column="job_details"/>
        <result property="runningTime" column="running_time"/>
    </resultMap>
    <sql id="selectFrom">
        select t.id,
               t.dept_id,
               t.dept_name,
               t.rec_time,
               t.submit_status_value,
               t.wellhead_id,
               t.wellhead_name,
               t.wellhead_code,
               t.paragraph_id,
               t.paragraph_name,
               t.paragraph_code,
               t.equipment_id,
               t.equipment_name,
               t.equipment_code,
               t.work_shift_value0,
               t.work_shift_value1,
               t.work_shift_value2,
               t.running_time,
               t.job_details
        from biz_rec_equipment_operation_view t
        where t.id in (select distinct t.id
                       from biz_rec_equipment_operation_view t
    </sql>
    <sql id="groupOrder">
        group by t.id
        order by t.id desc
        )
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecEquipmentOperationView" resultMap="BizRecEquipmentOperationViewResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="id != null">and t.id=#{id}</if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

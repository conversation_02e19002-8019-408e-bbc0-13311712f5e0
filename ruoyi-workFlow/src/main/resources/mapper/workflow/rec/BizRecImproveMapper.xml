<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.rec.mapper.BizRecImproveMapper">
    <resultMap type="BizRecImprove" id="BizRecImproveResult">
        <result property="id" column="id"/>
        <result property="createById" column="create_by_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateById" column="update_by_id"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="remark" column="remark"/>
        <result property="deptId" column="dept_id"/>
        <result property="recTime" column="rec_time"/>
        <result property="submitStatusValue" column="submit_status_value"/>
    </resultMap>
    <sql id="selectFrom">
        select distinct t.id,
        t.create_by_id,
        t.create_by,
        t.create_time,
        t.update_by_id,
        t.update_by,
        t.update_time,
        t.del_flag,
        t.remark,
        t.dept_id,
        t.rec_time,
        t.submit_status_value
        from biz_rec_improve t
        where t.id in (select distinct t.id
        from biz_rec_improve t
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            left join sys_dept d on d.dept_id=t.dept_id
        </if>
    </sql>
    <sql id="groupOrder">
        <!-- 数据范围过滤 -->
        <if test="params!=null and params.dataScope !=null and params.dataScope != '' ">
            ${params.dataScope}
        </if>
        group by t.id
        order by t.id desc
        )
        group by t.id
        order by t.id desc
    </sql>
    <select id="list" parameterType="BizRecImprove" resultMap="BizRecImproveResult">
        <include refid="selectFrom"/>
        <where>
            1=1
            <if test="mobileParams!= null and mobileParams!='' ">
                and concat(ifnull(t.id,"")) like concat('%',#{mobileParams},'%')
            </if>
            <if test="deptId != null and deptId != 0">
                and (t.dept_id = #{deptId} or t.dept_id in (select distinct t.dept_id from sys_dept t where
                find_in_set(#{deptId},ancestors)))
            </if>
            <if test="startTime != null">AND
                t.rec_time >= #{startTime}
            </if>
            <if test="endTime != null ">AND
                t.rec_time &lt;= #{endTime}
            </if>
            <if test="submitStatusValue != null and submitStatusValue != ''">
                and t.submit_status_value = #{submitStatusValue}
            </if>
        </where>
        <include refid="groupOrder"/>
    </select>
</mapper>

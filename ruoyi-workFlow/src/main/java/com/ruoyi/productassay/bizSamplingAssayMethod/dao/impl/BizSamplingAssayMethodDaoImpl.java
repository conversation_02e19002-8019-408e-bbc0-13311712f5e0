package com.ruoyi.productassay.bizSamplingAssayMethod.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.productassay.bizSamplingAssayMethod.dao.IBizSamplingAssayMethodDao;
import com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod;
import com.ruoyi.productassay.bizSamplingAssayMethod.mapper.BizSamplingAssayMethodMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor
public class BizSamplingAssayMethodDaoImpl extends BaseDaoImpl<BizSamplingAssayMethodMapper, BizSamplingAssayMethod> implements

        IBizSamplingAssayMethodDao {
}

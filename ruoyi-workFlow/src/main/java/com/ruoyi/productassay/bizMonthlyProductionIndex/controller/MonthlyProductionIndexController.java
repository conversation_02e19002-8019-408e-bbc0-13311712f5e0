package com.ruoyi.productassay.bizMonthlyProductionIndex.controller;

import com.ruoyi.common.util.StringUtil;
import com.ruoyi.productassay.bizMonthlyProductionIndex.dao.IMonthlyProductionIndexDao;
import com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex;
import com.ruoyi.productassay.bizMonthlyProductionIndex.service.MonthlyProductionIndexService;
import com.ruoyi.productionplan.year.domain.SupportYearPlanSub;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;
import java.util.List;

import com.ruoyi.common.util.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/workflow/monthlyProductionIndex")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class MonthlyProductionIndexController extends BaseController implements IBaseController {

    private final IMonthlyProductionIndexDao dao;
    private final MonthlyProductionIndexService service;

    @GetMapping
    public AjaxResult list(MonthlyProductionIndex entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @GetMapping("/getList")
    public AjaxResult getList(MonthlyProductionIndex entity) {
        return success(getPageInfo(() -> service.getList(entity)));
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( MonthlyProductionIndex entity) {
        new ExcelUtil<>(MonthlyProductionIndex. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "月度生产指标分析表数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MonthlyProductionIndex entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MonthlyProductionIndex entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStatus")
    public AjaxResult updateStatus(@RequestBody MonthlyProductionIndex entity) {
        return toAjax(service.updateStatus(entity.getId()));
    }
}

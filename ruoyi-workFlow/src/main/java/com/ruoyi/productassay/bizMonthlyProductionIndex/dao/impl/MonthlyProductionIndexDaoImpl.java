package com.ruoyi.productassay.bizMonthlyProductionIndex.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.productassay.bizMonthlyProductionIndex.dao.IMonthlyProductionIndexDao;
import com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex;
import com.ruoyi.productassay.bizMonthlyProductionIndex.mapper.MonthlyProductionIndexMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class MonthlyProductionIndexDaoImpl extends BaseDaoImpl<MonthlyProductionIndexMapper, MonthlyProductionIndex> implements IMonthlyProductionIndexDao {
}

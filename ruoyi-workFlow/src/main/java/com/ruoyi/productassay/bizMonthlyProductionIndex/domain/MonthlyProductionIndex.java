package com.ruoyi.productassay.bizMonthlyProductionIndex.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_monthly_production_index")
public class MonthlyProductionIndex extends BaseEntity<MonthlyProductionIndex> {
    /**
     * 指标名称
     */
    @Excel(name = "指标名称")
    @ExcelProperty("指标名称")
    private String name;

    /**
     * 指标类别
     */
    @Excel(name = "指标类别")
    @ExcelProperty("指标类别")
    private String typeName;


    private String currentUseValue;
    //dept_type
    private String deptType;


    private Long orderIndex;
    /**
     * 单位
     */
    @Excel(name = "单位")
    @ExcelProperty("单位")
    private String measurementUnitValue;


    // 年集团计划责任指标
    private Long groupPlanTarget;

    // 年内部责任指标
    private Long internalTarget;

    // 上上月累计下达计划
    private Long lastLastMonthEndPassdownPlan;

    // 上上月份预计完成
    private Long lastLastMonthEndPassdownCompl;

    // 上月份计划
    private Long lastMonthPlan;

    // 上月份预计完成
    private Long lastMonthExpectCompl;

    // 上月月份完成率
    private Long lastMonthComplRate;

    // 1月到上月份累计预完成
    private Long lastMonthEndExpectTotalCompl;

    // 1月到上月份累计预完成率
    private Long lastMonthEndExpectTotalComplRate;

    // 当月基本任务
    private Long curMonthTask;

    // 当月奋斗指标
    private Long curMonthFightTarget;

    // 截至当月份累计预完成
    private Long curMonthEndExpectTotalCompl;

    // 年度内部指标预完成率
    private Long curMonthEndExpectTotalComplRate;

}

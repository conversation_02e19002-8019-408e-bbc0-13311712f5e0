package com.ruoyi.productassay.bizSamplingAssayResult.controller;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.SysDictData;
import com.ruoyi.common.domain.SysDictType;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.SecurityUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productassay.bizSamplingAssayResult.dao.IBizSamplingAssayResultDao;
import com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult;
import com.ruoyi.productassay.bizSamplingAssayResult.service.IBizSamplingAssayResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.Console;
import java.util.Arrays;
import java.util.Date;

@RestController
@RequestMapping("/workflow/bizSamplingAssayResult")
@RequiredArgsConstructor
public class BizSamplingAssayResultController extends BaseController implements IBaseController {

    private final IBizSamplingAssayResultDao bizSamplingAssayResultDao;
    private final IBizSamplingAssayResultService bizSamplingAssayResultService;

    @GetMapping
    public AjaxResult list(BizSamplingAssayResult bizSamplingAssayResult) {
        return success(getPageInfo(() -> bizSamplingAssayResultDao.list(bizSamplingAssayResult, DeepConfig.<BizSamplingAssayResult>builder().loop(true).deep(3).build())));
    }

    @GetMapping("/listSamplingAssayResult")
    public AjaxResult listSamplingAssayResult(BizSamplingAssayResult bizSamplingAssayResult) {
        return success(getPageInfo(() -> bizSamplingAssayResultDao.listSamplingAssayResult(bizSamplingAssayResult)));
    }


    @Log(title = "化验室化验结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizSamplingAssayResult bizSamplingAssayResult) {
        new ExcelUtil<>(BizSamplingAssayResult.class).exportExcel(response, getPageInfo(() -> bizSamplingAssayResultDao.list(bizSamplingAssayResult)).getList(), "化验室化验结果数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizSamplingAssayResultDao.getByIdDeep(Long.valueOf(id)));
    }


    @Log(title = "化验室化验结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizSamplingAssayResult bizSamplingAssayResult) {
        return bizSamplingAssayResultDao.save(bizSamplingAssayResult) ? toAjax(bizSamplingAssayResult.getId()) : toAjax(false);
    }

    @Log(title = "化验室化验结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizSamplingAssayResult bizSamplingAssayResult) {
        return bizSamplingAssayResultDao.updateById(bizSamplingAssayResult) ? toAjax(bizSamplingAssayResult.getId()) : toAjax(false);
    }

    @Log(title = "化验室化验结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizSamplingAssayResultDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    /**
     * 化验结果审核
     * @param ids
     * @return
     */
    @GetMapping("/auditAssayResult/{ids}")
    public AjaxResult auditAssayResult(@PathVariable String[] ids) {

        for (String id : ids) {

            BizSamplingAssayResult result =   bizSamplingAssayResultDao.getByIdDeep(Long.valueOf(id));
            if(result.getAuditStatusValue().equals("0")) {
                result.setAuditStatusValue("1");

                result.setAuditTime(new Date());
                result.setAuditById(SecurityUtil.getLoginUser().getUserId().toString());

                result.setUnauditTime(null);
                result.setUnauditById(null);
            }
            bizSamplingAssayResultDao.updateById(result);
        }

        return toAjax(1);
    }

    @GetMapping("/unAuditAssayResult/{ids}")
    public AjaxResult unAuditAssayResult(@PathVariable String[] ids) {
        log.info(ids.toString());
        for (String id : ids) {

            BizSamplingAssayResult result =   bizSamplingAssayResultDao.getByIdDeep(Long.valueOf(id));
            if(result.getAuditStatusValue().equals("1")){
                result.setAuditStatusValue("0");
                result.setUnauditTime(new Date());
                result.setUnauditById(SecurityUtil.getLoginUser().getUserId().toString());

                result.setAuditTime(null);
                result.setAuditById(null);
                bizSamplingAssayResultDao.updateById(result);
            }

        }
        return toAjax(1);
    }






}

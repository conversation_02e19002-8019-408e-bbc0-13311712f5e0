package com.ruoyi.productassay.sampling.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod;
import com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_sampling")
public class Sampling extends BaseEntity<Sampling> {
    /**
     * 探矿、采矿、选矿、物资化验
     */
    @Excel(name = "采样类型", dictType = "sampling_type")
    private String samplingTypeValue;

    @TableField(exist = false)
    private List<String> samplingTypeList;

    /**
     * 纸质样品号
     */
    @Excel(name = "纸质样品号")
    private String paperCode;

    /**
     * 样品编码
     */
    @Excel(name = "样品编码")
    private String samplingCode;

    /**
     * 取\送样人id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long samplingUserId;



    @Excel(name = "取样人")
    @TableField(exist = false)
    @FieldMapping(tag = SysUser.class,thisField = Sampling.Fields.samplingUserId,joinField = SysUser.Fields.userId, select = SysUser.Fields.nickName)
    private String samplingUserName;

    /**
     * 采\取样时间
     */
    @Excel(name = "取样时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date samplingTime;

    /**
     * 化验类型
     */
    @Excel(name = "化验类型", dictType = "assay_type")
    private String assayTypeValue;

    /**
     * 采样地点
     */

    /**
     * 采样地点id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long placeId;

    /**
     * 采场地点
     */
    @TableField(exist = false)
    private String stopeLevelNum;

    @TableField(exist = false)
    private String plangName;

    @TableField(exist = false)
    private String deptName;

    @TableField(exist = false)
    private String placeName;

    /**
     * 探矿类型：（字典：地质探矿、生产探矿）
     */
    @Excel(name = "探矿类型：", dictType = "exploration_type")
    private String explorationTypeValue;

    /**
     * 样长
     */
    @Excel(name = "样长")
    private String sampleLength;
    /**
     * 是否钻孔样
     */
    @Excel(name = "是否钻孔样")
    private String drillingIs;

    /**
     * 样品单位
     */
    @Excel(name = "样品单位")
    @TableField(exist = false)
    @FieldMapping(tag = SysDept.class, thisField = Sampling.Fields.sampleDeptId, joinField = SysDept.Fields.deptId, select = SysDept.Fields.deptName)
    private String sampleDeptName;

    /**
     * 样品单位id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long sampleDeptId;

    /**
     * 样品类型
     */
    @Excel(name = "样品类型")
    private String sampleTypeValue;

    /**
     * 班次
     */
    @Excel(name = "班次")
    private String workShiftValue;

    /**
     * 样品描述
     */
    @Excel(name = "样品描述")
    private String samplingDescribe;


    @Excel(name = "化验用途")
    private String assayPurposeValue;



//   2025.03.22 增加审核状态 by guanzpjune
//    @Excel(name = "审核状态", dictType = "audit_status")
//    private String auditStatusValue;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 取样日期(时间段) 开始时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startSamplingTime;

    /**
     * 取样日期(时间段) 结束时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endSamplingTime;

    /**
     * 化验结果 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = BizSamplingAssayResult.Fields.parentId)
    private List<BizSamplingAssayResult> bizSamplingAssayResultList = new ArrayList<>();

    /**
     * 化验方法 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = BizSamplingAssayMethod.Fields.parentId)
    private List<BizSamplingAssayMethod> bizSamplingAssayMethodList = new ArrayList<>();

    @TableField(exist = false)
    private String SamplingAssayName;


    @TableField(exist = false)
    private Long deptId;


    @TableField(exist = false)
    private Long year;

    @TableField(exist = false)
    private Long month;
    @TableField(exist = false)
    private String grade;
    @TableField(exist = false)
    private String assayMethodName;
    @TableField(exist = false)
    private Long totalNumber;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    // 开始和结束时间
    @TableField(exist = false)
    private Date startDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date endDate;

    //查询的年月
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    @TableField(exist = false)
    private Date yearmonth;




}

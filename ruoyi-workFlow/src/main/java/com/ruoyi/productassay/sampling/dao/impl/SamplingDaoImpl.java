package com.ruoyi.productassay.sampling.dao.impl;

import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.archives.Stope.dao.IStopeDao;
import com.ruoyi.archives.Stope.domain.Stope;
import com.ruoyi.archives.projectItem.dao.ProjectItemDao;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.productassay.bizSamplingAssayMethod.dao.IBizSamplingAssayMethodDao;
import com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod;
import com.ruoyi.productassay.bizSamplingAssayResult.dao.IBizSamplingAssayResultDao;
import com.ruoyi.productassay.bizSamplingAssayResult.domain.BizSamplingAssayResult;
import com.ruoyi.productassay.sampling.dao.ISamplingDao;
import com.ruoyi.productassay.sampling.domain.Sampling;
import com.ruoyi.productassay.sampling.mapper.SamplingMapper;
import com.ruoyi.system.dao.ISysUserDao;
import lombok.RequiredArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SamplingDaoImpl extends BaseDaoImpl<SamplingMapper, Sampling> implements ISamplingDao {
    private final IBizSamplingAssayResultDao iBizSamplingAssayResultDao;
    private final IBizSamplingAssayMethodDao iBizSamplingAssayMethodDao;
    private final ProjectItemDao projectItemDao;
    private final IStopeDao stopeDao;
    private final ISysUserDao iSysUserDao;

    @Override
    public IBizSamplingAssayResultDao iBizSamplingAssayResultDao() {
        return iBizSamplingAssayResultDao;
    }

    @Override
    public IBizSamplingAssayMethodDao iBizSamplingAssayMethodDao() {
        return iBizSamplingAssayMethodDao;
    }

    /**
     * 通过采样编码获取一条采样信息
     *
     * @param samplingCode String 采样编码（自动生成、唯一）
     * @return Sampling 采样信息
     */
    @Override
    public Sampling getOneBySamplingCode(String samplingCode) {
        Sampling sampling = lambdaQuery().eq(Sampling::getSamplingCode, samplingCode).one();

        if (sampling == null) {
            return null;
        }
        SysUser sysUser = iSysUserDao.getById(sampling.getSamplingUserId());
        if (sampling.getPlaceId() != null && sampling.getSamplingTypeValue().equals("10")) {
            ProjectItem projectItem = projectItemDao.getById(sampling.getPlaceId());
            sampling.setPlaceName(projectItem.getName());
        }
        if (sampling.getPlaceId() != null && sampling.getSamplingTypeValue().equals("20")) {
            Stope projectItem = stopeDao.getById(sampling.getPlaceId());
            sampling.setPlaceName(projectItem.getStopeLevelNum());
        }
        if (sysUser!=null)
        sampling.setSamplingUserName(sysUser.getNickName());
        List<BizSamplingAssayResult> bizSamplingAssayResult = iBizSamplingAssayResultDao.lambdaQuery().eq(BizSamplingAssayResult::getParentId, sampling.getId()).list();
        List<BizSamplingAssayMethod> bizSamplingAssayMethod = iBizSamplingAssayMethodDao.lambdaQuery().eq(BizSamplingAssayMethod::getParentId, sampling.getId()).list();
        if (bizSamplingAssayMethod != null && bizSamplingAssayResult != null) {
            sampling.setBizSamplingAssayResultList(bizSamplingAssayResult);
            sampling.setBizSamplingAssayMethodList(bizSamplingAssayMethod);
        }
        return sampling;
    }

    @Override
    public boolean removePaperCode(String paperCode) {
        // 判断paperCode 是否存在
        boolean result = lambdaQuery().eq(Sampling::getPaperCode, paperCode).exists();
        if (result) {
            return false;
        }
        return true;
    }


    @Override
    public Sampling getListByPaperCode(String paperCode) {
        Sampling sampling = lambdaQuery().eq(Sampling::getPaperCode, paperCode).one();
        List<BizSamplingAssayResult> bizSamplingAssayResult = iBizSamplingAssayResultDao.lambdaQuery().eq(BizSamplingAssayResult::getParentId, sampling.getId()).list();
        List<BizSamplingAssayMethod> bizSamplingAssayMethod = iBizSamplingAssayMethodDao.lambdaQuery().eq(BizSamplingAssayMethod::getParentId, sampling.getId()).list();
        if (bizSamplingAssayMethod != null && bizSamplingAssayResult != null) {
            sampling.setBizSamplingAssayResultList(bizSamplingAssayResult);
            sampling.setBizSamplingAssayMethodList(bizSamplingAssayMethod);
        }
        return sampling;
    }

    @Override
    public List<Sampling> listProspecting(Sampling entity) {
        return baseMapper.listProspecting(entity);
    }

    @Override
    public List<Sampling> listMining(Sampling entity) {
        return baseMapper.listMining(entity);
    }

    @Override
    public List<Sampling> listMineral(Sampling entity) {
        return baseMapper.listMineral(entity);
    }

    @Override
    public List<Sampling> listBizMaterialAssay(Sampling entity) {
        return baseMapper.listBizMaterialAssay(entity);
    }

    @Override
    public List<Sampling> listByMineralexperiment(Sampling entity) {
        return baseMapper.listByMineralexperiment(entity);
    }

    @Override
    public List<Sampling> listProductionLaboratoryReport(Sampling entity) {
        return baseMapper.listProductionLaboratoryReport(entity);
    }

    @Override
    public List<Sampling> listByMineralexperimentReport(Sampling entity) {
        return baseMapper.listByMineralexperimentReport(entity);
    }

    public List<String> listSamplingTime(@Param("startDate") Date startDate, @Param("endDate") Date endDate) {
        return baseMapper.listSamplingTime(startDate, endDate);
    }

    public List<JSONObject> listByMineralexperimentForReport(List<String> columnsList, @Param("startDate") Date startDate, @Param("endDate") Date endDate) {
        return baseMapper.listByMineralexperimentForReport(columnsList, startDate, endDate);
    }

}

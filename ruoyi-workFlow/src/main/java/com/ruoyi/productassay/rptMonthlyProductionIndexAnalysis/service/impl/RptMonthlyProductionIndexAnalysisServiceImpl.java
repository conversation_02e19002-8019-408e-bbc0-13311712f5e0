package com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.productassay.bizMonthlyProductionIndex.dao.IMonthlyProductionIndexDao;
import com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.dao.IRptMonthlyProductionIndexAnalysisDao;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.dao.IRptMonthlyProductionIndexAnalysisSubDao;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.service.RptMonthlyProductionIndexAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class RptMonthlyProductionIndexAnalysisServiceImpl implements RptMonthlyProductionIndexAnalysisService {

    private final IRptMonthlyProductionIndexAnalysisDao rptMonthlyProductionIndexAnalysisDao;
    private final IRptMonthlyProductionIndexAnalysisSubDao rptMonthlyProductionIndexAnalysisSubDao;
    private final IMonthlyProductionIndexDao monthlyProductionIndexDao;
    @Override
    public boolean isRepeat(RptMonthlyProductionIndexAnalysis entity) {
        // 根据 部门 年度 月度 查询数据是否存在
        boolean isRepeat = rptMonthlyProductionIndexAnalysisDao.lambdaQuery()
                .eq(RptMonthlyProductionIndexAnalysis::getDeptId, entity.getDeptId())
                .eq(RptMonthlyProductionIndexAnalysis::getYear, entity.getYear())
                .eq(RptMonthlyProductionIndexAnalysis::getMonth, entity.getMonth())
                .count() > 0;
                if (isRepeat) {
                    return true;
                }else {
                    throw new RuntimeException("数据已存在");
                }
    }

    @Override
    public List<RptMonthlyProductionIndexAnalysisSub> getRptMonthlyProductionIndexAnalysisSubList(RptMonthlyProductionIndexAnalysis entity) {
        if (entity.getYear() == null){
            throw new RuntimeException("年度不能为空");
        }
        if (entity.getMonth() == null){
            throw new RuntimeException("月度不能为空");
        }
        return Optional.ofNullable(
                        rptMonthlyProductionIndexAnalysisDao.lambdaQuery()
                                .eq(RptMonthlyProductionIndexAnalysis::getDeptId, entity.getDeptId())
                                .eq(RptMonthlyProductionIndexAnalysis::getYear, entity.getYear())
                                .eq(RptMonthlyProductionIndexAnalysis::getMonth, entity.getMonth())
                                .one())
                .map(parent ->
                        rptMonthlyProductionIndexAnalysisSubDao.lambdaQuery()
                                .eq(RptMonthlyProductionIndexAnalysisSub::getParentId, parent.getId())
                                .list()
                                .stream()
                                .peek(sub -> {
                                    MonthlyProductionIndex index = monthlyProductionIndexDao.lambdaQuery()
                                            .eq(MonthlyProductionIndex::getId, sub.getProIndexId())
                                            .one();
                                    sub.builder()
                                            .proIndexName(index.getName())
                                            .proIndexTypeName(index.getTypeName())
                                            .build();
                                })
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList()); // 返回空列表而不是抛出异常
    }

    public List<RptMonthlyProductionIndexAnalysisSub> getRptSubList(RptMonthlyProductionIndexAnalysis entity) {

        return  rptMonthlyProductionIndexAnalysisSubDao.lambdaQuery()
                .eq(RptMonthlyProductionIndexAnalysisSub::getParentId, entity.getId())
                .list();
    }

    @Override
    public void updateRptMonthlyProductionIndexAnalysisSub(RptMonthlyProductionIndexAnalysis entity) {
        List<RptMonthlyProductionIndexAnalysisSub> subList = entity.getRptMonthlyProductionIndexAnalysisSubList();

        // 原矿金属量
       RptMonthlyProductionIndexAnalysisSub rawMetalContent = subList.stream()
                .filter(x -> x.getProIndexId()==26)
                .findFirst().orElse(null);

        // 选矿处理矿量合计
       RptMonthlyProductionIndexAnalysisSub beneficiation = subList.stream()
                .filter(x -> x.getProIndexId()==22)
                .findFirst().orElse(null);

        // 入选品位
       entity.getRptMonthlyProductionIndexAnalysisSubList().forEach(sub -> {
           if (sub.getProIndexId() == 25){
               // 入选品位 上月份预计完成
               if (rawMetalContent.getLastMonthExpectCompl() != null && rawMetalContent.getLastMonthExpectCompl().compareTo(BigDecimal.ZERO) != 0
              && beneficiation.getLastMonthExpectCompl() != null && beneficiation.getLastMonthExpectCompl().compareTo(BigDecimal.ZERO) != 0 ){
               sub.setLastMonthExpectCompl(
                       (rawMetalContent.getLastMonthExpectCompl().multiply(BigDecimal.valueOf(1000)))
                               .divide(beneficiation.getLastMonthExpectCompl(), BigDecimal.ROUND_HALF_UP));
               }else {
                   sub.setLastMonthExpectCompl(BigDecimal.ZERO);
               }
               // 入选品位 上月份完成率
               sub.setLastMonthComplRate(
                       sub.getLastMonthExpectCompl().subtract(sub.getLastMonthPlan())
               );
               //  至(当月-1)预完成
               if (rawMetalContent.getLastMonthEndExpectTotalCompl() != null && rawMetalContent.getLastMonthEndExpectTotalCompl().compareTo(BigDecimal.ZERO) != 0
                       && beneficiation.getLastMonthEndExpectTotalCompl() != null && beneficiation.getLastMonthEndExpectTotalCompl().compareTo(BigDecimal.ZERO) != 0 ){
                   sub.setLastMonthEndExpectTotalCompl(
                           (rawMetalContent.getLastMonthEndExpectTotalCompl().multiply(BigDecimal.valueOf(1000)))
                                   .divide(beneficiation.getLastMonthEndExpectTotalCompl(), BigDecimal.ROUND_HALF_UP));
               }else {
                   sub.setLastMonthEndExpectTotalCompl(BigDecimal.ZERO);
               }

               //  至(当月-1)完成率
                sub.setLastMonthEndExpectTotalComplRate(
                        sub.getLastMonthEndExpectTotalCompl().subtract(sub.getInternalTarget())
                );
                // 当月基本任务
               if (rawMetalContent.getCurMonthTask() != null && rawMetalContent.getCurMonthTask().compareTo(BigDecimal.ZERO) != 0
                       && beneficiation.getCurMonthTask() != null && beneficiation.getCurMonthTask().compareTo(BigDecimal.ZERO) != 0 ){
                   sub.setCurMonthTask(
                           (rawMetalContent.getCurMonthTask().multiply(BigDecimal.valueOf(1000)))
                                   .divide(beneficiation.getCurMonthTask(), BigDecimal.ROUND_HALF_UP)
                   );
               }else {
                   sub.setCurMonthTask(BigDecimal.ZERO);
               }

                // 当月奋斗指标
               if (rawMetalContent.getCurMonthFightTarget() != null && rawMetalContent.getCurMonthFightTarget().compareTo(BigDecimal.ZERO) != 0
                       && beneficiation.getCurMonthFightTarget() != null && beneficiation.getCurMonthFightTarget().compareTo(BigDecimal.ZERO) != 0 ){
                   sub.setCurMonthFightTarget(
                           (rawMetalContent.getCurMonthFightTarget().multiply(BigDecimal.valueOf(1000)))
                                   .divide(beneficiation.getCurMonthFightTarget(), BigDecimal.ROUND_HALF_UP)
                   );
               }else {
                   sub.setCurMonthFightTarget(BigDecimal.ZERO);
               }

                // 截止当月份累计预完成
               if (rawMetalContent.getCurMonthEndExpectTotalCompl() != null && rawMetalContent.getCurMonthEndExpectTotalCompl().compareTo(BigDecimal.ZERO) != 0
                       && beneficiation.getCurMonthEndExpectTotalCompl() != null && beneficiation.getCurMonthEndExpectTotalCompl().compareTo(BigDecimal.ZERO) != 0 ){
                   sub.setCurMonthEndExpectTotalCompl(
                           (rawMetalContent.getCurMonthEndExpectTotalCompl().multiply(BigDecimal.valueOf(1000)))
                                   .divide(beneficiation.getCurMonthEndExpectTotalCompl(), BigDecimal.ROUND_HALF_UP)
                   );
               }else {
                   sub.setCurMonthEndExpectTotalCompl(BigDecimal.ZERO);
               }

               //  当月内部指标预完成率
                sub.setCurMonthEndExpectTotalComplRate(
                        sub.getCurMonthEndExpectTotalCompl().subtract(sub.getInternalTarget())
                );
           }
       });
    }

    @Override
    public RptMonthlyProductionIndexAnalysis getProductMonthIndex(RptMonthlyProductionIndexAnalysis entity) {
        boolean exists =rptMonthlyProductionIndexAnalysisDao.lambdaQuery()
                .eq(RptMonthlyProductionIndexAnalysis::getYear, entity.getYear())
                .eq(RptMonthlyProductionIndexAnalysis::getMonth, entity.getMonth())
                .eq(RptMonthlyProductionIndexAnalysis::getDeptId, entity.getDeptId()).exists();
        if (exists){
            throw new ServiceException("该部门年月已存在");
        }
        return rptMonthlyProductionIndexAnalysisDao.getProductMonthIndex(entity);

    }

    @Override
    @DSTransactional
    public String importMonthlyProductionIndex(List<RptMonthlyProductionIndexAnalysisSub> mineOuputMonthPlanSubList,  String id) {

        int line = 1;
        int success = 1;
        List<RptMonthlyProductionIndexAnalysisSub> list = new ArrayList<>();
        for (RptMonthlyProductionIndexAnalysisSub sub : mineOuputMonthPlanSubList){
            line++;
            if (!sub.getParentId().toString().equals(id)){
                throw new ServiceException("第"+line+"条数据请勿修改上级ID");
            }
            list.add(sub);
        }
        for (RptMonthlyProductionIndexAnalysisSub sub : list){
            success++;
            boolean b = rptMonthlyProductionIndexAnalysisSubDao.updateById(sub);
            if (!b){
                throw new ServiceException("第"+success+"条数据保存失败");
            }
        }
        return "导入成功";
    }
}

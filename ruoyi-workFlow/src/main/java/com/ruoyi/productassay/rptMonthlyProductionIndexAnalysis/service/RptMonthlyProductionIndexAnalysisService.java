package com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.service;

import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub;

import java.util.List;

public interface RptMonthlyProductionIndexAnalysisService {

    // 判重
    boolean isRepeat(RptMonthlyProductionIndexAnalysis entity);


    /**
     * 获取月度生产指标分析子表
     * @param entity 父表实体
     * 根据 矿区  年度 月份 查询
     * @return list
     */
    List<RptMonthlyProductionIndexAnalysisSub> getRptMonthlyProductionIndexAnalysisSubList(RptMonthlyProductionIndexAnalysis entity);
    List<RptMonthlyProductionIndexAnalysisSub> getRptSubList(RptMonthlyProductionIndexAnalysis entity);

    // 根据穿过来的值进行传过来的值修改
    void updateRptMonthlyProductionIndexAnalysisSub(RptMonthlyProductionIndexAnalysis entity);

    RptMonthlyProductionIndexAnalysis getProductMonthIndex(RptMonthlyProductionIndexAnalysis entity);

    String importMonthlyProductionIndex(List<RptMonthlyProductionIndexAnalysisSub> mineOuputMonthPlanSubList, String id);
}

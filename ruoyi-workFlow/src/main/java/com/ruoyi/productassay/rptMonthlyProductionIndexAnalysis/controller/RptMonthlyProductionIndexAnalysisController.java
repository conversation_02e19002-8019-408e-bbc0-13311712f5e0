package com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productassay.bizMonthlyProductionIndex.domain.MonthlyProductionIndex;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.dao.IRptMonthlyProductionIndexAnalysisDao;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.service.RptMonthlyProductionIndexAnalysisService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/rptMonthlyProductionIndexAnalysis")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class RptMonthlyProductionIndexAnalysisController extends BaseController implements IBaseController {

    private final IRptMonthlyProductionIndexAnalysisDao dao;
    private final RptMonthlyProductionIndexAnalysisService service;

    @GetMapping
    public AjaxResult list(RptMonthlyProductionIndexAnalysis entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @GetMapping("/getProductMonthIndex")
    public AjaxResult getProductMonthIndex(RptMonthlyProductionIndexAnalysis entity) {
        return success(service.getProductMonthIndex(entity));
    }


    @GetMapping("/getRptMonthlyProductionIndexAnalysisSubList")
    public AjaxResult getRptMonthlyProductionIndexAnalysisSubList(RptMonthlyProductionIndexAnalysis entity) {
        return success(service.getRptMonthlyProductionIndexAnalysisSubList(entity));
    }
    @Log(title = "月度生产指标分析主表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( RptMonthlyProductionIndexAnalysis entity) {
        new ExcelUtil<>(RptMonthlyProductionIndexAnalysis. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "月度生产指标分析主表数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "月度生产指标分析主表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RptMonthlyProductionIndexAnalysis entity) {
        if (service.isRepeat(entity)) {
            return AjaxResult.error("数据已存在");
        }
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }


    @Log(title = "月度生产指标分析主表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RptMonthlyProductionIndexAnalysis entity) {
       service.updateRptMonthlyProductionIndexAnalysisSub(entity);
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "月度生产指标分析主表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "月度生产指标分析表", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSub")
    public void exportSub( RptMonthlyProductionIndexAnalysis entity) {
        new ExcelUtil<>(RptMonthlyProductionIndexAnalysisSub. class)
                .exportExcel(ServletUtils.getResponse(), service.getRptSubList(entity), "月度生产指标分析");
    }

    @Log(title = "矿区年度支护计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestParam("file") MultipartFile file, @RequestParam("id") String id) throws Exception {
        ExcelUtil<RptMonthlyProductionIndexAnalysisSub> util = new ExcelUtil<>(RptMonthlyProductionIndexAnalysisSub.class);
        List<RptMonthlyProductionIndexAnalysisSub> mineOuputMonthPlanSubList = null;
        String message = null;
        try {
            mineOuputMonthPlanSubList = util.importExcel(StringUtil.EMPTY, file.getInputStream(), 0);

        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }

        if (message == null) {
            message = service.importMonthlyProductionIndex(mineOuputMonthPlanSubList,id);
        }
        System.out.println(mineOuputMonthPlanSubList);
        return success(message);
    }
}

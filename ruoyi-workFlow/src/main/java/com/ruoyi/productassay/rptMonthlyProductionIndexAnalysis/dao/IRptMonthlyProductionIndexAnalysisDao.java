package com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.dao;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysis;
import com.ruoyi.productassay.rptMonthlyProductionIndexAnalysis.domain.RptMonthlyProductionIndexAnalysisSub;
import com.ruoyi.productionplan.yearPlan.domain.YearPlan;

public interface IRptMonthlyProductionIndexAnalysisDao extends IBaseDao<RptMonthlyProductionIndexAnalysis> {
    IRptMonthlyProductionIndexAnalysisSubDao iSubDao ();

    @DSTransactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateSub (RptMonthlyProductionIndexAnalysis entity) {
       return entity.getRptMonthlyProductionIndexAnalysisSubList().stream()
               .peek(x -> x.setParentId(entity.getId()))
               .allMatch(x -> Objects.nonNull(x.getId()) && Objects.nonNull(iSubDao().getById(x.getId())) ? iSubDao().updateById(x) : iSubDao().save(x))
               &&
               iSubDao().removeById(
                        iSubDao().lambdaQuery()
                             .eq(RptMonthlyProductionIndexAnalysisSub::getParentId, entity.getId())
                             .list().stream()
                                .map(RptMonthlyProductionIndexAnalysisSub::getId)
                                .filter(x ->
                                        !entity.getRptMonthlyProductionIndexAnalysisSubList().stream()
                                                .map(RptMonthlyProductionIndexAnalysisSub::getId)
                                                .collect(Collectors.toList())
                                                .contains(x)
                                ).toArray(Long[]::new)
               );
    }

    @DSTransactional(rollbackFor = Exception.class)
    default boolean removeSubByParentId(Serializable... id){
        return iSubDao().removeById(
                Arrays.stream(id)
                                .map(x ->
                                        iSubDao().lambdaQuery()
                                                .eq(RptMonthlyProductionIndexAnalysisSub::getParentId, x)
                                                .list()
                                ).flatMap(List::stream)
                                .map(RptMonthlyProductionIndexAnalysisSub::getId)
                                .toArray(Long[]::new)
        );
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean save (RptMonthlyProductionIndexAnalysis entity) {
        boolean rows = IBaseDao.super.save(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean updateById (RptMonthlyProductionIndexAnalysis entity) {
        boolean rows = IBaseDao.super.updateById(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean removeById (Serializable[] id) {
        boolean rows = IBaseDao.super.removeById(id);
        if (rows) {
            removeSubByParentId(id);
        }
        return rows;
    }

    RptMonthlyProductionIndexAnalysis getProductMonthIndex(RptMonthlyProductionIndexAnalysis entity);
}

package com.ruoyi.geology.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.geology.dao.ILossDepletionRateDao;
import com.ruoyi.geology.domain.LossDepletionRate;
import com.ruoyi.geology.mapper.LossDepletionRateMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class LossDepletionRateDaoImpl extends BaseDaoImpl<LossDepletionRateMapper, LossDepletionRate> implements ILossDepletionRateDao {
}

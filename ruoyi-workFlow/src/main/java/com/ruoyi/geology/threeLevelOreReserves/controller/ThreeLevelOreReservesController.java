package com.ruoyi.geology.threeLevelOreReserves.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.geology.threeLevelOreReserves.dao.IThreeLevelOreReservesDao;
import com.ruoyi.geology.threeLevelOreReserves.domain.ThreeLevelOreReserves;
import com.ruoyi.geology.threeLevelOreReserves.service.ThreeLevelOreReservesService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/threeLevelOreReserves")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ThreeLevelOreReservesController extends BaseController implements IBaseController {

    private final IThreeLevelOreReservesDao dao;
    private final ThreeLevelOreReservesService service;

    @GetMapping
    public AjaxResult list(ThreeLevelOreReserves entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "保有三级矿量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ThreeLevelOreReserves entity) {
        new ExcelUtil<>(ThreeLevelOreReserves.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "保有三级矿量数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "保有三级矿量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ThreeLevelOreReserves entity) {
        boolean isOk = service.isItRepeated(entity);
        if (isOk) {
            return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
        } else {
            throw new ServiceException("该年、季度、矿种、单位已存在，请勿重复添加");
        }
    }

    @Log(title = "保有三级矿量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ThreeLevelOreReserves entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "保有三级矿量", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    // 获取中段
    @GetMapping("/getDepartments/{deptId}")
    public AjaxResult getDepartments(@PathVariable String deptId) {
        return success(service.selectParagraphList(deptId));
    }

    // 导出模板
    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) {
        new ExcelUtil<>(ThreeLevelOreReserves.class).importTemplateExcel(response, "金精矿化验结果");
    }

    @Log(title = "金精矿化验结果管理", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<ThreeLevelOreReserves> util = new ExcelUtil<>(ThreeLevelOreReserves.class);
        List<ThreeLevelOreReserves> availableResourcesList = null;
        String message = null;
        try {
            availableResourcesList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }
        String operName = getUsername();
        if (message == null) {
            message = service.importThreeLevelOreReservesSub(availableResourcesList, updateSupport, operName);
        }
        return success(message);
    }


}

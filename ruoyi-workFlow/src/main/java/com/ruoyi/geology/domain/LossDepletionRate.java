package com.ruoyi.geology.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_loss_depletion_rate")
public class LossDepletionRate extends BaseEntity<LossDepletionRate> {
    /**
     * 年
     */
    @Excel(name = "年")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 季度
     */
    @NotNull(message = "月份不能为空")
    @Excel(name = "月")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String month;

    /**
     * 矿种
     */
    @NotNull(message = "矿种不能为空")
    @Excel(name = "矿种", dictType = "mineral_species", width = 8)
    private String mineralSpeciesValue;

    /**
     * 单位(矿区)
     */
    //@Excel(name = "矿区")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    @Excel(name = "矿区")
    @FieldMapping(tag = SysDept.class, thisField = LossDepletionRate.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    /**
     * 中段
     */
//            @Excel(name = "中段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;

    /**
     * 中段名称
     */
    @Excel(name = "中段名称")
    private String paragraphName;

    /**
     * 地质储量-矿石量
     */
    @Excel(name = "地质储量-矿石量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的矿石量")
    @DecimalMin(value = "0.00", message = "请输入正确的矿石量")
    private BigDecimal developOreQuantity;

    /**
     * 地质储量-品位
     */
    @Excel(name = "地质储量-品位")
    private BigDecimal developGrade;

    /**
     * 地质储量-金属量
     */
    @Excel(name = "地质储量-金属量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的金属量")
    @DecimalMin(value = "0.00", message = "请输入正确的金属量")
    private BigDecimal developMetallicity;

    /**
     * 采出矿岩量-矿石量
     */
    @Excel(name = "采出矿岩量-矿石量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的矿石量")
    @DecimalMin(value = "0.00", message = "请输入正确的矿石量")
    private BigDecimal outRockOreQuantity;

    /**
     * 采出矿岩量-品位
     */
    @Excel(name = "采出矿岩量-品位")
    private BigDecimal outRockGrade;

    /**
     * 采出矿岩量-金属量
     */
    @Excel(name = "采出矿岩量-金属量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的金属量")
    @DecimalMin(value = "0.00", message = "请输入正确的金属量")
    private BigDecimal outRockMetallicity;


    /**
     * 采出矿岩量-矿石量
     */
    @Excel(name = "采出矿石量-矿石量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的矿石量")
    @DecimalMin(value = "0.00", message = "请输入正确的矿石量")
    private BigDecimal outOreQuantity;

    /**
     * 采出矿岩量-品位
     */
    @Excel(name = "采出矿石量-品位")
    private BigDecimal outOreGrade;

    /**
     * 采出矿岩量-金属量
     */
    @Excel(name = "采出矿石量-金属量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的金属量")
    @DecimalMin(value = "0.00", message = "请输入正确的金属量")
    private BigDecimal outOreMetallicity;


    /**
     * 采出废石量-矿石量
     */
    @Excel(name = "采出废石量-矿石量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的矿石量")
    @DecimalMin(value = "0.00", message = "请输入正确的矿石量")
    private BigDecimal outWasteRockOreQuantity;

    /**
     * 采出废石量-品位
     */
    @Excel(name = "采出废石量-品位")
    private BigDecimal outWasteRockGrade;

    /**
     * 采出废石量-金属量
     */
    @Excel(name = "采出废石量-金属量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的金属量")
    @DecimalMin(value = "0.00", message = "请输入正确的金属量")
    private BigDecimal outWasteRockMetallicity;

    /**
     * 损失矿量-矿石量
     */
    @Excel(name = "损失矿量-矿石量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的矿石量")
    @DecimalMin(value = "0.00", message = "请输入正确的矿石量")
    private BigDecimal lossOreQuantity;

    /**
     * 损失矿量-品位
     */
    @Excel(name = "损失矿量-品位")
    private BigDecimal lossGrade;

    /**
     * 损失矿量-金属量
     */
    @Excel(name = "损失矿量-金属量")
    @DecimalMax(value = "9999999999.99", message = "请输入正确的金属量")
    @DecimalMin(value = "0.00", message = "请输入正确的金属量")
    private BigDecimal lossMetallicity;

    /**
     * 损失率(%)
     */
    @Excel(name = "损失率(%)")
    @DecimalMax(value = "999.99", message = "请输入正确的损失率")
    @DecimalMin(value = "0.00", message = "请输入正确的损失率")
    private BigDecimal lossRate;

    /**
     * 贫化率(%)
     */
    @DecimalMax(value = "999.99", message = "请输入正确的贫化率")
    @DecimalMin(value = "0.00", message = "请输入正确的贫化率")
    @Excel(name = "贫化率(%)")
    private BigDecimal depletionRate;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    @Excel(name = "备注")
    private String remark;
}

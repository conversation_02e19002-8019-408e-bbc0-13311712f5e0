package com.ruoyi.geology.reserveschange.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.geology.reserveschange.domain.ReservesChange;
import com.ruoyi.geology.reserveschange.dao.IReservesChangeDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("/workflow/ReservesChange")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ReservesChangeController extends BaseController implements IBaseController {

    private final IReservesChangeDao dao;

    @GetMapping
    public AjaxResult list(ReservesChange entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "采矿权储量变动管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( ReservesChange entity) {
        new ExcelUtil<>(ReservesChange. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "采矿权储量变动管理数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "采矿权储量变动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ReservesChange entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "采矿权储量变动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ReservesChange entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "采矿权储量变动管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }


}

package com.ruoyi.indicator.bizAssessmentDocuments.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_assessment_documents")
public class BizAssessmentDocuments extends ProcessEntity<BizAssessmentDocuments> {
    /**
     * 考核类型（数据字典：未完成规定指标/优化制/工作过失/工作优秀）
     */
    @Excel(name = "考核类型")
    @ExcelProperty("考核类型")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String documentType;

    /**
     * 考核文件编号
     */
    @Excel(name = "考核文件编号")
    @ExcelProperty("考核文件编号")
    private String documentCode;

    /**
     * 考核文件名称
     */
    @Excel(name = "考核文件名称")
    @ExcelProperty("考核文件名称")
    private String documentName;

    /**
     * 考核人ID（人员选择）
     */
    @Excel(name = "考核人Id")
    @ExcelProperty("考核人")
    private String evaluatorId;

    /**
     * 考核人（人员选择）
     */
    @Excel(name = "考核人")
    @ExcelProperty("考核人")
    @TableField(exist = false)
    private String evaluatorName;


    /**
     * 考核人部门ID（部门选择）
     */
    @Excel(name = "考核人部门")
    @ExcelProperty("考核人部门")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long evaluatorDept;

    /**
     * 考核人部门（部门选择）
     */
    @Excel(name = "考核人部门")
    @ExcelProperty("考核人部门")
    @TableField(exist = false)
//    @FieldMapping(tag = ProjectImplementApply.class, thisField = BizAssessmentDocuments.Fields.evaluatorDept, joinField = ProjectImplementApply.Fields.projectCode, select = "projectName")
    private String evaluatorDeptName;

    /**
     * 被考核人ID（人员选择）
     */
    @Excel(name = "被考核人")
    @ExcelProperty("被考核人")
    private String rateeId;

    /**
     * 被考核人（人员选择）
     */
    @Excel(name = "被考核人")
    @ExcelProperty("被考核人")
    @TableField(exist = false)
    private String rateeName;

    /**
     * 被考核人部门（部门选择）
     */
    @Excel(name = "被考核人部门")
    @ExcelProperty("被考核人部门")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long rateeDept;

    /**
     * 被考核人部门ID（部门选择）
     */
    @Excel(name = "被考核人部门")
    @ExcelProperty("被考核人部门")
    @TableField(exist = false)
    @FieldMapping(tag = SysDept.class, thisField = Fields.rateeDept, select = SysDept.Fields.deptName)
    private String rateeDeptName;

    /**
     * 是否考核：0正常、1执行考核、2申诉取消
     */
    @Excel(name = "是否考核")
    @ExcelProperty("是否考核")
    private String ifEvaluate;

    /**
     * 附件
     */
    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;

    /**
     * 考核说明
     */
    @Excel(name = "考核说明")
    @ExcelProperty("考核说明")
    private String evaluatorInstructions;


    @TableField(exist = false)
    @EntityMapping(joinField = BizAssessmentDocumentsItems.Fields.parentId)
    private List<BizAssessmentDocumentsItems> bizAssessmentDocumentsItemsList = new ArrayList<>();

    //考核月份
    @TableField(exist = false)
    private String assessmentMonth;

    //考核年份
    @TableField(exist = false)
    private String assessmentYear;

    //考核次数
    @TableField(exist = false)
    private String numberOfAssessments;

    //执行考核次数
    @TableField(exist = false)
    private String numberOfTimesTheAssessments;

    //申诉取消次数
    @TableField(exist = false)
    private String numberOfAppealCanceled;

    //考核总金额
    @TableField(exist = false)
    private String totalAmountOfAssessment;

    //执行金额
    @TableField(exist = false)
    private String totalAmountOfExecution;

    //申诉取消金额
    @TableField(exist = false)
    private String totalAmountOfAppealCanceled;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date endTime;
}

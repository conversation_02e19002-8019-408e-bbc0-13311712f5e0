package com.ruoyi.indicator.bizIndicatorQualitative.dao;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.indicator.bizIndicatorQualitative.domain.bizIndicatorQualitative;

public interface IbizIndicatorQualitativeDao extends IBaseDao<bizIndicatorQualitative> {
}

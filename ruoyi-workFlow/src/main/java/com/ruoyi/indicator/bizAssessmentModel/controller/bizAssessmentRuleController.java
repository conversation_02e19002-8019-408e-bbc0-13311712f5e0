package com.ruoyi.indicator.bizAssessmentRule.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.indicator.bizAssessmentModel.domain.BizAssessmentRule;
import com.ruoyi.indicator.bizAssessmentModel.dao.IbizAssessmentRuleDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("/workflow/bizAssessmentRule")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class bizAssessmentRuleController extends BaseController implements IBaseController {

    private final IbizAssessmentRuleDao dao;

    @GetMapping
    public AjaxResult list(BizAssessmentRule entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "考核方案明细表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( BizAssessmentRule entity) {
        new ExcelUtil<>(BizAssessmentRule. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "考核方案明细表数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "考核方案明细表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizAssessmentRule entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "考核方案明细表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizAssessmentRule entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "考核方案明细表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }


}

package com.ruoyi.indicator.bizIndicatorResult.controller;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.indicator.GetIndicatorToken;
import com.ruoyi.indicator.bizAssessmentModel.common.DeptMineToken;
import com.ruoyi.indicator.bizAssessmentModel.dao.IbizAssessmentRuleDao;
import com.ruoyi.indicator.bizAssessmentModel.domain.BizAssessmentRule;
import com.ruoyi.indicator.bizAssessmentModel.domain.dto.SyncQuery;
import com.ruoyi.indicator.bizIndicatorManagement.common.Result;
import com.ruoyi.indicator.bizIndicatorResult.dao.IbizIndicatorResultDao;
import com.ruoyi.indicator.bizIndicatorResult.domain.BizIndicatorResult;
import com.ruoyi.indicator.bizIndicatorResult.service.IBizIndicatorResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/workflow/deptMine")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class BizDeptMineController extends BaseController implements IBaseController {
    @Autowired
    private IBizIndicatorResultService bizIndicatorResultService;

    @PostMapping("/getApiDeptMine")
    public String apiTest(@RequestBody SyncQuery query){
        return bizIndicatorResultService.ayncData(query);
    }
}

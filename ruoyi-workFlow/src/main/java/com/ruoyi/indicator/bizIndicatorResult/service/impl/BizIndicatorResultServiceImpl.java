package com.ruoyi.indicator.bizIndicatorResult.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.service.impl.BaseServiceImpl;
import com.ruoyi.indicator.GetIndicatorToken;
import com.ruoyi.indicator.GetTokenUtil;
import com.ruoyi.indicator.bizAssessmentModel.common.DeptMineToken;
import com.ruoyi.indicator.bizAssessmentModel.dao.IbizAssessmentRuleDao;
import com.ruoyi.indicator.bizAssessmentModel.domain.BizAssessmentRule;
import com.ruoyi.indicator.bizAssessmentModel.domain.dto.SyncQuery;
import com.ruoyi.indicator.bizIndicatorMain.dao.IbizIndicatorMainDao;
import com.ruoyi.indicator.bizIndicatorMain.domain.BizIndicatorMain;
import com.ruoyi.indicator.bizIndicatorResult.dao.IbizIndicatorResultDao;
import com.ruoyi.indicator.bizIndicatorResult.domain.BizIndicatorResult;
import com.ruoyi.indicator.bizIndicatorResult.service.IBizIndicatorResultService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.net.URI;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Optional;

@Service
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class BizIndicatorResultServiceImpl extends BaseServiceImpl implements IBizIndicatorResultService {
    @Autowired
    private DeptMineToken tokenConfig;

    @Autowired
    private GetIndicatorToken getToken;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;  // 存储 GetIndicatorToken

    private final RestTemplate restTemplate = new RestTemplate(); // 发送请求

    private final IbizAssessmentRuleDao assessmentRule;  //考核方案明细表

    private final IbizIndicatorMainDao indicatorMainDao; //指标信息表

    private final IbizIndicatorResultDao indicatorResultDao;  //考核结果表

    private final GetTokenUtil getTokenUtil;
    @Override
    public String ayncData(SyncQuery query) {
        String apiToken = getTokenUtil.getToken();
        HttpHeaders headers = new HttpHeaders();
        headers.set("apiToken", apiToken);
        String baseUrl = "http://" + getToken.getDataMetricsBaseUrl() + tokenConfig.getDataMetricsListPath();

        // 遍历每个部门ID进行数据处理
        for (Integer deptId : query.getDeptId()) {
            try {
                // 步骤1: 获取该部门的指标数据
                JSONArray rowData = fetchDataForDept(baseUrl, headers, query, deptId);
                if (rowData == null || rowData.isEmpty()) continue;
                // 步骤2: 处理获取到的指标数据
                processDataForDept(rowData, query, deptId);
            } catch (Exception e) {
                throw new ServiceException("处理部门ID " + deptId + " 数据时出错: " + e.getMessage());
            }
        }
        return "数据同步完成";
    }
    /**
     * 为指定部门获取指标数据
     * @param baseUrl 基础URL
     * @param headers HTTP头部
     * @param query 查询参数
     * @param deptId 部门ID
     * @return JSON格式的数据数组
     */
    private JSONArray fetchDataForDept(String baseUrl, HttpHeaders headers, SyncQuery query, int deptId) {
        // 构建带查询参数的URL
        URI dataUrl = UriComponentsBuilder.fromHttpUrl(baseUrl)
                .queryParam("page", 1)
                .queryParam("pageSize", 1000)
                .queryParam("startTime", query.getStartTime())
                .queryParam("endTime", query.getEndTime())
                .queryParam("dept_id", deptId)
                .build()
                .encode()
                .toUri();
        // 发送HTTP GET请求
        ResponseEntity<String> response = restTemplate.exchange(
                dataUrl,
                HttpMethod.GET,
                new HttpEntity<>(headers),
                String.class
        );
        // 解析响应数据
        JSONObject jsonResponse = JSON.parseObject(response.getBody());
        JSONObject data = jsonResponse.getJSONObject("data");
        return data != null ? data.getJSONArray("rowData") : null;
    }
    /**
     * 处理部门数据
     * @param rowData 指标数据数组
     * @param query 查询参数
     * @param deptId 部门ID
     */
    private void processDataForDept(JSONArray rowData, SyncQuery query, int deptId) {
        // 遍历每条指标数据
        for (int i = 0; i < rowData.size(); i++) {
            JSONObject row = rowData.getJSONObject(i);
            // 根据指标名称查找考核规则
            BizAssessmentRule rule = getAssessmentRuleByName(row.getString("index_name"));
            if (rule == null) continue; // 如果没有找到规则，跳过该指标
            processRuleRow(row, query, deptId, rule);// 处理单条指标数据
        }
    }
    /**
     * 根据名称查找考核规则
     * @param name 指标名称
     * @return 考核规则对象
     */
    private BizAssessmentRule getAssessmentRuleByName(String name) {
        QueryWrapper<BizAssessmentRule> wrapper = new QueryWrapper<>();
        wrapper.eq("indicator_name", name);
        return assessmentRule.getOne(wrapper);
    }
    /**
     * 处理单条指标规则
     * @param row 指标数据
     * @param query 查询参数
     * @param deptId 部门ID
     * @param rule 考核规则
     */
    private void processRuleRow(JSONObject row, SyncQuery query, int deptId, BizAssessmentRule rule) {
        try {
            // 解析目标值
            Object ttlValue = row.get("ttl");
            BigDecimal targetValue = new BigDecimal(ttlValue.toString());

            // 确定结果状态
            String resultStatus = determineResultStatus(rule, targetValue, row.getString("index_code"));

            // 创建并保存结果
            saveIndicatorResult(row, query, deptId, rule, targetValue, resultStatus);
        } catch (Exception e) {
            throw new ServiceException("处理指标 {} 时出错: {}" + row.getString("index_name") + e.getMessage());
        }
    }
    /**
     * 确定指标结果状态
     * @param rule 考核规则
     * @param targetValue 目标值
     * @param indexCode 指标编码
     * @return 结果状态描述
     */
    private String determineResultStatus(BizAssessmentRule rule, BigDecimal targetValue, String indexCode) {
        // 验证阈值
        if (rule.getUpperThreshold() == null || rule.getLowerThreshold() == null) {
            throw new ServiceException("指标 " + rule.getIndicatorName() + " 的阈值未设置");
        }
        // 检查指标属性
        BizIndicatorMain indicator = getIndicatorByCode(indexCode);
        // 布尔型指标处理：0=未通过，1=通过
        if (indicator != null && "1".equals(indicator.getIndicatorProperty())) {
            return targetValue.compareTo(BigDecimal.ZERO) == 0 ? "未通过" : "通过";
        }
        // 常规阈值比较
        if (targetValue.compareTo(rule.getLowerThreshold()) >= 0 &&
                targetValue.compareTo(rule.getUpperThreshold()) <= 0) {
            return "正常";
        }
        // 返回超出上限或低于下限状态
        return targetValue.compareTo(rule.getUpperThreshold()) > 0 ? "超出上限" : "低于下限";
    }
    /**
     * 根据指标编码获取指标信息
     * @param indexCode 指标编码
     * @return 指标对象
     */
    private BizIndicatorMain getIndicatorByCode(String indexCode) {
        QueryWrapper<BizIndicatorMain> wrapper = new QueryWrapper<>();
        wrapper.eq("indicator_code", indexCode);
        return indicatorMainDao.getOne(wrapper);
    }
    /**
     * 创建并保存指标结果
     */
    private void saveIndicatorResult(JSONObject row, SyncQuery query, int deptId,
                                     BizAssessmentRule rule, BigDecimal targetValue,
                                     String resultStatus) throws ParseException {
        // 创建新结果对象
        BizIndicatorResult result = new BizIndicatorResult();
        // 基础字段设置
        result.setResult(resultStatus);
        result.setIndicatorName(row.getString("index_name"));
        result.setUpperThreshold(rule.getUpperThreshold());
        result.setLowerThreshold(rule.getLowerThreshold());
        result.setActualValue(targetValue);
        result.setIndicatorId(rule.getIndicatorId());
        result.setIndicatorCode(row.getString("index_code"));
        result.setDeptId(String.valueOf(deptId));
        result.setAccountYear(Integer.valueOf(row.getString("dim_year")));
        result.setAccountPeriod(rule.getPeriod());
        result.setResponsibleId(rule.getResponsibleId());
        result.setSyncTime(new Date());

        // 设置时间范围
        setResultDates(result, row, query);

        // 保存或更新
        handleResultPersist(result);
    }
    /**
     * 设置时间范围字段
     * 如果没有提供特定时间范围，则使用指标数据的年月
     */
    private void setResultDates(BizIndicatorResult result, JSONObject row, SyncQuery query) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        if (query.getStartTime() == null) {
            String year = row.getString("dim_year");
            String month = row.getString("dim_month");
            result.setStartTime(sdf.parse(year + "-" + month + "-01"));
            result.setEndTime(sdf.parse(year + "-" + month + "-" + getLastDayOfMonth(year, month)));
        } else {
            result.setStartTime(sdf.parse(query.getStartTime()));
            result.setEndTime(sdf.parse(query.getEndTime()));
        }
    }
    /**
     * 计算指定年月的最后一天
     */
    private int getLastDayOfMonth(String year, String month) {
        int yearInt = Integer.parseInt(year);
        int monthInt = Integer.parseInt(month);

        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, yearInt);
        calendar.set(Calendar.MONTH, monthInt - 1); // 月份从0开始
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
    /**
     * 持久化结果到数据库
     * 检查是否已存在相同记录，存在则更新，不存在则新建
     */
    private void handleResultPersist(BizIndicatorResult result) {
        QueryWrapper<BizIndicatorResult> wrapper = new QueryWrapper<>();
        wrapper.eq("dept_id", result.getDeptId())
                .eq("account_year", result.getAccountYear())
                .eq("account_period", result.getAccountPeriod())
                .eq("indicator_code", result.getIndicatorCode());
        // 检查记录是否存在
        Optional<BizIndicatorResult> existing = Optional.ofNullable(indicatorResultDao.getOne(wrapper));
        if (existing.isPresent()) {
            result.setId(existing.get().getId());
            indicatorResultDao.updateById(result);
        } else {
            indicatorResultDao.save(result);
        }
    }
}

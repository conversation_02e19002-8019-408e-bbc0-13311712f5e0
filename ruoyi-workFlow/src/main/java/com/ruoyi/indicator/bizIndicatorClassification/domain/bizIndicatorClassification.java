package com.ruoyi.indicator.bizIndicatorClassification.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.yulichang.annotation.EntityMapping;
import com.alibaba.excel.annotation.*;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.List;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class bizIndicatorClassification extends BaseEntity<bizIndicatorClassification> {
    /**
     * 父分类
     */
    @Excel(name = "父分类")
    @ExcelProperty("父分类")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 祖级列表
     */
    @Excel(name = "祖级列表")
    @ExcelProperty("祖级列表")
    private String ancestors;

    /**
     * 类别名称
     */
    @Excel(name = "类别名称")
    @ExcelProperty("类别名称")
    private String classificationName;

    /**
     * 显示顺序
     */
    @Excel(name = "显示顺序")
    @ExcelProperty("显示顺序")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long orderNum;

    /**
     * 状态
     */
    @Excel(name = "状态")
    @ExcelProperty("状态")
    private String status;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    @TableField(exist = false)
    private List<bizIndicatorClassification> children;

}

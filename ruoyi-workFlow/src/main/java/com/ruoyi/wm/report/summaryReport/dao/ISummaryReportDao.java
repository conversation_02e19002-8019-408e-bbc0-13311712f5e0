package com.ruoyi.wm.report.summaryReport.dao;

import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.wm.report.summaryReport.domain.SummaryReport;

import java.util.Date;
import java.util.List;

public interface ISummaryReportDao extends IBaseDao<SummaryReport> {

    /**
     * 查询的开始会计期间已结账 & 结束会计期间已结账
     *
     * @return
     */
    List<SummaryReport> listSummaryReportForAllPeriodCheckedOut(SummaryReport summaryReport);

    /**
     * 查询的开始会计期间已结账 & 结束会计期间未结账
     *
     * @return
     */
    List<SummaryReport> listSummaryReportForEndPeriodNotCheckedOut(SummaryReport summaryReport);

    /**
     * 查询的开始会计期间未结账 & 结束会计期间未结账
     *
     * @return
     */
    List<SummaryReport> listSummaryReportForAllPeriodNotCheckedOut(SummaryReport summaryReport);


}

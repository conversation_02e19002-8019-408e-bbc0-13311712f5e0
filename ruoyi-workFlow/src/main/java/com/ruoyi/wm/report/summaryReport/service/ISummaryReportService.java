package com.ruoyi.wm.report.summaryReport.service;

import com.github.pagehelper.PageInfo;
import com.ruoyi.wm.flowChart.domain.FlowChart;
import com.ruoyi.wm.report.summaryReport.domain.SummaryReport;

import java.util.List;

public interface ISummaryReportService {


    /**
     * 收发存汇总表
     *
     * @param summaryReport 查询参数
     */
    PageInfo<SummaryReport> listSummaryReport(SummaryReport summaryReport);

    /**
     * 收发存汇总表导出
     *
     * @param summaryReport 查询参数
     */
    List<SummaryReport> summaryReportExport(SummaryReport summaryReport);

}

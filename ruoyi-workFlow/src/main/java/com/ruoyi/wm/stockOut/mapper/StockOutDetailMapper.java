package com.ruoyi.wm.stockOut.mapper;

import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.wm.stockOut.domain.StockOutDetail;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

public interface StockOutDetailMapper extends IBaseMapper<StockOutDetail> {

    StockOutDetail getAllStockOutDetailQty(StockOutDetail stockOutDetail);

    Long getLastStockOutDetailId(
            @Param("accountPeriodId") Long accountPeriodId,
            @Param("warehouseId") Long warehouseId,
            @Param("materialId") Long materialId
    );

    /**
     * 结账时，调整 仓库、物料最后一条的核算金额
     *
     * @param lastStockOutDetailId 明细 id
     * @param adjustAmount 调整金额
     * @return 更新行数
     */
    int adjustAmountOfLastStockOutDetailByAcctPeriodIdWhIdMatId(
            @Param("lastStockOutDetailId") Long lastStockOutDetailId,
            @Param("adjustAmount") BigDecimal adjustAmount
    );

}

package com.ruoyi.wm.stockOut.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.math.BigDecimal;
import java.util.Date;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MaterialOutForExport {

    @Excel(name = "单据号", width = 24)
    private String billNo;

    @Excel(name = "出库时间", width = 24, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date date;

    @Excel(name = "经办人")
    private String refUseName;

    @Excel(name = "库管")
    private String managerName;

    @Excel(name = "申请部门")
    private String requestDeptName;

    @Excel(name = "仓库编码")
    private String warehouseCode;

    @Excel(name = "仓库名称", width = 24)
    private String warehouseName;

    @Excel(name = "班组", align = HorizontalAlignment.LEFT, width = 32)
    private String workTeamName;

    @Excel(name = "机台", align = HorizontalAlignment.LEFT, width = 32)
    private String vehicleName;

    @Excel(name = "成本分类", dictType = "cost_type")
    private String costType;

    @Excel(name = "在建工程")
    private String kingdeeProjectName;

    @Excel(name = "作业项目")
    private String financeItemName;

    @Excel(name = "制造费用")
    private String manufacturingCostName;

    @Excel(name = "工程作业")
    private String projectItemName;

    @Excel(name = "单据备注", align = HorizontalAlignment.LEFT, width = 150)
    private String billRemark;

    @Excel(name = "物料编码", align = HorizontalAlignment.LEFT, width = 28)
    private String materialCode;

    @Excel(name = "物料名称", align = HorizontalAlignment.LEFT, width = 90)
    private String materialName;

//    @Excel(name = "规格")
//    private String model;

    @Excel(name = "单位", width = 8)
    private String unit;

    @Excel(name = "应出数量", align = HorizontalAlignment.RIGHT)
    private BigDecimal expectedQty;

    @Excel(name = "出库数量", align = HorizontalAlignment.RIGHT)
    private BigDecimal qty;

    @Excel(name = "核算单价", align = HorizontalAlignment.RIGHT)
    private BigDecimal calculationPrice;

    @Excel(name = "核算金额", align = HorizontalAlignment.RIGHT)
    private BigDecimal calculationAmount;

    @Excel(name = "明细备注", align = HorizontalAlignment.LEFT, width = 30)
    private String remark;

}

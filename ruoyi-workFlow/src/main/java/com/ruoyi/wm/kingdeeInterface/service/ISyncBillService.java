package com.ruoyi.wm.kingdeeInterface.service;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;

import com.ruoyi.archives.accountPeriod.dao.AccountPeriodDao;
import com.ruoyi.archives.accountPeriod.domain.AccountPeriod;
import com.ruoyi.archives.kingdeeProject.dao.IKingdeeProjectDao;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import com.ruoyi.wm.kingdeeInterface.converter.MaterialRequest2CurrentSyncBillConverter;
import com.ruoyi.wm.kingdeeInterface.dao.ICurrentSyncBillDao;
import com.ruoyi.wm.kingdeeInterface.domain.KingdeeRequest.KingdeeRequestEntry;
import com.ruoyi.wm.kingdeeInterface.domain.currentSyncBill.CurrentSyncBill;
import com.ruoyi.wm.kingdeeInterface.domain.KingdeeRequest.KingdeeRequest;
import com.ruoyi.wm.kingdeeInterface.enums.OperatorResult;
import com.ruoyi.wm.kingdeeInterface.mapper.CurrentSyncBillMapper;
import com.ruoyi.wm.stockIn.domain.StockIn;
import com.ruoyi.wm.strategy.factory.SyncStrategyFactory;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ObjectUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface ISyncBillService {

    IKingdeeApiService kingdeeApiService();

    SyncStrategyFactory syncStrategyFactory();

    MaterialRequest2CurrentSyncBillConverter materialRequest2CurrentSyncBillConverter();

    ICurrentSyncBillDao  currentSyncBillDao();
    CurrentSyncBillMapper currentSyncBillMapper();

    AccountPeriodDao periodDao();

    IKingdeeProjectDao  kingdeeProjectDao();


    @SneakyThrows
    @DSTransactional(rollbackFor = Exception.class)
    default String sync() {
        String token = kingdeeApiService().getToken();
        List<KingdeeRequest> materialRequestList = kingdeeApiService().getKingdeeRequestList(token);
        if (ObjectUtils.isEmpty(materialRequestList)){
            return "没有需要同步的数据";
        }
        kingdeeProjectDao().projectSync(materialRequestList);
        // 根据转换类 将 api请求结果转换为 即时表数据
        List<CurrentSyncBill> currentSyncBillList = materialRequest2CurrentSyncBillConverter().convert(materialRequestList);

        int batchSize = 200;

        AccountPeriod tPeriod = periodDao().getCurrentAccountPeriod();

        CurrentSyncBill deleteCurrentSyncBillParam =
                CurrentSyncBill.builder()
                        .startTime(tPeriod.getStartDate())
                        .endTime(tPeriod.getEndDate())
                        .build();
        currentSyncBillDao().deleteBill(deleteCurrentSyncBillParam);
        currentSyncBillList = currentSyncBillList.stream()
                .map(currentSyncBill -> currentSyncBill.setId(null))
                .collect(Collectors.toList());
        currentSyncBillMapper().insert(currentSyncBillList, batchSize);
        int size = currentSyncBillList.size();
        for (CurrentSyncBill currentSyncBill : currentSyncBillList) {
            try {
                if (OperatorResult.PROCESS_SUCCESS.getCode().equals(currentSyncBill.getOperatorResult())) {
                    continue;
                }
                syncStrategyFactory().getStrategy(currentSyncBill.getOperatorType()).sync(currentSyncBill);
            } catch (Exception e) {
                size--;
            }
        }
        return "同步完成，成功：" + size + "条，失败：" + (currentSyncBillList.size() - size) + "条，如有失败请到同步中心查看失败原因";
    }

    /**
     * 定时同步 + 调用存储过程
     */
    void dataScheduling();


    void recordFailReason(CurrentSyncBill bill, Exception e);

    void updateSyncBillStatus(CurrentSyncBill currentSyncBill);


    //更改即时库存
    void updateCurrentStock(List<StockIn> bills);

    //反审核
    void unCheck(List<StockIn> bills);



    /***
     * <p>
     * 效验是否当前会计期间已经结账关账，以及效验负库存
     * <p>
     * **/
    void beforeConfirm(List<StockIn> bills);
    /***
     * <p>
     * 确认逻辑
     * <p>
     * **/
    Map<CurrentStock, Boolean> confirmLogic(List<StockIn> bills);

    /***
     * <p>
     * 确认后操作
     * <p>
     * **/
    void afterConfirm(List<StockIn> bills);
    /***
     * <p>
     *  将入库单的状态改为入库中，以及更改入库单明细的正负
     * <p>
     * **/
    List<StockIn> reverseConfirmWithQtyAdjustment(List<StockIn> bills);

    /**
     * 反审核删除
     * **/
    void deleteAfterUnCheck(List<StockIn> bills);
    /**
     *
     * 失败重试
     * */
    String retry(Long id) throws Exception;

}

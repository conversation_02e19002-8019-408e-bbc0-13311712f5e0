package com.ruoyi.wm.strategy.iPushBillStrategy.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.enums.WMBillStatus;
import com.ruoyi.common.enums.WMBillType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.dao.ISysConfigDao;
import com.ruoyi.system.strategy.AutoCodeUtil;
import com.ruoyi.wm.otherOutStockRequest.dao.IOtherOutStockRequestDao;
import com.ruoyi.wm.otherOutStockRequest.dao.IOtherOutStockRequestDetailDao;
import com.ruoyi.wm.otherOutStockRequest.domain.OtherOutStockRequest;
import com.ruoyi.wm.otherOutStockRequest.domain.OtherOutStockRequestDetail;
import com.ruoyi.wm.stockOut.dao.IStockOutDao;
import com.ruoyi.wm.stockOut.domain.StockOut;
import com.ruoyi.wm.stockOut.domain.StockOutDetail;
import com.ruoyi.wm.stockOut.service.StockOutService;
import com.ruoyi.wm.strategy.iPushBillStrategy.IPushBillStrategy;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component("otherOutStockRequestPushBillStrategy")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class OtherOutStockRequestPushBillStrategy implements IPushBillStrategy<OtherOutStockRequest> {
    private final IStockOutDao iStockOutDao;
    private final IOtherOutStockRequestDao iOtherOutStockRequestDao;
    private final IOtherOutStockRequestDetailDao iOtherOutStockRequestDetailDao;

    private final StockOutService stockOutService;

    private final AutoCodeUtil autoCodeUtil;
    private final ISysConfigDao iSysConfigDao;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public void pushDown(OtherOutStockRequest bill) {
        createStockOut(bill);
    }

    /**
     * 生成出库单
     *
     * @param bill
     */
    private void createStockOut(OtherOutStockRequest bill) {
        if (iSysConfigDao.selectConfigByKey("WM.isItAutomaticallyPushedDown").equals("true")) {

            checkRepeatPushDown(bill);

            checkBillStatus(bill);


            // 查询其他出库申请单明细
            List<OtherOutStockRequestDetail> otherOutStockRequestDetailList =
                    iOtherOutStockRequestDetailDao.lambdaQuery()
                                                  .eq(OtherOutStockRequestDetail::getParentId, bill.getId())
                                                  .list();
            // 创建出库单明细列表
            List<StockOutDetail> stockOutDetailList = new ArrayList<>();

            // 生成出库单
            StockOut.StockOutBuilder stockOutBuilder =
                    StockOut.builder()
                            .billNo(autoCodeUtil.genSerialCode("STOCK_OUT_CODE", null))
                            .requestDeptId(bill.getRequestDeptId()) // 申请部门
                            .warehouseId(bill.getWarehouseId()) // 仓库ID
                            .warehouseCode(bill.getWarehouseCode()) // 仓库编码
                            .refUserId(bill.getRefUserId()) // 业务员Id
                            .refUserCode(bill.getRefUserCode()) // 业务员编码
                            .billStatus(WMBillStatus.STOCKING_OUT.getCode()) // 单据状态
                            .sourceBillType(WMBillType.OTHER_OUT_STOCK_REQUEST.getCode()) // 来源单据类型
                            .sourceBillId(bill.getId()) // 来源单据ID
                            .originBillType(WMBillType.OTHER_OUT_STOCK_REQUEST.getCode()) // 原单类型
                            .originBillId(bill.getId()); // 原单ID

            // 添加出库单明细
            otherOutStockRequestDetailList.forEach(detail -> {
                StockOutDetail.StockOutDetailBuilder stockOutDetailBuilder =
                        StockOutDetail.builder()
                                      .materialId(detail.getMaterialId()) // 物料编码
                                      .materialCode(detail.getMaterialCode()) // 物料编码
                                      .expectedQty(detail.getExpectedQty()) // 应出数量
                                      .qty(detail.getExpectedQty()) // 实际出数量
                                      .price(detail.getPrice()) // 单价
                                      .amount(detail.getAmount()) // 金额
                                      .calculationPrice(BigDecimal.valueOf(0))
                                      .calculationAmount(BigDecimal.valueOf(0))
                                      .sourceBillType(WMBillType.OTHER_OUT_STOCK_REQUEST_DETAIL.getCode()) // 来源单据类型
                                      .sourceBillId(detail.getId()) // 来源单据ID
                                      .originBillType(WMBillType.OTHER_OUT_STOCK_REQUEST_DETAIL.getCode()) // 原单类型
                                      .originBillId(detail.getId()); // 原单ID
                stockOutDetailList.add(stockOutDetailBuilder.build());
            });

            stockOutBuilder.stockOutDetailList(stockOutDetailList);
            stockOutService.createBlueBill(stockOutBuilder.build());

            bill.setBillStatus(WMBillStatus.STOCKING_OUT.getCode());
            iOtherOutStockRequestDao.saveOrUpdate(bill);
        }
    }

    private void checkRepeatPushDown(OtherOutStockRequest bill) {
        boolean isExist = iStockOutDao.lambdaQuery()
                                      .eq(StockOut::getSourceBillId, bill.getId())
                                      .eq(StockOut::getSourceBillType, WMBillType.OTHER_OUT_STOCK_REQUEST.getCode())
                                      .exists();
        if (isExist) {
            throw new ServiceException("其他出库申请单已下推，无法重复进行下推操作");
        }
    }

    private void checkBillStatus(OtherOutStockRequest bill) {
        if (!BizStatus.DONE.getCode().equals(bill.getStatus())) {
            throw new ServiceException("单据未审批通过，无法进行下推操作");
        }
        if (!WMBillStatus.STOCKING_OUT.getCode().equals(bill.getBillStatus())) {
            throw new ServiceException("单据状态不是出库中，无法进行下推操作");
        }
    }

}

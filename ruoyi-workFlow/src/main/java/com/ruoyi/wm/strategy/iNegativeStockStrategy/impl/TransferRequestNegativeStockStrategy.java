package com.ruoyi.wm.strategy.iNegativeStockStrategy.impl;

import com.ruoyi.archives.material.dao.IMaterialDao;
import com.ruoyi.archives.material.domain.Material;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.dao.ISysConfigDao;
import com.ruoyi.wm.currentStock.dao.ICurrentStockDao;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import com.ruoyi.wm.strategy.iNegativeStockStrategy.INegativeStockStrategy;
import com.ruoyi.wm.transferRequest.domain.TransferRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Component("transferRequestNegativeStockStrategy")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TransferRequestNegativeStockStrategy implements INegativeStockStrategy<TransferRequest> {
    private final ISysConfigDao iSysConfigDao;

    private final ICurrentStockDao iCurrentStockDao;

    private final IMaterialDao materialDao;

    /**
     * 负库存管理
     *
     * @param bill 对应需要进行负库存管理的单据
     */
    @Override
    public void checkNegativeStock(TransferRequest bill) {
        if (iSysConfigDao.selectConfigByKey("WM.negativeStockControl").equals("true")) {
            List<String> errorInfoList = new ArrayList<>();
            bill.getTransferRequestDetailList().forEach(y -> {
                CurrentStock currentStock = iCurrentStockDao.lambdaQuery()
                                                            .eq(CurrentStock::getMaterialId, y.getMaterialId())
                                                            .eq(CurrentStock::getWarehouseId, bill.getOutWarehouseId())
                                                            .one();
                Material material = materialDao.getById(y.getMaterialId());

                if (currentStock != null) {
                    if (currentStock.getQty().compareTo(y.getExpectedQty().abs()) < 0) {
                        errorInfoList.add(String.format("%s-%s的库存数量为 %s, 出库数量为 %s",
                                                        material.getCode(),
                                                        material.getName(),
                                                        currentStock.getQty(),
                                                        y.getExpectedQty().abs()));
                    }
                } else {
                    if (BigDecimal.ZERO.compareTo(y.getExpectedQty().abs()) < 0) {
                        errorInfoList.add(String.format("%s-%s的库存数量为 %s, 出库数量为 %s",
                                                        material.getCode(),
                                                        material.getName(),
                                                        0,
                                                        y.getExpectedQty().abs()));
                    }
                }
                if (!errorInfoList.isEmpty()) {
                    String errorInfo = String.join("<br/>", errorInfoList);
                    throw new ServiceException("负库存控制，当前库存不足，请先调整出库数量!<br/>" + errorInfo);
                }
            });
        }
    }
}

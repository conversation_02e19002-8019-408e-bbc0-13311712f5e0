package com.ruoyi.wm.strategy.iAfterCalculationStrategy;

import com.ruoyi.wm.checkoutHistoryRecord.domain.CalculationPriceResult;

import java.util.Map;

public interface IAfterCalculationStrategy {

    /**
     * 更新单据核算单价、核算金额
     *
     * @param accountPeriodId   当前会计期间
     * @param materialIdCalculationPriceResultMap 当前会计期间核算单价结果
     */
    void updateBillAfterCalculation(Long accountPeriodId,
                                    Map<Long, CalculationPriceResult> materialIdCalculationPriceResultMap);

}

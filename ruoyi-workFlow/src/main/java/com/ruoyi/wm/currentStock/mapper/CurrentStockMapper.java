package com.ruoyi.wm.currentStock.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CurrentStockMapper extends IBaseMapper<CurrentStock> {

    // 清空表的数据
    boolean truncateTable();

    // 批次 原子增量更新 核算单价覆盖更新、核算金额增量更新、库存数量增量更新（确认出/入库）
    int batchUpsertIncrementallyAtomically(@Param("list") List<CurrentStock> stockList);

    // 批次 原子全量更新 核算单价覆盖更新、核算金额覆盖更新、库存数量覆盖更新（核算）
    int batchUpsertAtomically(@Param("list") List<CurrentStock> stockList);

    List<CurrentStock> listMaterialRefsFromCurrentStock(CurrentStock currentStock);

    default int selectVersion(CurrentStock stock) {

        if (ObjectUtils.anyNull(
                stock.getMaterialId(),
                stock.getWarehouseId()
        )) {
            throw new ServiceException("selectVersion方法传入materialId、warehouseId的参数不允许为空");
        }

        return selectOne(
                new LambdaQueryWrapper<CurrentStock>()
                        .eq(CurrentStock::getMaterialId, stock.getMaterialId())
                        .eq(CurrentStock::getWarehouseId, stock.getWarehouseId())
                        .select(CurrentStock::getVersion)
        ).getVersion();
    }

}

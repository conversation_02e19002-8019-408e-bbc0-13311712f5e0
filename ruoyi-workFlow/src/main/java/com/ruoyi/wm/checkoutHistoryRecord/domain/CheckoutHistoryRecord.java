package com.ruoyi.wm.checkoutHistoryRecord.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.ruoyi.archives.accountPeriod.domain.AccountPeriod;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("wm_checkout_history_record")
public class CheckoutHistoryRecord extends BaseEntity<CheckoutHistoryRecord> {
    /** 结账日期 */
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "结账日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("结账日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date date;

    /** 账期ID */
    @Excel(name = "账期ID")
    @ExcelProperty("账期ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountPeriodId;

    @TableField(exist = false)
    @EntityMapping(thisField = Fields.accountPeriodId, joinField = AccountPeriod.Fields.id)
    private AccountPeriod accountPeriod;

    /** 操作类型 0：关账，1：反关账，2：核算，3：结账，4：反结账 */
    @Excel(name = "操作类型")
    @ExcelProperty("操作类型")
    private String operatorType;

    /** 搜索用 开始时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginDate;

    /**搜索用  结束时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endDate;

    //会计日期 开始时间 显示用 */
   /* @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @EntityMapping(thisField = Fields.accountPeriodId, joinField = AccountPeriod.Fields.startDate)
    private Date accountPeriodStartDate;*/

    //会计日期 结束时间 显示用*/
    /*@TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @EntityMapping(thisField = Fields.accountPeriodId, joinField = AccountPeriod.Fields.endDate)
    private Date accountPeriodEndDate;*/
}

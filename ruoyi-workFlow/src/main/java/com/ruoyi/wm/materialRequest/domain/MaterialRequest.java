package com.ruoyi.wm.materialRequest.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.archives.BizVehicle.domain.BizVehicle;
import com.ruoyi.archives.financeItem.domain.FinanceItem;
import com.ruoyi.archives.kingdeeProject.domain.KingdeeProject;
import com.ruoyi.archives.manufacturingCost.domain.ManufacturingCost;
import com.ruoyi.archives.material.domain.Material;
import com.ruoyi.archives.place.domain.Place;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.archives.warehouse.domain.Warehouse;
import com.ruoyi.archives.workTeam.domain.WorkTeam;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("wm_material_request")
public class MaterialRequest extends ProcessEntity<MaterialRequest> {
    /**
     * 单据号
     */
    @Excel(name = "单据号")
    private String billNo;

    @Excel(name = "领料单位")
    private Long requestDeptId;


    @FieldMapping(tag = SysDept.class, thisField = Fields.requestDeptId, joinField = SysDept.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String requestDeptName;

    @Excel(name = "井口")
    private Long wellheadId;


    @Excel(name = "机台")
    private Long vehicle;

    @Excel(name = "成本分类")
    private String costTypeValue;
    /**
     * 申请日期时间
     */
    @Excel(name = "申请日期时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date date;

    /**
     * 申请仓库ID
     */
    @Excel(name = "申请仓库ID")
    private Long warehouseId;

    /**
     * 申请仓库编码
     */
    @Excel(name = "申请仓库编码")
    private String warehouseCode;

    /**
     * 业务员ID
     */
    @Excel(name = "业务员ID")
    private Long refUserId;

    /**
     * 业务员编码
     */
    @Excel(name = "业务员编码")
    private String refUserCode;

    /**
     * 班组ID
     */
    @Excel(name = "班组ID")
    private Long workTeamId;

    /**
     * 班组编码
     */
    @Excel(name = "班组编码")
    private String workTeamCode;

    /**
     * 作业地点ID
     */
    @Excel(name = "作业地点ID")
    private Long workPlaceId;

    /**
     * 作业地点编码
     */
    @Excel(name = "作业地点编码")
    private String workPlaceCode;

    /**
     * 作业项目ID
     */
    @Excel(name = "作业项目ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long financeItemId;

    /**
     * 作业项目编码
     */
    @Excel(name = "作业项目编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String financeItemCode;

    /**
     * 工程项目ID
     */
    @Excel(name = "工程作业ID")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long projectItemId;

    /**
     * 工程作业编码
     */
    @Excel(name = "工程作业编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String projectItemCode;

    /**
     * 红蓝单 0：红单，1：蓝单
     */
    @Excel(name = "红蓝单 0：红单，1：蓝单")
    private String blueRedFlag;

    /**
     * 在建工程ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long kingdeeProjectId;
    /**
     * 制造费用ID
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long manufacturingCostId;
    /**
     * 单据状态 31：未领料，34：领料中，37：已领料，41： 未退料，44：退料中，47：已退料
     */
    @Excel(name = "单据状态 31：未领料，34：领料中，37：已领料，41： 未退料，44：退料中，47：已退料")
    private String billStatus;

    /**
     * 领料申请明细信息 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = MaterialRequestDetail.Fields.parentId)
    private List<MaterialRequestDetail> materialRequestDetailList = new ArrayList<>();

    /**
     * 仓库全称
     */

    @FieldMapping(tag = Warehouse.class, thisField = Fields.warehouseId, joinField = Warehouse.Fields.id, select = Warehouse.Fields.name)
    @TableField(exist = false)
    private String warehouseName;

    /**
     * 业务员
     */
    @FieldMapping(tag = SysUser.class, thisField = Fields.refUserId, joinField = SysUser.Fields.userId, select = SysUser.Fields.nickName)
    @TableField(exist = false)
    private String userName;

    /**
     * 班组
     */
    @FieldMapping(tag = WorkTeam.class, thisField = Fields.workTeamId, joinField = WorkTeam.Fields.id, select = WorkTeam.Fields.name)
    @TableField(exist = false)
    private String workTeamName;


    /**
     * 作业地点
     */
    @FieldMapping(tag = Place.class, thisField = Fields.workPlaceId, joinField = Place.Fields.id, select = Place.Fields.name)
    @TableField(exist = false)
    private String placeName;

    /**
     * 作业项目
     */
    @FieldMapping(tag = FinanceItem.class, thisField = Fields.financeItemId, joinField = FinanceItem.Fields.id, select = FinanceItem.Fields.name)
    @TableField(exist = false)
    private String financeName;

    /**
     * 机台名称
     */
    @FieldMapping(tag = BizVehicle.class, thisField = Fields.vehicle, joinField = BaseEntity.Fields.id, select = BizVehicle.Fields.name)
    @TableField(exist = false)
    private String vehicleName;

    /**
     * 工程项目
     */
    @FieldMapping(tag = ProjectItem.class, thisField = Fields.projectItemId, joinField = ProjectItem.Fields.id, select = ProjectItem.Fields.name)
    @TableField(exist = false)
    private String projectName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;


    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @TableField(exist = false)
    private Long materialId;

    @TableField(exist = false)
    private Long materialName;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @TableField(exist = false)
    private Long qty;

    @TableField(exist = false)
    private Long expectedQty;

    /** 制造费用名称*/
    @TableField(exist = false)
    @FieldMapping(tag = ManufacturingCost.class, thisField = Fields.manufacturingCostId, joinField = BaseEntity.Fields.id, select = ManufacturingCost.Fields.manufacturingCostName)
    private String manufacturingCostName;
    /** 在建工程名称*/
    @TableField(exist = false)
    @FieldMapping(tag = KingdeeProject.class, thisField = Fields.kingdeeProjectId, joinField = BaseEntity.Fields.id, select = KingdeeProject.Fields.projectName)
    private String kingdeeProjectName;



}

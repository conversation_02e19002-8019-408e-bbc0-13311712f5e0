package com.ruoyi.wm.materialRequest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import com.ruoyi.wm.materialRequest.dao.IMaterialRequestDao;
import com.ruoyi.wm.materialRequest.dao.IMaterialRequestDetailDao;
import com.ruoyi.wm.materialRequest.domain.MaterialRequest;
import com.ruoyi.wm.materialRequest.domain.MaterialRequestDetail;
import com.ruoyi.wm.materialRequest.service.MaterialRequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@RestController
@RequestMapping("/workflow/materialrequest")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MaterialRequestController extends BaseController implements IBaseController {

    private final IMaterialRequestDao materialRequestDao;
    private final IMaterialRequestDetailDao materialRequestDetailDao;

    private final MaterialRequestService materialRequestService;

    @GetMapping
//    @PreAuthorize("@ss.hasPermi('workflow:materialRequest:list')")
    public AjaxResult list(MaterialRequest materialRequest) {
        return success(getPageInfo(() -> materialRequestService.listWithPermi(materialRequest)));
    }
    @GetMapping("listWithPermi")
    public AjaxResult listWithPermi(MaterialRequest materialRequest) {
        return success(getPageInfo(() -> materialRequestService.listWithPermi(materialRequest)));
    }
    @Log(title = "领料申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MaterialRequest materialRequest) {
        new ExcelUtil<>(MaterialRequest.class).exportExcel(response,
                                                           getPageInfo(() -> materialRequestDao.list(materialRequest)).getList(),
                                                           "领料申请数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(materialRequestDao.getByIdDeep(Long.valueOf(id), conf -> conf.deep(3).loop(true)));
    }

    @Log(title = "领料申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MaterialRequest materialRequest) {
        return materialRequestService.insert(materialRequest) ? toAjax(materialRequest.getId()) : toAjax(false);
    }

    @Log(title = "领料申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MaterialRequest materialRequest) {
        return materialRequestService.update(materialRequest) ? toAjax(materialRequest.getId()) : toAjax(false);
    }

    @Log(title = "领料申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(materialRequestService.delete(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交领料申请", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
//        return toAjax(materialRequestDao.submitApply(Long.valueOf(id), materialRequestService.getMaterialRequest(id)));
        return toAjax(materialRequestService.submitApply(Long.valueOf(id)));
    }


    @GetMapping("listDetail")
    public AjaxResult listDetail(MaterialRequestDetail materialRequest) {
        return success(materialRequestDetailDao.list(materialRequest));
    }


    @PostMapping("pushDown/{ids}")
    public AjaxResult pushDown(@PathVariable String[] ids) {
        return toAjax(materialRequestService.pushDown(ids));
    }

    @PostMapping("/obsolete/{id}")
    public AjaxResult obsolete(@PathVariable("id") String id) {
        return toAjax(materialRequestService.obsolete(materialRequestDao.getByIdDeep(Long.valueOf(id))));
    }

    @GetMapping("/materialSummaryTableList")
    public AjaxResult materialSummaryTableList(MaterialRequest materialRequest) {
        return success(getPageInfo(() -> materialRequestDao.materialConsumptionSummaryTableList(materialRequest)));
    }
}

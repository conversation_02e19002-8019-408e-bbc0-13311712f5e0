package com.ruoyi.wm.flowChart.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.warehouse.domain.Warehouse;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.wm.stockIn.domain.StockInDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class FlowChart extends BaseEntity<FlowChart> {

    // 会计期间（查询条件）
    private Long accountPeriodId;

    // 开始时间（查询条件）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    // 结束时间（查询条件）
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    private Long deptId;

    @Excel(name = "部门名称")
    private String deptName;

    private Long warehouseId;

    @Excel(name = "仓库编码", align = HorizontalAlignment.LEFT)
    private String warehouseCode;

    @Excel(name = "仓库名称", align = HorizontalAlignment.LEFT)
    private String warehouseName;

    private Long materialId;

    @Excel(name = "物料编码", width = 28, align = HorizontalAlignment.LEFT)
    private String materialCode;

    @Excel(name= "物料名称", width = 90, align = HorizontalAlignment.LEFT)
    private String materialName;

    private String model;

    @Excel(name = "单位", width = 8)
    private String unit;

    @Excel(name = "日期", width = 24, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date billDate;

    @Excel(name = "单据类型", dictType = "source_bill_type")
    private String billType;

    @Excel(name = "单据号", width = 24)
    private String billNo;

    // 入库数量
    @Excel(name = "入库数量", align = HorizontalAlignment.RIGHT)
    private BigDecimal billInQty;

    // 入库金额
    @Excel(name = "入库金额", align = HorizontalAlignment.RIGHT)
    private BigDecimal billInAmount;

    // 出库数量
    @Excel(name = "出库数量", align = HorizontalAlignment.RIGHT)
    private BigDecimal billOutQty;

    // 出库金额
    @Excel(name = "出库金额", align = HorizontalAlignment.RIGHT)
    private BigDecimal billOutAmount;

    @Excel(name = "期末数量", align = HorizontalAlignment.RIGHT)
    private BigDecimal endQty;

    @Excel(name = "期末金额", align = HorizontalAlignment.RIGHT)
    private BigDecimal endAmount;

}

package com.ruoyi.wm.flowChart.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.wm.flowChart.dao.IFlowChartDao;
import com.ruoyi.wm.flowChart.domain.FlowChart;
import com.ruoyi.wm.flowChart.service.FlowChartService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/workflow/FlowChart")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class FlowChartController extends BaseController implements IBaseController {

    private final FlowChartService flowChartService;

    @GetMapping("/lowingWaterList")
    public AjaxResult lowingWaterList(FlowChart flowChart) {
        return success(flowChartService.getFlowChartPageInfo(flowChart));
    }

    @Log(title = "库存台账", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FlowChart flowChart) {
        new ExcelUtil<>(FlowChart.class).
                exportExcel(response, flowChartService.listFlowChart(flowChart), "库存台账数据");
    }

}

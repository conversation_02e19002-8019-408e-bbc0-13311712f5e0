package com.ruoyi.wm.transferRequest.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.archives.warehouse.domain.Warehouse;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.wm.materialRequest.domain.MaterialRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("wm_transfer_request")
public class TransferRequest extends ProcessEntity<TransferRequest> {
    /**
     * 业务员
     */
    @TableField(exist = false)
    @EntityMapping(thisField = TransferRequest.Fields.refUserId, joinField = SysUser.Fields.userId)
    protected SysUser refUser;

    @Excel(name = "领料单位")
    private Long requestDeptId;
    /**
     * 单据号
     */
    @Excel(name = "单据号")
    private String billNo;
    /**
     * 申请时间
     */
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;
    /**
     * 调出仓库
     */
    @Excel(name = "调出仓库")
    private String outWarehouseCode;
    /**
     * 调出仓库Id
     */
    @Excel(name = "调出仓库Id")
    private Long outWarehouseId;
    /**
     * 调入仓库
     */
    @Excel(name = "调入仓库")
    private String inWarehouseCode;
    /**
     * 调入仓库Id
     */
    @Excel(name = "调入仓库Id")
    private Long inWarehouseId;
    /**
     * 业务员
     */
    @Excel(name = "业务员")
    private String refUserCode;
    /**
     * 业务员
     */
    @Excel(name = "业务员")
    private Long refUserId;
    /**
     * 单据状态
     */
    @Excel(name = "单据状态")
    private String billStatus;
    /**
     * 调拨申请明细信息 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = TransferRequestDetail.Fields.parentId)
    private List<TransferRequestDetail> transferRequestDetailList = new ArrayList<>();
    /**
     * 连表调出仓库
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.outWarehouseId, joinField = BaseEntity.Fields.id)
    private Warehouse outWarehouse;
    /**
     * 连表调入仓库
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.inWarehouseId, joinField = BaseEntity.Fields.id)
    private Warehouse inWarehouse;
    /**
     * 查询时间
     * 0是开始时间 1是结束时间
     */
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<LocalDateTime> daterangeDate;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endDate;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startDate;

    @FieldMapping(tag = SysDept.class, thisField = Fields.requestDeptId, joinField = SysDept.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String requestDeptName;
}

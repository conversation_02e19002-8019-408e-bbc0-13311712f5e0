package com.ruoyi.wm.transferRequest.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.enums.WMBillStatus;
import com.ruoyi.common.enums.WMBillType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.wm.stockOut.dao.IStockOutDao;
import com.ruoyi.wm.stockOut.domain.StockOut;
import com.ruoyi.wm.strategy.factory.NegativeStockStrategyFactory;
import com.ruoyi.wm.strategy.factory.ObsoleteBillStrategyFactory;
import com.ruoyi.wm.strategy.factory.PushBillStrategyFactory;
import com.ruoyi.wm.transferRequest.dao.ITransferRequestDao;
import com.ruoyi.wm.transferRequest.domain.TransferRequest;
import com.ruoyi.wm.transferRequest.mapper.TransferRequestMapper;
import com.ruoyi.wm.transferRequest.service.ITransferRequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TransferRequestServiceImpl extends ServiceImpl<TransferRequestMapper, TransferRequest> implements ITransferRequestService {
    @Lazy
    private final ITransferRequestDao iTransferRequestDao;

    @Lazy
    private final NegativeStockStrategyFactory negativeStockStrategyFactory;
    private final PushBillStrategyFactory pushBillStrategyFactory;
    private final ObsoleteBillStrategyFactory obsoleteBillStrategyFactory;


    /**
     * 新增调拨申请
     *
     * @param transferRequest 调拨申请
     * @return 结果
     */

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean insert(TransferRequest transferRequest) {
        // 新增时添加单据状态默认值
        transferRequest.setStatus(WMBillStatus.NOT_TRANSFER.getCode());

        iTransferRequestDao.validateSaveBillParams(transferRequest);

        //负库存控制
        negativeStockStrategyFactory.getStrategy(WMBillType.TRANSFER_REQUEST.getCode())
                                    .checkNegativeStock(transferRequest);
        return iTransferRequestDao.save(transferRequest);
    }

    /**
     * 修改调拨申请
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean update(TransferRequest transferRequest) {
        checkUpdateOrDeleteOpt(Collections.singletonList(transferRequest));

        iTransferRequestDao.validateSaveBillParams(transferRequest);

        //负库存控制
        negativeStockStrategyFactory.getStrategy(WMBillType.TRANSFER_REQUEST.getCode())
                                    .checkNegativeStock(transferRequest);
        return iTransferRequestDao.updateById(transferRequest);
    }

    /**
     * 批量删除调拨申请
     *
     * @return 结果
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean delete(Serializable[] ids) {
         List<Long> idList = Arrays.stream(ids).map(id -> Long.valueOf(id.toString())).collect(Collectors.toList());
        List<TransferRequest> transferRequestList =
                iTransferRequestDao.lambdaQuery().in(TransferRequest::getId, idList).list();

        checkUpdateOrDeleteOpt(transferRequestList);

        return iTransferRequestDao.removeById(ids);
    }

    /**
     * 下推操作
     */
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean pushDown(String[] id) {
        List<TransferRequest> transferRequestList =
                iTransferRequestDao.lambdaQuery()
                                   .in(TransferRequest::getId, Arrays.asList(id))
                                   .list();
        transferRequestList.forEach(transferRequest -> {
            pushBillStrategyFactory.getStrategy(WMBillType.TRANSFER_REQUEST.getCode())
                                   .pushDown(transferRequest);
        });
        return true;
    }

    /**
     * 作废
     *
     * @param entity
     * @return
     */
    @Override
    public boolean obsolete(TransferRequest entity) {
        obsoleteBillStrategyFactory.getStrategy(WMBillType.TRANSFER_REQUEST.getCode())
                                   .obsoleteProcessBill(entity, entity.getBillStatus(), entity.getId());
        return true;
    }

    @Override
    public List<TransferRequest> listWithPermi(TransferRequest transferRequest) {
          return iTransferRequestDao.listDeepWithPermi(transferRequest);
    }

    void checkUpdateOrDeleteOpt(List<TransferRequest> entryList) {
        List<String> errorList = new ArrayList<>();

        entryList.forEach(entry -> {
            // 审核状态 审核中与已审核的单据 不允许编辑或删除
            String status = entry.getStatus();
            if (BizStatus.DONE.getCode().equals(status) || BizStatus.CHECKING.getCode().equals(status)) {
                errorList.add(String.format("单号为%s的 调拨申请 审批状态为%s，不允许进行此操作！",
                                                entry.getBillNo(),
                                                BizStatus.getInfoByCode(status)));
                return;
            }

            // 单据状态 调拨中与已调拨的单据 不允许编辑或删除
            String billStatus = entry.getBillStatus();
            if (WMBillStatus.TRANSFERRING.getCode().equals(billStatus)
                    || WMBillStatus.TRANSFERRED.getCode().equals(billStatus)) {
                errorList.add(String.format("单号为%s的 调拨申请 单据状态为%s，不允许进行此操作！",
                                                entry.getBillNo(),
                                                BizStatus.getInfoByCode(status)));
            }
        });

        if (ObjectUtil.isNotEmpty(errorList)) {
            throw new ServiceException(StringUtil.join(errorList, System.lineSeparator()));
        }
    }

}

package com.ruoyi.wm.transferRequest.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import com.ruoyi.wm.transferRequest.dao.ITransferRequestDao;
import com.ruoyi.wm.transferRequest.domain.TransferRequest;
import com.ruoyi.wm.transferRequest.service.ITransferRequestService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/workflow/transferRequest")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TransferRequestController extends BaseController implements IBaseController {

    private final ITransferRequestDao transferRequestDao;
    private final ITransferRequestService iTransferRequestService;

    @GetMapping
//    @PreAuthorize("@ss.hasPermi('transferrequest:transferrequest:list')")
    public AjaxResult list(TransferRequest transferRequest) {
        return success(getPageInfo(() -> iTransferRequestService.listWithPermi(transferRequest)));
    }
    @GetMapping("listWithPermi")
    public AjaxResult listWithPermi(TransferRequest transferRequest) {
        return success(getPageInfo(() -> iTransferRequestService.listWithPermi(transferRequest)));
    }
    @Log(title = "调拨申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TransferRequest transferRequest) {
        new ExcelUtil<>(TransferRequest.class).
                exportExcel(response, getPageInfo(() -> transferRequestDao.list(transferRequest)).getList(), "调拨申请数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(transferRequestDao.getByIdDeep(Long.valueOf(id), conf -> conf.deep(3).loop(true)));
    }

    @Log(title = "调拨申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TransferRequest transferRequest) {
        return iTransferRequestService.insert(transferRequest) ? toAjax(transferRequest.getId()) : toAjax(false);
    }

    @Log(title = "调拨申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TransferRequest transferRequest) {
        return iTransferRequestService.update(transferRequest) ? toAjax(transferRequest.getId()) :
                toAjax(false);
    }

    @Log(title = "调拨申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(iTransferRequestService.delete(ids));
    }

    @Log(title = "提交调拨申请", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(transferRequestDao.submitApply(Long.valueOf(id)));
    }

    @Log(title = "调拨下推", businessType = BusinessType.DELETE)
    @PostMapping("/transferPush/{ids}")
    public AjaxResult transferPush(@PathVariable String[] ids) {
        return toAjax(iTransferRequestService.pushDown(ids));
    }

    @PostMapping("/obsolete/{id}")
    public AjaxResult obsolete(@PathVariable("id") String id) {
        return toAjax(iTransferRequestService.obsolete(transferRequestDao.getByIdDeep(Long.valueOf(id))));
    }
}

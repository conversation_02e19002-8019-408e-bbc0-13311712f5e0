package com.ruoyi.archives.kingdeeProject.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import com.alibaba.excel.annotation.*;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_kingdee_project")
public class KingdeeProject extends BaseEntity<KingdeeProject> {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.AUTO)
    protected Long id;


    /** 在建工程号.在建工程编码 */
    @Excel(name = "在建工程编码")
    @ExcelProperty("在建工程编码")
    private String projectNumber;

    /** 在建工程名称 */
    @Excel(name = "在建工程名称")
    @ExcelProperty("在建工程名称")
    private String projectName;

    /** 在建工程的部门 */
//    @Excel(name = "在建工程的部门")
//    @ExcelProperty("在建工程的部门")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;
    @Excel(name = "在建工程状态")
    @ExcelProperty("在建工程状态")
    private String projectStatus;

}

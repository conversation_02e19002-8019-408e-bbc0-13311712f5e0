package com.ruoyi.archives.kingdeeProject.dao;


import com.ruoyi.archives.kingdeeProject.domain.KingdeeProject;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.wm.kingdeeInterface.domain.KingdeeRequest.KingdeeRequest;

import java.util.List;

public interface IKingdeeProjectDao extends IBaseDao<KingdeeProject> {
    //金蝶同步

    boolean projectSync(List<KingdeeRequest> kingdeeRequests);

    @Override
    boolean save(KingdeeProject entity);
    @Override
    boolean updateById(KingdeeProject entity);

    boolean isProjectCodeUnique(KingdeeProject entity);
}

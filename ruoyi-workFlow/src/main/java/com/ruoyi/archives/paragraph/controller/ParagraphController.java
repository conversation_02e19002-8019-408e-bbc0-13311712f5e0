package com.ruoyi.archives.paragraph.controller;

import com.ruoyi.archives.paragraph.dao.IParagraphDao;
import com.ruoyi.archives.paragraph.domain.Paragraph;
import com.ruoyi.archives.paragraph.service.IBizParagraphService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.page.TableDataInfo;
import com.ruoyi.common.util.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/BizParagraph/bizParagraph")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ParagraphController extends BaseController implements IBaseController {

    private final IParagraphDao bizParagraphDao;

    private final IBizParagraphService IBizParagraphService;

    @GetMapping
    public AjaxResult list(Paragraph paragraph) {
        return success(getPageInfo(() -> bizParagraphDao.list(paragraph)));
    }

    // 查询中段所有信息
    @GetMapping("/paragraphList")
    public AjaxResult paragraphList(Paragraph paragraph) {
        return success(bizParagraphDao.list(paragraph));
    }

    @Log(title = "中段", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Paragraph paragraph) {
        new ExcelUtil<>(Paragraph.class).
                exportExcel(response, getPageInfo(() -> bizParagraphDao.list(paragraph)).getList(), "中段数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizParagraphDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "中段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Paragraph paragraph) {
        return IBizParagraphService.save(paragraph) ? toAjax(paragraph.getId()) : toAjax(false);
    }

    @Log(title = "中段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Paragraph paragraph) {
        return IBizParagraphService.updateById(paragraph) ? toAjax(paragraph.getId()) :
                toAjax(false);
    }

    @Log(title = "中段", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizParagraphDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    //根据code查询
    @GetMapping("/listByCodes")
    public AjaxResult listByCodes(@RequestParam(value = "codes", required = false) String codes) {
        return success(bizParagraphDao.listByCodes(codes));
    }


    // 根据井口id和部门查询井段
    @GetMapping("/listParagraph")
    public AjaxResult listParagraph(String wellheadId, String deptId) {
        if (wellheadId == null || wellheadId.isEmpty()){
            wellheadId = "0";
        }
        return success(IBizParagraphService.listParagraph(Long.valueOf(deptId), Long.valueOf(wellheadId)));
    }


    @GetMapping("/listParagraphName")
    public AjaxResult listParagraphName(Paragraph paragraph) {
        return success(getPageInfo(() -> IBizParagraphService.listParagraphName(paragraph)));
    }


    @GetMapping("/listWithoutPermi")
    public TableDataInfo<Paragraph> listWithoutPermi(Paragraph paragraph) {
        List<Paragraph> list = bizParagraphDao.list(paragraph);
        return getDataTable(list);
    }
}

package com.ruoyi.archives.paragraph.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@TableName("biz_paragraph")
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class Paragraph extends BaseEntity<Paragraph> {
    /**
     * 部门ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /**
     * 工区ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workAreaId;

    /**
     * 井口ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;

    /**
     * 部门
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.deptId)
    private SysDept dept;

    @TableField(exist = false)
    @Excel(name = " 部门")
    @FieldMapping(tag = SysDept.class, thisField = Paragraph.Fields.deptId, select = SysDept.Fields.deptName)
    private String deptName;

    /**
     * 编码
     */
    @Excel(name = "编码")
    private String code;

    /**
     * 名称
     */
    @Excel(name = "名称")
    private String name;

    /**
     * 部门名称缩写
     */
    @TableField(exist = false)
    @FieldMapping(tag = SysDept.class, thisField = Paragraph.Fields.deptId, select = SysDept.Fields.acronymName)
    private String acronymName;

    @TableField(exist = false)
    @Excel(name = " 井口")
    @FieldMapping(tag = BizWellhead.class, thisField = Paragraph.Fields.wellheadId, select = BizWellhead.Fields.name)
    private String wellheadName;

    @TableField(exist = false)
    @Excel(name = " 工区")
    @FieldMapping(tag = BizWorkArea.class, thisField = Paragraph.Fields.workAreaId, select = BizWorkArea.Fields.name)
    private String workAreaName;

    /**
     * 全称
     */
    @Excel(name = "全称")
    private String fullName;

    @Excel(name = "备注")
    private String remark;

}

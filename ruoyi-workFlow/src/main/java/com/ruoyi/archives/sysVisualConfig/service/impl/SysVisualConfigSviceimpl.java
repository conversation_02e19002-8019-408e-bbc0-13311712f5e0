package com.ruoyi.archives.sysVisualConfig.service.impl;

import com.ruoyi.archives.sysVisualConfig.dao.ISysVisualConfigDao;
import com.ruoyi.archives.sysVisualConfig.domain.SysVisualConfig;
import com.ruoyi.archives.sysVisualConfig.service.SysVisualConfigSvice;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysVisualConfigSviceimpl implements SysVisualConfigSvice {

    private final ISysVisualConfigDao sysVisualConfigDao;
    @Override
    public SysVisualConfig cover(String cover) {
        SysVisualConfig svc = sysVisualConfigDao.lambdaQuery().eq( SysVisualConfig::getCover, cover).one();
        if (svc != null) {
            return svc;
        }
        throw new RuntimeException("未找到该封面");
    }
}

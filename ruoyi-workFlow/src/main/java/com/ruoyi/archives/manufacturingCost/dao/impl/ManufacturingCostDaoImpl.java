package com.ruoyi.archives.manufacturingCost.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.archives.kingdeeProject.domain.KingdeeProject;
import com.ruoyi.archives.manufacturingCost.dao.IManufacturingCostDao;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import com.ruoyi.archives.manufacturingCost.mapper.ManufacturingCostMapper;
import com.ruoyi.archives.manufacturingCost.domain.ManufacturingCost;
import org.springframework.aop.framework.AopContext;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ManufacturingCostDaoImpl extends BaseDaoImpl<ManufacturingCostMapper, ManufacturingCost> implements IManufacturingCostDao {
    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean save(ManufacturingCost entity) {
        ((IManufacturingCostDao) AopContext.currentProxy()).isManufacturingCostCodeUnique(entity);
        return super.save(entity);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean updateById(ManufacturingCost entity) {
        return super.updateById(entity);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean isManufacturingCostCodeUnique(ManufacturingCost entity) {
        List<ManufacturingCost> manufacturingCostList = lambdaQuery().
                eq(ManufacturingCost::getManufacturingCostCode, entity.getManufacturingCostCode()).list();
        if (ObjectUtils.isNotEmpty(manufacturingCostList)){
            throw new ServiceException("制造费用编码不能重复");
        }
        return true;
    }
}

package com.ruoyi.archives.manufacturingCost.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import com.alibaba.excel.annotation.*;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_manufacturing_cost")
public class ManufacturingCost extends BaseEntity<ManufacturingCost> {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.AUTO)
    protected Long id;

    /** 制造费用编码 */
    @Excel(name = "制造费用编码")
    @ExcelProperty("制造费用编码")
    private String manufacturingCostCode;

    /** 制造费用名称 */
    @Excel(name = "制造费用名称")
    @ExcelProperty("制造费用名称")
    private String manufacturingCostName;

}

package com.ruoyi.archives.manufacturingCost.dao;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.archives.kingdeeProject.domain.KingdeeProject;
import com.ruoyi.archives.manufacturingCost.domain.ManufacturingCost;
import com.ruoyi.common.dao.IBaseDao;

public interface IManufacturingCostDao extends IBaseDao<ManufacturingCost> {
    @Override
    boolean save(ManufacturingCost entity);
    @Override
    boolean updateById(ManufacturingCost entity);

    boolean isManufacturingCostCodeUnique(ManufacturingCost entity);
}

package com.ruoyi.archives.projectType.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.alibaba.excel.annotation.*;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_project_type")
public class ProjectType extends BaseEntity<ProjectType> {
                /** 工程性质 */
    @Excel(name = "工程性质")
    @ExcelProperty("工程性质")
    private String projectPropertyValue;

                /** 名称 */
    @Excel(name = "名称")
    @ExcelProperty("名称")
    private String name;

                /** 手动输入 */
    @Excel(name = "手动输入")
    @ExcelProperty("手动输入")
    private String code;

}

package com.ruoyi.archives.workTeam.controller;

import com.ruoyi.archives.workArea.dao.IBizWorkAreaDao;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.archives.workTeam.dao.WorkTeamDao;
import com.ruoyi.archives.workTeam.domain.WorkTeam;
import com.ruoyi.archives.workTeam.service.WorkTeamService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.wm.currentStock.domain.CurrentStock;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workFlow/workTeam")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WorkTeamController extends BaseController implements IBaseController {
    private final IBizWorkAreaDao workAreaDao;
    private final WorkTeamDao workTeamDao;
    private final WorkTeamService workTeamService;

    @GetMapping
    public AjaxResult list(WorkTeam workTeam) {
        return success(getPageInfo(() -> workTeamDao.list(workTeam)));
    }

    @GetMapping("/listWorkTeamWithPermi")
    public AjaxResult listCurrentStockWithPermi(WorkTeam workTeam) {
        return success(getPageInfo(() -> workTeamService.listWorkTeamWithPermi(workTeam)));
    }

    //查询工区工段
    @GetMapping("/workArealist")
    public AjaxResult workArealist(BizWorkArea bizWorkArea) {
        startPage();
        List<BizWorkArea> bizWorkAreaList = workAreaDao.list(bizWorkArea);
        return success(getPageInfo(() -> bizWorkAreaList));
    }

    @Log(title = "班组管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkTeam workTeam) {
        new ExcelUtil<>(WorkTeam.class).
                exportExcel(response, getPageInfo(() -> workTeamDao.list(workTeam)).getList(), "班组管理数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(workTeamDao.getByIdDeep(Long.valueOf(id)));
    }

    //根据id查询名字
    @GetMapping("/listWorkTeamByIds")
    public AjaxResult listByIds(@RequestParam(value = "ids", required = false) String ids) {

        return AjaxResult.success(workAreaDao.getById(ids));
    }

    @Log(title = "班组管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WorkTeam workTeam) {
        return workTeamService.save(workTeam) ? toAjax(workTeam.getId()) : toAjax(false);
    }

    @Log(title = "班组管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WorkTeam workTeam) {
        return workTeamService.updateById(workTeam) ? toAjax(workTeam.getId()) :
                toAjax(false);
    }

    @Log(title = "班组管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(workTeamDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }

    //根据code查询
    @GetMapping("/listByCodes")
    public AjaxResult listByCodes(@RequestParam(value = "codes", required = false) String codes) {
        return success(workTeamDao.listByCodes(codes));
    }


}

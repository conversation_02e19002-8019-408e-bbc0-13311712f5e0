package com.ruoyi.archives.workTeam.dao.impl;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.ruoyi.archives.workTeam.dao.WorkTeamDao;
import com.ruoyi.archives.workTeam.domain.WorkTeam;
import com.ruoyi.archives.workTeam.mapper.WorkTeamMapper;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WorkTeamDaoImpl extends BaseDaoImpl<WorkTeamMapper, WorkTeam> implements WorkTeamDao {
    private final WorkTeamMapper workTeamMapper;

    @Override
    @DataScope(deptAlias = "d")
    public List<WorkTeam> listWorkTeamWithPermi(WorkTeam workTeam) {
        List<WorkTeam> workTeamList = workTeamMapper.list(workTeam);
        Relation.list(workTeamList, 0, DeepConfig.defaultConfig());
        return workTeamList;
    }
}

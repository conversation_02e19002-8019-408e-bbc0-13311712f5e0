package com.ruoyi.archives.workTeam.service.Impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.archives.workArea.dao.IBizWorkAreaDao;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.archives.workTeam.dao.WorkTeamDao;
import com.ruoyi.archives.workTeam.domain.WorkTeam;
import com.ruoyi.archives.workTeam.service.WorkTeamService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.dao.ISysDeptDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;


@Slf4j
@Service
@RequiredArgsConstructor
public class WorkTeamServiceImpl implements WorkTeamService {

    private final WorkTeamDao workTeamDao;
    private final ISysDeptDao deptDao;
    private final IBizWorkAreaDao workAreaDao;


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @Override
    public boolean save(WorkTeam entity) {
        if (workTeamDao.isCodeUnique(WorkTeam::getCode, WorkTeam::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("班组编码不能重复");
        }
        isDuplicateForUpdate(entity, "新增");
        return workTeamDao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(WorkTeam entity) {
        if (workTeamDao.isCodeUnique(WorkTeam::getCode, WorkTeam::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("班组编码不能重复");
        }
        isDuplicateForUpdate(entity, "修改");
        return workTeamDao.updateById(entity);
    }

    @Override
    public List<WorkTeam> listWorkTeamWithPermi(WorkTeam workTeam) {
        return workTeamDao.listWorkTeamWithPermi(workTeam);
    }

    /**
     * 数据校验
     *
     * @param entity
     * @return
     */
    private void isDuplicateForUpdate(WorkTeam entity, String content) {
        LambdaQueryChainWrapper<WorkTeam> queryWrapper = workTeamDao.lambdaQuery()
                .eq(WorkTeam::getName, entity.getName())
                .eq(WorkTeam::getWorkAreaId, entity.getWorkAreaId());

        if (entity.getId() != null) {
            queryWrapper.ne(WorkTeam::getId, entity.getId());
        }

        boolean isDuplicate = queryWrapper.count() > 0;

        if (isDuplicate) {
            BizWorkArea en = workAreaDao.lambdaQuery()
                    .eq(BizWorkArea::getId, entity.getWorkAreaId())
                    .select(BizWorkArea::getName)
                    .one();

            if (en != null) {// 如果找到对应数据
                throw new ServiceException(content + "班组'" + entity.getName() + "'失败，班组名称在'" + en.getName() + "'工区中已存在");
            } else {
                throw new ServiceException(content + "班组'" + entity.getName() + "'失败，工区ID对应的名称不存在");
            }
        }
    }
}

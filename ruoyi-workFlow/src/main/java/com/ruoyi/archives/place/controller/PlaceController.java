package com.ruoyi.archives.place.controller;

import com.ruoyi.archives.BizWellhead.dao.IBizWellheadDao;
import com.ruoyi.archives.Stope.domain.Stope;
import com.ruoyi.archives.place.dao.IPlaceDao;
import com.ruoyi.archives.place.domain.Place;
import com.ruoyi.archives.place.service.IPlaceService;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.system.dao.ISysDeptDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@RestController
@RequestMapping("/workFlow/place")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class PlaceController extends BaseController implements IBaseController {

    private final IPlaceDao placeDao;
    private final ISysDeptDao deptService;
    private final IBizWellheadDao wellheadDao;
    private final IPlaceService placeService;

    @GetMapping
    public AjaxResult list(Place place) {
        return success(getPageInfo(() -> placeDao.list(place)));
    }


    @GetMapping("/listPlaceWithPermi")
    public AjaxResult listPlaceWithPermi(Place place) {
        return success(getPageInfo(() -> placeService.listPlaceWithPermi(place)));
    }

    @Log(title = "作业地点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(Place entity) {
        new ExcelUtil<>(Place.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> placeDao.list(entity)).getList(), "作业地点数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(placeDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "作业地点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Place place) {
        return placeService.save(place) ? toAjax(place.getId()) : toAjax(false);
    }

    @Log(title = "作业地点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Place place) {
        return placeService.updateById(place) ? toAjax(place.getId()) :
                toAjax(false);
    }

    @Log(title = "作业地点", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(placeDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }

    //根据code查询
    @GetMapping("/listByCodes")
    public AjaxResult listByCodes(@RequestParam(value = "codes", required = false) String codes) {
        return success(placeDao.listByCodes(codes));
    }

    @GetMapping("/listByIds")
    public AjaxResult listByIds(String ids) {
        return success(placeDao.listByIds(ids));
    }

    /**
     * 获取部门树列表
     */

    @GetMapping("/deptAndWellheadTreeSelect")
    public AjaxResult deptAndWellheadTree(SysDept dept) {
        return success(placeDao.listDeptAndWellheadTree(deptService.list(dept), wellheadDao.list()));
    }
}

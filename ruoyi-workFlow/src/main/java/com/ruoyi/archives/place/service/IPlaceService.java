package com.ruoyi.archives.place.service;

import com.ruoyi.archives.place.domain.Place;

import java.util.List;

public interface IPlaceService {

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    boolean save(Place entity);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    boolean updateById(Place entity);

    List<Place> listPlaceWithPermi(Place place);
}

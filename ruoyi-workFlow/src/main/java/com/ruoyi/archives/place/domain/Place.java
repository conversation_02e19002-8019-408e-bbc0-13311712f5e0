package com.ruoyi.archives.place.domain;


import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
import com.ruoyi.archives.paragraph.domain.Paragraph;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_place")
public class Place extends BaseEntity<Place> {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.AUTO)
    protected Long id;

    /**
     * 编码
     */
    @Excel(name = "编码")
    private String code;

    /**
     * 名称
     */
    @Excel(name = "名称", width = 25)
    private String name;

    /**
     * 全称
     */
    private String fullName;

    /**
     * 中段ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;

    /**
     * 部门ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /**
     * 井口ID
     */
    private Long wellheadId;

    @TableField(exist = false)
    @Excel(name = " 部门")
    @FieldMapping(tag = SysDept.class, thisField = Place.Fields.deptId, select = SysDept.Fields.deptName)
    private String deptName;

    @TableField(exist = false)
    @Excel(name = " 井口")
    @FieldMapping(tag = BizWellhead.class, thisField = Place.Fields.wellheadId, select = BizWellhead.Fields.name)
    private String wellheadName;

    /**
     * 中段
     */
    @Excel(name = "中段")
    @TableField(exist = false)
    @FieldMapping(tag = Paragraph.class, thisField = Place.Fields.paragraphId, select = Paragraph.Fields.name)
    private String paragraphName;


    /**
     * 勘探线
     */
    @Excel(name = "勘探线")
    private String explorationLine;

    @Excel(name = "备注")
    private String remark;


    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}

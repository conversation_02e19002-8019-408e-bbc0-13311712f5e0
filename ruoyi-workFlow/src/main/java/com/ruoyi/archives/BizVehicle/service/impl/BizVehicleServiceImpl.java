package com.ruoyi.archives.BizVehicle.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.archives.BizVehicle.dao.IBizVehicleDao;
import com.ruoyi.archives.BizVehicle.domain.BizVehicle;
import com.ruoyi.archives.BizVehicle.service.IBizVehicleService;
import com.ruoyi.common.exception.ServiceException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2024-12-16 18:03
 */
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizVehicleServiceImpl implements IBizVehicleService {

    private final IBizVehicleDao bizVehicleDao;

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @Override
    public boolean save(BizVehicle entity) {
        if (bizVehicleDao.isCodeUnique(BizVehicle::getCode, BizVehicle::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("车辆编码不能重复");
        }
        isDuplicateForUpdate(entity, "新增");
        return bizVehicleDao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(BizVehicle entity) {
        if (bizVehicleDao.isCodeUnique(BizVehicle::getCode, BizVehicle::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("车辆编码不能重复");
        }
        isDuplicateForUpdate(entity, "修改");
        return bizVehicleDao.updateById(entity);
    }

    /**
     * 数据校验
     *
     * @param entity
     * @return
     */
    private void isDuplicateForUpdate(BizVehicle entity, String content) {
        // 车牌号确定唯一
        LambdaQueryChainWrapper<BizVehicle> queryWrapper = bizVehicleDao.lambdaQuery()
                .eq(BizVehicle::getName, entity.getName());
        if (entity.getId() != null) {
            queryWrapper.ne(BizVehicle::getId, entity.getId());
        }
        boolean isDuplicate = queryWrapper.count() > 0;
        if (isDuplicate) {
            throw new ServiceException(content + "失败，车牌号已存在");
            //throw new ServiceException(content + "车辆'" + entity.getName() + "'失败，车牌号已存在");
        }
    }

}

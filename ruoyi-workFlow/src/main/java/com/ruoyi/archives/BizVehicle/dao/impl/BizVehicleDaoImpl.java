package com.ruoyi.archives.BizVehicle.dao.impl;

import com.ruoyi.archives.BizVehicle.dao.IBizVehicleDao;
import com.ruoyi.archives.BizVehicle.domain.BizVehicle;
import com.ruoyi.archives.BizVehicle.mapper.BizVehicleMapper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizVehicleDaoImpl extends BaseDaoImpl<BizVehicleMapper, BizVehicle> implements

        IBizVehicleDao {
}

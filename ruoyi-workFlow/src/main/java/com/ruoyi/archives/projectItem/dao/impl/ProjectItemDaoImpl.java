package com.ruoyi.archives.projectItem.dao.impl;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.ruoyi.archives.projectItem.dao.ProjectItemDao;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.archives.projectItem.mapper.ProjectItemMapper;

import com.ruoyi.archives.workTeam.domain.WorkTeam;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProjectItemDaoImpl extends BaseDaoImpl<ProjectItemMapper, ProjectItem> implements ProjectItemDao {

    private final ProjectItemMapper projectItemMapper;

    @Override
    public List<ProjectItem> GetList(ProjectItem projectItem) {
        return projectItemMapper.GetList(projectItem);
    }


    public ProjectItem SelectOne(long id) {
        return projectItemMapper.selectOne(id);
    }

    @Override
    public List<ProjectItem> getProjectItemList(ProjectItem projectItem) {
        return projectItemMapper.getProjectItemList(projectItem);
    }

    @Override
    @DataScope(deptAlias = "d")
    public List<ProjectItem> listProjectItemWithPermi(ProjectItem projectItem) {
        List<ProjectItem> projectItemList = projectItemMapper.list(projectItem);
        Relation.list(projectItemList, 0, DeepConfig.defaultConfig());
        return projectItemList;
    }

//    @Override
//    public List<BizProjectItem> list(BizProjectItem bizProjectItem) {
//        List<BizProjectItem> all = new ArrayList<>();
//        List<BizProjectItem> list = bizProjectItemMapper.list(bizProjectItem);
//        for (BizProjectItem item : list){
//            item.setProjectItemType(bizProjectItemtypeMapper.selectById(item.getProjectItemTypeId()));
//            all.add(item);
//        }
//        return all;
//    }


}

package com.ruoyi.archives.projectItem.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizConstructionUnit.domain.BizConstructionUnit;
import com.ruoyi.archives.Stope.domain.Stope;
import com.ruoyi.archives.place.domain.Place;
import com.ruoyi.archives.projectType.domain.ProjectType;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_project_item")
public class ProjectItem extends BaseEntity<ProjectItem> {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.AUTO)
    protected Long id;

    /**
     * 编码
     */
    /*@Excel(name = "编码")*/
    private String code;

    /**
     * 名称
     */
    @Excel(name = "名称", width = 35)
    private String name;



    /**
     * 全称
     */
    private String fullName;

    @Excel(name = "作业地点", width = 35)
    @TableField(exist = false)
    @FieldMapping(tag = Place.class, thisField = ProjectItem.Fields.placeId, select = Place.Fields.name)
    private String placeName;

    @Excel(name = "工程类别名称", dictType = "project_name")
    private String projectTypeNameValue;


    @Excel(name = "工程类型")
    @TableField(exist = false)
    @FieldMapping(tag = ProjectType.class, thisField = ProjectItem.Fields.projectTypeId, select = ProjectType.Fields.name)
    private String projectTypeName;


    //@Excel(name = "工程类型")
    /**
     * 工程类型id
     */
    private Long projectTypeId;

    @Excel(name = "采场层面号")
    @TableField(exist = false)
    @FieldMapping(tag = Stope.class, thisField = ProjectItem.Fields.stopeId, select = Stope.Fields.stopeLevelNum)
    private String stopeLevelNum;

    @Excel(name = "实施申请项目名称")
    private String projectImplementApplyName;


    private Long operationalUnitId;

    @Excel(name = "作业单位")
    @FieldMapping(tag = BizConstructionUnit.class, thisField = ProjectItem.Fields.operationalUnitId, select = BizConstructionUnit.Fields.name)
    @TableField(exist = false)
    private String operationalUnitName;


    @Excel(name = "成本分类", dictType = "cost_type")
    private String costTypeValue;

    @Excel(name = "投资类型", dictType = "investment_type")
    private String investmentTypeValue;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 作业地点Id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long placeId;

    /**
     * 采场ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stopeId;


    @TableField(exist = false)
    private Long deptId;

    @TableField(exist = false)
    private Long wellheadId;

    @TableField(exist = false)
    private Long paragraphId;

    @TableField(exist = false)
    private Long workAreaId;

    @Excel(name = "状态", dictType = "submit_status")
    private String submitStatusValue;


    /**
     * 是否完成
     */
    private String completeStatusValue;

    /**
     * 工程性质
     */
    @TableField(exist = false)
    private String projectPropertyValue;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;



}

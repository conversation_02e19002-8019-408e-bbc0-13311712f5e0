package com.ruoyi.archives.projectItem.dao;

import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.common.dao.IBaseDao;

import java.util.List;

public interface ProjectItemDao extends IBaseDao<ProjectItem> {
    @DataScope(deptAlias = "d")
    @Override
    default List<ProjectItem> list(ProjectItem entity) {
        return IBaseDao.super.list(entity);
    }

    @DataScope(deptAlias = "d")
    List<ProjectItem> GetList(ProjectItem projectItem);

    ProjectItem SelectOne(long id);

    List<ProjectItem> getProjectItemList(ProjectItem projectItem);

    List<ProjectItem> listProjectItemWithPermi(ProjectItem projectItem);
}

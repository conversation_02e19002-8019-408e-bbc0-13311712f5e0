package com.ruoyi.archives.Stope.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.place.domain.Place;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_stope")
public class Stope extends BaseEntity<Stope> {
    /**
     * 编码
     */
   // @Excel(name = "编码")
    private String code;

    @Excel(name = "作业地点", width = 28)
    @TableField(exist = false)
    @FieldMapping(tag = Place.class, thisField = Stope.Fields.placeId, select = Place.Fields.name)
    private String placeName;

    /**
     * 采场层面号
     */
    @Excel(name = "采场层面号")
    private String stopeLevelNum;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 作业地点ID
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long placeId;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;



    @TableField(exist = false)
    private Long deptId;

    /**
     * 设计-地质储量
     */
    @Excel(name = "设计-地质储量")
    private String designGeologicalReserves;

    /**
     * 设计-地质储量-品位
     */
    @Excel(name = "设计-地质储量-品位")
    private String designGrGrade;

    /**
     * 设计-地质储量-金属量
     */
    @Excel(name = "设计-地质储量-金属量")
    private String  designGrMetallicity;

    /**
     * 设计-采矿量
     */
    @Excel(name = "设计-采矿量")
    private String designMiningVolume;

    /**
     * 设计-采矿量-品位
     */
    @Excel(name = "设计-采矿量-品位")
    private String designMvGrade;

    /**
     * 设计-采矿量-金属量
     */
    @Excel(name = "设计-采矿量-金属量")
    private String designMvMetallicity;

    /**
     * 设计-技术指标-损失率
     */
    @Excel(name = "设计-技术指标-损失率")
    private String designTiLossRate;

    /**
     * 设计-技术指标-贫化率
     */
    @Excel(name = "设计-技术指标-贫化率")
    private String designTiDilutionRate;

}

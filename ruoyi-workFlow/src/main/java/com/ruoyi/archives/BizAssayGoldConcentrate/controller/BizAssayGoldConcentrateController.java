package com.ruoyi.archives.BizAssayGoldConcentrate.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.ruoyi.archives.BizAssayGoldConcentrate.dao.IBizAssayGoldConcentrateDao;
import com.ruoyi.archives.BizAssayGoldConcentrate.domain.BizAssayGoldConcentrate;
import com.ruoyi.archives.BizAssayGoldConcentrate.service.IBizAssayGoldConcentrateService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/BizAssayGoldConcentrate")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizAssayGoldConcentrateController extends BaseController implements IBaseController {

    private final IBizAssayGoldConcentrateDao bizAssayGoldConcentrateDao;
    private final IBizAssayGoldConcentrateService bizAssayGoldConcentrateService;

    @GetMapping
    public AjaxResult list(BizAssayGoldConcentrate bizAssayGoldConcentrate) {
        return success(getPageInfo(() -> bizAssayGoldConcentrateDao.list(bizAssayGoldConcentrate)));
    }

    @Log(title = "金精矿化验结果数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizAssayGoldConcentrate bizAssayGoldConcentrate) {
        new ExcelUtil<>(BizAssayGoldConcentrate.class).
                exportExcel(response, getPageInfo(() -> bizAssayGoldConcentrateDao.list(bizAssayGoldConcentrate)).getList(), "金精矿化验结果");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizAssayGoldConcentrateDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "添加金精矿化验结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizAssayGoldConcentrate bizAssayGoldConcentrate) {
        return bizAssayGoldConcentrateService.save(bizAssayGoldConcentrate) ? toAjax(bizAssayGoldConcentrate.getId()) : toAjax(false);
    }

    @Log(title = "修改金精矿化验结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizAssayGoldConcentrate bizAssayGoldConcentrate) {
        return bizAssayGoldConcentrateService.updateById(bizAssayGoldConcentrate) ? toAjax(bizAssayGoldConcentrate.getId()) :
                toAjax(false);
    }

    @Log(title = "删除金精矿化验结果", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizAssayGoldConcentrateDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }

    /**
     * 下载导入模板
     */
    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception{
//        new ExcelUtil<>(BizAssayGoldConcentrate.class).importTemplateExcel(response, "金精矿化验结果");
        ClassPathResource classPathResource = new ClassPathResource("file/BizAssayGoldConcentrate.xls");
        @Cleanup InputStream inputStream = classPathResource.getStream();
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        @Cleanup Workbook template = WorkbookFactory.create(inputStream);
        template.write(outputStream);
    }



    /**
     * 导入作业量完成详情列表
     *
     * @param file
     * @return
     */
    @Log(title = "金精矿化验结果导入", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport,@RequestParam("deptId") String deptId,@RequestParam("year") String year) throws Exception {
        ExcelUtil<BizAssayGoldConcentrate> util = new ExcelUtil<>(BizAssayGoldConcentrate.class);
        List<BizAssayGoldConcentrate> bizAssayGoldConcentrateList = util.importExcel(file.getInputStream(),2);

        System.out.println("bizAssayGoldConcentrateList:"+bizAssayGoldConcentrateList);
        bizAssayGoldConcentrateList.stream().forEach(bizAssayGoldConcentrate -> bizAssayGoldConcentrate.setDeptId(Long.parseLong(deptId)).setYear(year));
        System.out.println("  --"+year);


        String operName = getUsername();
        String message = bizAssayGoldConcentrateService.importBizAssayGoldConcentrate(bizAssayGoldConcentrateList,
                updateSupport, operName);
        return success(message);
    }

    @GetMapping("/getSumStatistics")
    public AjaxResult getSumStatistics(BizAssayGoldConcentrate bizAssayGoldConcentrate){
        return success(bizAssayGoldConcentrateService.sumStatistics(bizAssayGoldConcentrate));
    }
}

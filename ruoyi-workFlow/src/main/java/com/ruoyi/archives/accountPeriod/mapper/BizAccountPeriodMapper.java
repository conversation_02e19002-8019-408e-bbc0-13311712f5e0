package com.ruoyi.archives.accountPeriod.mapper;

import com.ruoyi.archives.accountPeriod.domain.AccountPeriod;
import com.ruoyi.common.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.Date;
import java.util.List;

@Mapper
public interface BizAccountPeriodMapper extends IBaseMapper<AccountPeriod> {
    AccountPeriod getAccountPeriodByDate(Date date);

    List<AccountPeriod> selectForSelect(AccountPeriod entity);

    AccountPeriod selectForSelectDefaultId();
}

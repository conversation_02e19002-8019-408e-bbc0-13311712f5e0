package com.ruoyi.archives.workArea.dao.impl;

import com.ruoyi.archives.workArea.dao.IBizWorkAreaDao;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.archives.workArea.mapper.BizWorkAreaMapper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizWorkAreaDaoImpl extends BaseDaoImpl<BizWorkAreaMapper, BizWorkArea> implements

        IBizWorkAreaDao {
}

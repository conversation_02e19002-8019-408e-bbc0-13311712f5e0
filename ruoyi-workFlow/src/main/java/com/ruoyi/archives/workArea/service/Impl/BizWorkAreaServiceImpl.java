package com.ruoyi.archives.workArea.service.Impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.archives.workArea.dao.IBizWorkAreaDao;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.archives.workArea.service.IBizWorkAreaService;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.dao.ISysDeptDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@RequiredArgsConstructor
public class BizWorkAreaServiceImpl implements IBizWorkAreaService {

    private final IBizWorkAreaDao bizWorkAreaDao;
    private final ISysDeptDao deptDao;

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @Override
    public boolean save(BizWorkArea entity) {
        if (bizWorkAreaDao.isCodeUnique(BizWorkArea::getCode, BizWorkArea::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("工区编码不能重复");
        }
        isDuplicateForUpdate(entity, "新增");
        return bizWorkAreaDao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(BizWorkArea entity) {
        if (bizWorkAreaDao.isCodeUnique(BizWorkArea::getCode, BizWorkArea::getId,
                entity.getCode(), entity.getId())) {
            throw new ServiceException("工区编码不能重复");
        }
        isDuplicateForUpdate(entity, "修改");
        return bizWorkAreaDao.updateById(entity);
    }

    /**
     * 数据校验
     *
     * @param entity
     * @return
     */
    private void isDuplicateForUpdate(BizWorkArea entity, String content) {
        LambdaQueryChainWrapper<BizWorkArea> queryWrapper = bizWorkAreaDao.lambdaQuery()
                .eq(BizWorkArea::getName, entity.getName())
                .eq(BizWorkArea::getDeptId, entity.getDeptId());

        if (entity.getId() != null) {
            queryWrapper.ne(BizWorkArea::getId, entity.getId());
        }

        boolean isDuplicate = queryWrapper.count() > 0;

        if (isDuplicate) {
            SysDept dept = deptDao.lambdaQuery()
                    .eq(SysDept::getDeptId, entity.getDeptId())
                    .select(SysDept::getDeptName)
                    .one();

            if (dept != null) {// 如果找到对应部门
                throw new ServiceException(content + "工区'" + entity.getName() + "'失败，工区名称在" + dept.getDeptName() + "中已存在");
            } else {
                throw new ServiceException(content + "工区'" + entity.getName() + "'失败，对应部门不存在");
            }
        }
    }
}

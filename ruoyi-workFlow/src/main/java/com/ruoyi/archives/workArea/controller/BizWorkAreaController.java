package com.ruoyi.archives.workArea.controller;

import com.ruoyi.archives.workArea.dao.IBizWorkAreaDao;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.archives.workArea.service.IBizWorkAreaService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.system.dao.ISysDeptDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@RestController
@RequestMapping("/workflow/workArea")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizWorkAreaController extends BaseController implements IBaseController {

    private final IBizWorkAreaDao bizWorkAreaDao;
    private final IBizWorkAreaService bizWorkAreaService;
    private final ISysDeptDao sysDeptDao;

    @GetMapping
    public AjaxResult list(BizWorkArea bizWorkArea) {
        return success(getPageInfo(() -> bizWorkAreaDao.list(bizWorkArea)));
    }

    // 查询所有的工区
    @GetMapping("/workAreaList")
    public AjaxResult workAreaList(BizWorkArea bizWorkArea) {
        return success(bizWorkAreaDao.list(bizWorkArea));
    }

    @GetMapping("/getAll")
    public AjaxResult getAllList(BizWorkArea bizWorkArea) {
        return success(bizWorkAreaDao.list(bizWorkArea));
    }

    @GetMapping("Datelist")
    public AjaxResult listDate(SysDept bizWorkArea) {
        return success(sysDeptDao.list(bizWorkArea));
    }


    @Log(title = "工区管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(BizWorkArea bizWorkArea) {
        new ExcelUtil<>(BizWorkArea.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> bizWorkAreaDao.list(bizWorkArea)).getList(), "工区管理数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizWorkAreaDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "工区/工段", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizWorkArea bizWorkArea) {
        return bizWorkAreaService.save(bizWorkArea) ? toAjax(bizWorkArea.getId()) : toAjax(false);
    }

    @Log(title = "工区/工段", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizWorkArea bizWorkArea) {
        return bizWorkAreaService.updateById(bizWorkArea) ? toAjax(bizWorkArea.getId()) :
                toAjax(false);
    }

    @Log(title = "工区/工段", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizWorkAreaDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }

    //根据code查询
    @GetMapping("/listByCodes")
    public AjaxResult listByCodes(@RequestParam(value = "codes", required = false) String codes) {
        return success(bizWorkAreaDao.listByCodes(codes));
    }
}

package com.ruoyi.archives.BizAssayMethod.controller;

import com.ruoyi.archives.BizAssayMethod.dao.IBizAssayMethodDao;
import com.ruoyi.archives.BizAssayMethod.domain.BizAssayMethod;
import com.ruoyi.archives.BizAssayMethod.service.IBizAssayMethodService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@RestController
@RequestMapping("/workflow/BizAssayMethod")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizAssayMethodController extends BaseController implements IBaseController {

    private final IBizAssayMethodDao bizAssayMethodDao;
    private final IBizAssayMethodService bizAssayMethodService;

    @GetMapping
    public AjaxResult list(BizAssayMethod bizAssayMethod) {
        return success(getPageInfo(() -> bizAssayMethodDao.list(bizAssayMethod)));
    }

    @Log(title = "化验方法", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BizAssayMethod bizAssayMethod) {
        new ExcelUtil<>(BizAssayMethod.class).
                exportExcel(response, getPageInfo(() -> bizAssayMethodDao.list(bizAssayMethod)).getList(), "化验方法数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizAssayMethodDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "化验方法", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody BizAssayMethod bizAssayMethod) {
        return bizAssayMethodService.save(bizAssayMethod) ? toAjax(bizAssayMethod.getId()) : toAjax(false);
    }

    @Log(title = "化验方法", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody BizAssayMethod bizAssayMethod) {
        return bizAssayMethodService.updateById(bizAssayMethod) ? toAjax(bizAssayMethod.getId()) :
                toAjax(false);
    }

    @Log(title = "化验方法", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizAssayMethodDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }


}

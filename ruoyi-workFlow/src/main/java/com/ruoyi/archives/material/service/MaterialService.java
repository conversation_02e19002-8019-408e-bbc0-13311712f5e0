package com.ruoyi.archives.material.service;

import com.ruoyi.archives.material.domain.Material;
import com.ruoyi.geology.availableReserves.domain.AvailableReserves;

import java.util.List;

public interface MaterialService {

    boolean save(Material material);

    boolean updateById(Material material);


    /**
     * 导入
     *
     * @param materialList
     * @param operName
     * @return
     */
    String importUpdate(List<Material> materialList, String operName);
}

package com.ruoyi.archives.costCenterMapping.controller;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.archives.costCenterMapping.domain.CostCenterMapping;
import com.ruoyi.archives.costCenterMapping.dao.ICostCenterMappingDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("/workflow/CostCenterMapping")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class CostCenterMappingController extends BaseController implements IBaseController {

    private final ICostCenterMappingDao dao;

    @GetMapping
    public AjaxResult list(CostCenterMapping entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "成本中心对应仓库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( CostCenterMapping entity) {
        new ExcelUtil<>(CostCenterMapping. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "成本中心对应仓库数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "成本中心对应仓库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CostCenterMapping entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "成本中心对应仓库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CostCenterMapping entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "成本中心对应仓库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }


}

package com.ruoyi.archives.financeItemType.mapper;

import com.ruoyi.archives.financeItemType.domain.FinanceItemType;
import com.ruoyi.common.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FinanceItemTypeMapper extends IBaseMapper<FinanceItemType> {

    /**
     * 根据ID查询所有子类型
     *
     * @param id 类型ID
     * @return 类型列表
     */
    List<FinanceItemType> listChildrenById(Long id);

    /**
     * 修改子元素关系
     *
     * @param types 子元素
     * @return 结果
     */
    int updateChildren(@Param("types") List<FinanceItemType> types);
}

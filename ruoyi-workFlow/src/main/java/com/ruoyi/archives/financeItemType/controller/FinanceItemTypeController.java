package com.ruoyi.archives.financeItemType.controller;

import com.ruoyi.archives.financeItemType.dao.IFinanceItemTypeDao;
import com.ruoyi.archives.financeItemType.domain.FinanceItemType;
import com.ruoyi.archives.financeItemType.service.FinanceItemTypeService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@RestController
@RequestMapping("/workflow/financeItemType")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class FinanceItemTypeController extends BaseController implements IBaseController {

    private final IFinanceItemTypeDao bizFinanceItemTypeDao;
    private final FinanceItemTypeService service;


    @GetMapping
    public AjaxResult list(FinanceItemType bizFinanceItemType) {
        return success(getPageInfo(() -> bizFinanceItemTypeDao.list(bizFinanceItemType)));
    }

    @Log(title = "作业项目类型信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, FinanceItemType bizFinanceItemType) {
        new ExcelUtil<>(FinanceItemType.class).
                exportExcel(response, getPageInfo(() -> bizFinanceItemTypeDao.list(bizFinanceItemType)).getList(), "作业项目类型信息数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(bizFinanceItemTypeDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "作业项目类型信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody FinanceItemType bizFinanceItemType) {
        return service.save(bizFinanceItemType) ? toAjax(bizFinanceItemType.getId()) : toAjax(false);
    }

    @Log(title = "作业项目类型信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody FinanceItemType bizFinanceItemType) {
        return service.updateById(bizFinanceItemType) ? toAjax(bizFinanceItemType.getId()) :
                toAjax(false);
    }

    @Log(title = "作业项目类型信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(bizFinanceItemTypeDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::
                new)));
    }

    //根据code查询
    @GetMapping("/listByCodes")
    public AjaxResult listByCodes(@RequestParam(value = "codes", required = false) String codes) {
        return success(bizFinanceItemTypeDao.listByCodes(codes));
    }

    /**
     * 获取作业项目类型树列表
     */

    @GetMapping("/fianceItemTypeTree")
    public AjaxResult fianceItemTypeTree(FinanceItemType financeItemType) {
        return success(bizFinanceItemTypeDao.listTreeSelect(bizFinanceItemTypeDao.list(financeItemType)));
    }

}

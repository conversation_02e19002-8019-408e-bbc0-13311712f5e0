package com.ruoyi.demo.mobileComponentsTest.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.demo.mobileComponentsTest.dao.IBizMobileComponentsTestSubDao;
import com.ruoyi.demo.mobileComponentsTest.domain.BizMobileComponentsTestSub;
import com.ruoyi.demo.mobileComponentsTest.mapper.BizMobileComponentsTestSubMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

/**
 * 手机端控件测试子Service业务层处理
 * 已将自动生成的代码 尽量抽象 default 到 interface 中，若有特殊需求请在实现类 Impl 中 重写
 * 默认继承了 MybatisPlus 的 ServiceImpl<M,T>,IService<T> 接口,可以使用基于BaseMapper的SQL查询操作，必要时候可以重写方法进行覆盖；
 * <p>
 * 模板方法已经抽好 default 进 interface 中，实现类里只定义特殊业务方法即可，涉及到的 CRUD 也请尽量调用 interface；
 *
 * <AUTHOR>
 * @date 2023-06-30
 */
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizMobileComponentsTestSubDaoImpl extends BaseDaoImpl<BizMobileComponentsTestSubMapper, BizMobileComponentsTestSub> implements IBizMobileComponentsTestSubDao {


}

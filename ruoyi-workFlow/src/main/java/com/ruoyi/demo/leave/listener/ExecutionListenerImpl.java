package com.ruoyi.demo.leave.listener;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ExecutionListenerImpl implements ExecutionListener {


    @Override
    public void notify(DelegateExecution delegateExecution) {
        String event = delegateExecution.getEventName();

        String processDefinitionId = delegateExecution.getProcessDefinitionId();
//        System.out.println("[StartExecListener]当前流程定义id：" + processDefinitionId);

        switch (event) {
            case "start":
//                System.out.println("start event");
                break;
            case "end":
//                System.out.println("end event");
                break;
            case "take":
//                System.out.println("take event");
                break;
        }
    }
}

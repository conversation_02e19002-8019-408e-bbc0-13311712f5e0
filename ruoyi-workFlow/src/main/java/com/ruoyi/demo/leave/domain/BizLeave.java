package com.ruoyi.demo.leave.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 请假对象 biz_leave
 *
 * <AUTHOR>
 * @date 2022-10-14
 */

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BizLeave extends ProcessEntity<BizLeave> {


    /**
     * 请假类型
     */
    @Excel(name = "请假类型")

    private String leaveType;

    /**
     * 标题
     */
    @Excel(name = "标题")

    private String title;

    /**
     * 原因
     */
    @Excel(name = "原因")

    private String reason;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd")

    private Date leaveStartTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")

    private Date leaveEndTime;

    /**
     * 请假时长，单位秒
     */
    @Excel(name = "请假时长，单位秒")

    private Long totalTime;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际开始时间", width = 30, dateFormat = "yyyy-MM-dd")

    private Date realityStartTime;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "实际结束时间", width = 30, dateFormat = "yyyy-MM-dd")

    private Date realityEndTime;


    private String createById;
}

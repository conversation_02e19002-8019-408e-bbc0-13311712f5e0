package com.ruoyi.rpt.dao.impl;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.rpt.dao.IBizRptMiningDailyDao;
import com.ruoyi.rpt.domain.BizRptMiningDaily;
import com.ruoyi.rpt.mapper.BizRptMiningDailyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizRptMiningDailyDaoImpl extends BaseDaoImpl<BizRptMiningDailyMapper, BizRptMiningDaily> implements IBizRptMiningDailyDao {
    @Override
    public List<BizRptMiningDaily> listReduce(BizRptMiningDaily entity) {
        List<BizRptMiningDaily> list = baseMapper.listReduce(entity);
        Relation.list(list, 0, DeepConfig.defaultConfig());
        return list;
    }
}

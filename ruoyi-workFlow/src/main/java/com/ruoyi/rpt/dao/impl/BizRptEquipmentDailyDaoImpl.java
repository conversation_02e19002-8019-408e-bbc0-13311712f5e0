package com.ruoyi.rpt.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.rpt.dao.IBizRptEquipmentDailyDao;
import com.ruoyi.rpt.domain.BizRptEquipmentDaily;
import com.ruoyi.rpt.mapper.BizRptEquipmentDailyMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizRptEquipmentDailyDaoImpl extends BaseDaoImpl<BizRptEquipmentDailyMapper, BizRptEquipmentDaily> implements

        IBizRptEquipmentDailyDao {
}

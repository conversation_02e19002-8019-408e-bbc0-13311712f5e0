package com.ruoyi.rpt.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@ExcelIgnoreUnannotated
public class BizRptConcentratorDailyBriefing extends BaseEntity<BizRptConcentratorDailyBriefing> {
    /**
     * 班次
     */
    @ExcelProperty("班次")
    @Excel(name = "班次")
    private String workShiftValue;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    @ExcelProperty("部门")
    @Excel(name = "部门")
    @FieldMapping(tag = SysDept.class, select = SysDept.Fields.deptName, thisField = Fields.deptId)
    @TableField(exist = false)
    private String deptName;
    /**
     * 单据时间
     */
    @ExcelProperty("时间")
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "单据时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dispatchDate;

    /**
     * 处理矿量(吨)
     */
    @ExcelProperty("处理量(t)")
    @Excel(name = "处理矿量(t)")
    private BigDecimal processingOres;

    /**
     * 开车运行时长h
     */
    @ExcelProperty("开车时间(h)")
    @Excel(name = "开车运行时长(h)")
    private BigDecimal runningTime;

    /**
     * 原矿品位g/t
     */
    @ExcelProperty("原矿品位(g/t)")
    @Excel(name = "原矿品位(g/t)")
    private BigDecimal rawOreGrade;

    /**
     * 原矿金属量g
     */
    @ExcelProperty("原矿金属量(g)")
    @Excel(name = "原矿金属量(g)")
    private BigDecimal rawOreMetalAmount;

    /**
     * 精矿矿量t
     */
    @ExcelProperty("精矿矿量(t)")
    @Excel(name = "精矿矿量(t)")
    private BigDecimal concentrateAmount;

    /**
     * 精矿品位g/t
     */
    @ExcelProperty("精矿品位(g/t)")
    @Excel(name = "精矿品位(g/t)")
    private BigDecimal concentrateGrade;

    /**
     * 选矿产品金属量(g)
     */
    @ExcelProperty("选矿产品金属量(g)")
    @Excel(name = "选矿产品金属量(g)")
    private BigDecimal productMetals;

    /**
     * 尾矿品位g/t
     */
    @ExcelProperty("尾矿品位(g/t)")
    @Excel(name = "尾矿品位(g/t)")
    private BigDecimal tailingsGrade;

    /**
     * 选矿回收率(%)
     */
    @ExcelProperty("选矿回收率(%)")
    @Excel(name = "选矿回收率(%)")
    private BigDecimal recoveryRate;

    /**
     * 尾矿库回水抽水时间
     */
    @ExcelProperty("尾矿库回水抽水时间(h)")
    @Excel(name = "尾矿库回水抽水时间(h)")
    private BigDecimal tailingsPumpingTime;

    /**
     * 精矿金属量g
     */
    @ExcelProperty("精矿金属量(g)")
    @Excel(name = "精矿金属量(g)")
    private BigDecimal concentrateMetalAmount;

    /**
     * 矿石产区
     */
    @ExcelProperty("矿石产区")
    @Excel(name = "矿石产区")
    private String oreDesc;

}

package com.ruoyi.rec.mapper;

import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.rec.domain.BizRecEquipmentOperation;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface BizRecEquipmentOperationMapper extends IBaseMapper<BizRecEquipmentOperation> {
    @Update("{call p_equipment_peration_daily(#{id})}")
    Integer callStoredProcedure(@Param("id") Long id);


    List<BizRecEquipmentOperation> listEquipmentDaily(BizRecEquipmentOperation entity);
}

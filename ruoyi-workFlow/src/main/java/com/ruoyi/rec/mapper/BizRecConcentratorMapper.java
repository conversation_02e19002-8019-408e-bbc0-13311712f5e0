package com.ruoyi.rec.mapper;

import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.rec.domain.BizRecConcentrator;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface BizRecConcentratorMapper extends IBaseMapper<BizRecConcentrator> {

    @Update("{call p_concentrator_daily(#{id})}")
    Integer callStoredProcedure(@Param("id") Long id);

    String dailyPlanForChart(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String dailyCompletionVolume(@Param("dispatchDate")String dispatchDate, @Param("deptId")String deptId);

    String monthlyPlan(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String monthlyCompletionVolume(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String thePlanBackThen(@Param("accountYear")String accountYear, @Param("deptId")String deptId);

    String completedQuantityInThatYear(@Param("accountYear")String accountYear, @Param("deptId")String deptId);

    String metalsDailyPlan(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String metalsDailyCompletionVolume(@Param("dispatchDate")String dispatchDate, @Param("deptId")String deptId);

    String metalsMonthlyPlan(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String metalsMonthlyCompletionVolume(@Param("accountMonth") String accountMonth,@Param("accountYear") String accountYear,@Param("deptId") String deptId);

    String metalsThePlanBackThen(@Param("accountYear")String accountYear, @Param("deptId")String deptId);

    String metalsCompletedQuantityInThatYear(@Param("accountYear")String accountYear, @Param("deptId")String deptId);
}

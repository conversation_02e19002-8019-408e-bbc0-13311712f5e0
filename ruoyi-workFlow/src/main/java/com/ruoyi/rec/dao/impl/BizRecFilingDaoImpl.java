package com.ruoyi.rec.dao.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.rec.dao.IBizRecFilingDao;
import com.ruoyi.rec.domain.BizRecFiling;
import com.ruoyi.rec.mapper.BizRecFilingMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.Collections;
import java.util.Date;
import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizRecFilingDaoImpl extends BaseDaoImpl<BizRecFilingMapper, BizRecFiling> implements IBizRecFilingDao {

    private final BizRecFilingMapper  bizRecFilingMapper;
    @Override
    public boolean callStoredProcedure(Long id) {
        return SqlHelper.retBool(baseMapper.callStoredProcedure(id));
    }

    @Override
    public List<BizRecFiling> listReduceWellhead(BizRecFiling entity) {
        List<BizRecFiling> list = baseMapper.listReduceWellhead(entity);
        Relation.list(list, 0, DeepConfig.defaultConfig());
        return list;
    }

    @Override
    public List<BizRecFiling> getBizRecFilingDaily(BizRecFiling entity) {
        List<BizRecFiling> BizRecFilingDaily = baseMapper.getlist(entity);

        return BizRecFilingDaily;
    }

    @Override
    public String dailyIncrease(String formattedDate, String deptId) {
        return bizRecFilingMapper.dailyIncrease(formattedDate, deptId);
    }

    @Override
    public String monthlyIncrease(Date startDate, String formattedDate, String deptId) {
        return bizRecFilingMapper.monthlyIncrease(startDate, formattedDate, deptId);
    }

    @Override
    public String annualIncrease(LocalDate firstDayOfYear, String formattedDate, String deptId) {
        return bizRecFilingMapper.annualIncrease(firstDayOfYear, formattedDate, deptId);
    }

    @Override
    public List<BizRecFiling> dailyIncreaseAmount(LocalDate firstDayOfMonth, String formattedDate, String deptId) {
        return bizRecFilingMapper.dailyIncreaseAmount(firstDayOfMonth, formattedDate, deptId);
    }
}

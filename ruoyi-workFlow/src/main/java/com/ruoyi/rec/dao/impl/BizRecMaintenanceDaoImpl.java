package com.ruoyi.rec.dao.impl;

import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.rec.dao.IBizRecMaintenanceDao;
import com.ruoyi.rec.domain.BizRecMaintenance;
import com.ruoyi.rec.mapper.BizRecMaintenanceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BizRecMaintenanceDaoImpl extends BaseDaoImpl<BizRecMaintenanceMapper, BizRecMaintenance> implements IBizRecMaintenanceDao {

    @Override
    public boolean callStoredProcedure(Long id) {
        return SqlHelper.retBool(baseMapper.callStoredProcedure(id));
    }
}

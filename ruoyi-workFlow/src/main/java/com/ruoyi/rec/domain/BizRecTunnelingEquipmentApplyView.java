package com.ruoyi.rec.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.common.util.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.Date;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class BizRecTunnelingEquipmentApplyView extends BaseEntity<BizRecTunnelingEquipmentApplyView> {

    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected String createBy;
    @JsonFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected Date createTime;
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected String createById;
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected String updateBy;
    @JsonFormat(pattern = DateUtil.YYYY_MM_DD_HH_MM_SS, timezone = "GMT+8")
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected Date updateTime;
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected String updateById;
    @TableField(exist = false)
    protected String remark;
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected SysUser creator;
    @TableField(exist = false)
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    protected SysUser updater;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;
    @Excel(name = "部门")
    private String deptName;
    @Excel(name = "单据时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recTime;
    private String submitStatusValue;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;
    @Excel(name = "井口编号")
    private String wellheadCode;
    @Excel(name = "井口")
    private String wellheadName;
    private String tunnelingEquipmentValue;
    @Excel(name = "设备")
    private String tunnelingEquipmentLabel;
    @Excel(name = "0点班投入量")
    private String workShiftValue0;
    @Excel(name = "8点班投入量")
    private String workShiftValue1;
    @Excel(name = "4点班投入量")
    private String workShiftValue2;
    @Excel(name = "投入量")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long amount;
    @Excel(name = "详情描述")
    private String jobDetails;
}

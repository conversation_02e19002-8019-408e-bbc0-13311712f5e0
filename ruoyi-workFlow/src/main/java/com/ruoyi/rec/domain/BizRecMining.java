package com.ruoyi.rec.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class BizRecMining extends BaseEntity<BizRecMining> {

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;
    @FieldMapping(tag = SysDept.class, select = SysDept.Fields.deptName, thisField = Fields.deptId)
    @TableField(exist = false)
    private String deptName;

    /**
     * 单据时间
     */
    @Excel(name = "单据时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recTime;
    private String submitStatusValue;

    /**
     * 采矿信息 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = BizRecMiningSub.Fields.parentId)
    private List<BizRecMiningSub> bizRecMiningSubList = new ArrayList<>();

    /**
     * 取样日期(时间段) 开始时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startSamplingTime;

    /**
     * 取样日期(时间段) 结束时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endSamplingTime;


}

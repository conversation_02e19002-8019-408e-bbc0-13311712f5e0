package com.ruoyi.rec.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
public class BizRecTransportSub extends BaseEntity<BizRecTransportSub> {

//    @Excel(name = "单据时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date recTime;

//    @Excel(name = "起始时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date startSamplingTime;

//    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date endSamplingTime;

    @Excel(name = "驾驶员")
    @FieldMapping(tag = SysUser.class, select = SysUser.Fields.nickName, thisField = Fields.driverId)
    @TableField(exist = false)
    private String driverName;

    @Excel(name = "部门")
    @TableField(exist = false)
    private String deptName;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 车辆类型字典值 铲车 翻斗车
     */
    private String vehicleTypeValue;

    /**
     * 车
     */
    @Excel(name = "车")
    private String vehicle;

    /**
     * 驾驶员
     */

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long driverId;

    /**
     * 井口
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;

    @FieldMapping(tag = BizWellhead.class, select = BizWellhead.Fields.name, thisField = BizRecExplosivesSub.Fields.wellheadId)
    @TableField(exist = false)
    private String wellheadName;

    //作业内容字典
    @Excel(name = "作业内容" , dictType = "biz_transport_job_content")
    private String wellheadFlag;

    /**
     * 起点
     */
    private String startPoint;

    /**
     * 终点
     */
    private String endPoint;

    @Excel(name = "运输类型" , dictType = "biz_transport_type")
    private String transportTypeValue;

    /**
     * 其他内容
     */
    private String otherContent;

    /**
     * 数量
     */
    @Excel(name = "车数(车)")
    private String count;
    @Excel(name = "合计里程（公里）")
    private String mileage;
    @Excel(name = "工时(小时)")
    private BigDecimal hour;
    @Excel(name = "合计运输量（吨）")
    private BigDecimal loadCapacity;

    //服务对象Id
    private Long aimDeptId;

}

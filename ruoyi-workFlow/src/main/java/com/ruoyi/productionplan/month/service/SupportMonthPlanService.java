package com.ruoyi.productionplan.month.service;


import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;
import com.ruoyi.productionplan.month.domain.SupportMonthPlan;
import com.ruoyi.productionplan.month.domain.SupportMonthPlanSub;

import java.io.Serializable;
import java.util.List;

public interface SupportMonthPlanService {


    // 判断是否重复
    boolean isItRepeated(List<SupportMonthPlanSub> supportMonthPlanSubs);

    // 判断是否有年月
    boolean isSupportMonthPlan(SupportMonthPlan supportMonthPlan);

    // 导入
    String importUpdateSupportMonthPlanSub(List<SupportMonthPlanSub> mineOuputMonthPlanSubList, String operName);

    // 删除判断
    boolean DateApply(String[] ids);

    boolean IsItRepeated(Long accountYear, Long accountMonth, Long deptId);

    boolean submitApply(Serializable id);

    List<SupportMonthPlanSub> excavationAddExtractionPlan(SupportMonthPlan entity);
}

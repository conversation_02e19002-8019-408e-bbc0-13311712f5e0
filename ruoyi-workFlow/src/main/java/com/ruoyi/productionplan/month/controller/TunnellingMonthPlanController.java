package com.ruoyi.productionplan.month.controller;

import com.github.yulichang.toolkit.MPJWrappers;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productionplan.month.dao.ITunnellingMonthPlanDao;
import com.ruoyi.productionplan.month.dao.ITunnellingMonthPlanSubDao;
import com.ruoyi.productionplan.month.domain.TunnellingMonthPlan;
import com.ruoyi.productionplan.month.domain.TunnellingMonthPlanSub;
import com.ruoyi.productionplan.month.service.TunnellingMonthPlanService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("/workflow/tunnellingMonthPlan")
@RequiredArgsConstructor
public class TunnellingMonthPlanController extends BaseController implements IBaseController {

    private final ITunnellingMonthPlanDao dao;
    private final TunnellingMonthPlanService service;
    private final ITunnellingMonthPlanSubDao subDao;

    @GetMapping
    public AjaxResult list(TunnellingMonthPlan tunnellingMonthPlan) {
        return success(getPageInfo(() -> dao.list(tunnellingMonthPlan)));
    }

    /**
     * 当日掘进计划
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */

    @GetMapping("/dailyPlanForChart")
    public AjaxResult dailyPlanForChart(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        String dailyPlanStr = service.dailyPlanForChart(formattedDate, deptId);
        BigDecimal dailyPlan = StringUtils.isEmpty(dailyPlanStr) ? BigDecimal.ZERO : new BigDecimal(dailyPlanStr);
        // 四舍五入保留两位小数
        return success(dailyPlan.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
    }

    /**
     * 当日掘进数量
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */

    @GetMapping("/dailyQuantityForChart")
    public AjaxResult dailyQuantityForChart(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        String dailyQuantityStr = service.dailyQuantityForChart(formattedDate, deptId);
        BigDecimal dailyQuantity = StringUtils.isEmpty(dailyQuantityStr) ? BigDecimal.ZERO : new BigDecimal(dailyQuantityStr);
        // 四舍五入保留两位小数
        return success(dailyQuantity.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
    }

    /**
     * 掘进计划完成率
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/completionRate")
    public AjaxResult completionRate(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {

        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳

        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        // 当日掘进数量
        String dailyQuantityStr = service.dailyQuantityForChart(formattedDate, deptId);
        BigDecimal dailyQuantity = StringUtils.isEmpty(dailyQuantityStr) ? BigDecimal.ZERO : new BigDecimal(dailyQuantityStr);

        // 当日掘进计划
        String dailyPlanStr = service.dailyPlanForChart(formattedDate, deptId);
        BigDecimal dailyPlan = StringUtils.isEmpty(dailyPlanStr) ? BigDecimal.ZERO : new BigDecimal(dailyPlanStr);

        // 相除操作
        BigDecimal result;
        if (dailyPlan.compareTo(BigDecimal.ZERO) == 0) {
            result = BigDecimal.ZERO; // 避免除以零的情况
        } else {
            result = dailyQuantity.divide(dailyPlan, 2, BigDecimal.ROUND_HALF_UP);
        }
        return success(result.toString());
    }

    /**
     * 统计表
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/statisticalTable")
    public AjaxResult statisticalTable(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        // 获取各部分数据
        String monthlyPlannedQuantityStr = service.monthlyPlannedQuantityForChart(formattedDate, deptId);
        String monthlyCompletionVolumeStr = service.monthlyCompletionVolumeForChart(formattedDate, deptId);
        String yearPlannedQuantityStr = service.yearPlannedQuantityForChart(formattedDate, deptId);
        String yearCompletionVolumeStr = service.yearCompletionVolumeForChart(formattedDate, deptId);

        // 转换为 BigDecimal（如果为空则默认为 0）
        BigDecimal monthlyPlannedQuantity = StringUtils.isEmpty(monthlyPlannedQuantityStr) ? BigDecimal.ZERO : new BigDecimal(monthlyPlannedQuantityStr);
        BigDecimal monthlyCompletionVolume = StringUtils.isEmpty(monthlyCompletionVolumeStr) ? BigDecimal.ZERO : new BigDecimal(monthlyCompletionVolumeStr);
        BigDecimal yearPlannedQuantity = StringUtils.isEmpty(yearPlannedQuantityStr) ? BigDecimal.ZERO : new BigDecimal(yearPlannedQuantityStr);
        BigDecimal yearCompletionVolume = StringUtils.isEmpty(yearCompletionVolumeStr) ? BigDecimal.ZERO : new BigDecimal(yearCompletionVolumeStr);

        // 进行加法操作
        yearCompletionVolume = yearCompletionVolume.add(monthlyCompletionVolume);

        // 构建响应数据
        Map<String, Object> response = new LinkedHashMap<>();

        // dimensions
        List<String> dimensions = Arrays.asList("product", "计划量", "完成量");
        response.put("dimensions", dimensions);

        // source
        List<Map<String, Object>> source = new ArrayList<>();

        // 当月数据
        Map<String, Object> monthlyData = new LinkedHashMap<>();
        monthlyData.put("product", "当月");
        monthlyData.put("计划量", monthlyPlannedQuantity);
        monthlyData.put("完成量", monthlyCompletionVolume);
        source.add(monthlyData);

        // 当年数据
        Map<String, Object> yearlyData = new LinkedHashMap<>();
        yearlyData.put("product", "当年");
        yearlyData.put("计划量", yearPlannedQuantity);
        yearlyData.put("完成量", yearCompletionVolume);
        source.add(yearlyData);

        response.put("source", source);

        // 返回结果
        return success(response);
    }

    @GetMapping("/listTunnellingMonthPlanSub/{id}")
    public AjaxResult listSub(@PathVariable("id") String id) {
        return success(getPageInfo(() -> subDao.listDeep(MPJWrappers.lambdaJoin(TunnellingMonthPlanSub.class).eq(TunnellingMonthPlanSub::getParentId, id))));
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, TunnellingMonthPlan tunnellingMonthPlan) {
        new ExcelUtil<>(TunnellingMonthPlan.class).exportExcel( dao.list(tunnellingMonthPlan), "矿区月度掘进作业计划数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @GetMapping(value = "/sub/{id}")
    public AjaxResult getInfoSub(@PathVariable("id") String id) {
        return success(subDao.getByIdDeep(Long.valueOf(id)));
    }

    @GetMapping(value = "/{accountYear}/{accountMonth}/{deptId}")
    public AjaxResult getInfo(@PathVariable("accountYear") String accountYear, @PathVariable("accountMonth") String accountMonth, @PathVariable("deptId") String deptId) {
        return success(dao.getByYMD(Long.valueOf(accountYear), Long.valueOf(accountMonth), Long.valueOf(deptId)));
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody TunnellingMonthPlan tunnellingMonthPlan) {
        // 是否为空
        boolean b = service.DetermineWhetherTheYearAndMonthExist(tunnellingMonthPlan.getAccountYear(), tunnellingMonthPlan.getAccountMonth(), tunnellingMonthPlan.getDeptId(), tunnellingMonthPlan.getTunnellingMonthPlanSubList());
        if (b) {
            return dao.save(tunnellingMonthPlan) ? toAjax(tunnellingMonthPlan.getId()) : toAjax(false);
        } else {
            return AjaxResult.warn("已存在" + tunnellingMonthPlan.getAccountYear() + "年" + tunnellingMonthPlan.getAccountMonth() + "的掘进计划");
        }
    }

    @PostMapping("/isItRepeated")
    public AjaxResult isItRepeated(@RequestBody List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList) {
        return service.isItRepeated(tunnellingMonthPlanSubList) ? success() : warn("该月已存在");
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody TunnellingMonthPlan tunnellingMonthPlan) {
        return dao.updateById(tunnellingMonthPlan) ? toAjax(tunnellingMonthPlan.getId()) : toAjax(false);
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        // 判断的状态
        boolean b = service.DateApply(ids);
        if (!b) {
            return warn("该月已提交，请勿重复提交");
        }
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交矿区月度掘进作业计划", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(service.submitApply(Long.valueOf(id)));
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcelol")
    public void exportExcelol(HttpServletResponse response, TunnellingMonthPlan tunnellingMonthPlanSubList) {
        if (tunnellingMonthPlanSubList.getId() == null) {
            new ExcelUtil<>(TunnellingMonthPlanSub.class).importTemplateExcel(ServletUtils.getResponse(), "矿区月度掘进作业计划数据");
        } else {
            TunnellingMonthPlan tunnellingMonthPlan = dao.list(tunnellingMonthPlanSubList).get(0);
            TunnellingMonthPlanSub tunnellingMonthPlanSub = new TunnellingMonthPlanSub();
            tunnellingMonthPlanSub.setParentId(tunnellingMonthPlan.getId());
            new ExcelUtil<>(TunnellingMonthPlanSub.class).exportExcel(response, getPageInfo(() -> subDao.list(tunnellingMonthPlanSub)).getList(), "矿区月度掘进作业计划数据");
        }
    }

    @Log(title = "矿区月度掘进作业计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<TunnellingMonthPlanSub> util = new ExcelUtil<>(TunnellingMonthPlanSub.class);
        List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList = null;
        String message = null;
        try {
            tunnellingMonthPlanSubList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }
        String operName = getUsername();
        if (message == null) {
            message = service.importUpdateMineOuputMonthPlanSub(tunnellingMonthPlanSubList, operName);
        }
        return success(message);
    }

    @PostMapping("/isTunnellingMonthPlan")
    public AjaxResult isTunnellingMonthPlan(@RequestBody TunnellingMonthPlan tunnellingMonthPlanSubList) {
        return service.isTunnellingMonthPlan(tunnellingMonthPlanSubList) ? success() : warn("该月已存在");
    }
}

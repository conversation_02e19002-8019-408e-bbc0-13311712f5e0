package com.ruoyi.productionplan.month.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productionplan.month.dao.IMineOuputMonthPlanDao;
import com.ruoyi.productionplan.month.dao.IMineOuputMonthPlanSubDao;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlan;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;
import com.ruoyi.productionplan.month.service.IMineOuputMonthPlanService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;

@RestController
@RequestMapping("/workflow/mineOuputMonthPlan")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MineOuputMonthPlanController extends BaseController implements IBaseController {

    private final IMineOuputMonthPlanDao dao;
    private final IMineOuputMonthPlanSubDao iMineOuputMonthPlanSubDao;
    private final IMineOuputMonthPlanService iMineOuputMonthPlanService;

    @GetMapping
    public AjaxResult list(MineOuputMonthPlan entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    /**
     * 当日采矿计划
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/dailyPlanForChart")
    public AjaxResult dailyPlanForChart(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        String dailyPlanStr = iMineOuputMonthPlanService.dailyPlanForChart(formattedDate, deptId);
        BigDecimal dailyPlan = StringUtils.isEmpty(dailyPlanStr) ? BigDecimal.ZERO : new BigDecimal(dailyPlanStr);
        // 四舍五入保留两位小数
        return success(dailyPlan.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
    }

    /**
     * 当日采矿数量
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/dailyQuantityForChart")
    public AjaxResult dailyQuantityForChart(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        String dailyQuantityStr = iMineOuputMonthPlanService.dailyQuantityForChart(formattedDate, deptId);
        BigDecimal dailyQuantity = StringUtils.isEmpty(dailyQuantityStr) ? BigDecimal.ZERO : new BigDecimal(dailyQuantityStr);
        // 四舍五入保留两位小数
        return success(dailyQuantity.setScale(2, BigDecimal.ROUND_HALF_UP).toString());
    }


    /**
     * 采矿计划完成率
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/completionRate")
    public AjaxResult completionRate(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        // 当日掘进数量
        String dailyQuantityStr = iMineOuputMonthPlanService.dailyQuantityForChart(formattedDate, deptId);
        BigDecimal dailyQuantity = StringUtils.isEmpty(dailyQuantityStr) ? BigDecimal.ZERO : new BigDecimal(dailyQuantityStr);

        // 当日掘进计划
        String dailyPlanStr = iMineOuputMonthPlanService.dailyPlanForChart(formattedDate, deptId);
        BigDecimal dailyPlan = StringUtils.isEmpty(dailyPlanStr) ? BigDecimal.ZERO : new BigDecimal(dailyPlanStr);

        // 相除操作
        BigDecimal result;
        if (dailyPlan.compareTo(BigDecimal.ZERO) == 0) {
            result = BigDecimal.ZERO; // 避免除以零的情况
        } else {
            result = dailyQuantity.divide(dailyPlan, 2, BigDecimal.ROUND_HALF_UP);
        }
        return success(result.toString());
    }

    /**
     * 统计表
     *
     * @param dispatchDate 时间
     * @param deptId       部门
     * @return 数量
     */
    @GetMapping("/statisticalTable")
    public AjaxResult statisticalTable(@RequestParam(required = false) String dispatchDate, @RequestParam(required = false) String deptId) {
        // 因为前端语法日期选择器传后台的是毫秒数需要毫秒转换为时间戳
        // 格式化日期
        Instant instant = Instant.ofEpochMilli(Long.parseLong(dispatchDate));
        LocalDateTime dateTime = instant.atZone(ZoneId.systemDefault()).toLocalDateTime(); // 转换为LocalDateTime对象
        // 定义新的日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

        // 格式化日期
        String formattedDate = dateTime.format(formatter);
        // 获取各部分数据
        String monthlyPlannedQuantityStr = iMineOuputMonthPlanService.monthlyPlannedQuantityForChart(formattedDate, deptId);
        String monthlyCompletionVolumeStr = iMineOuputMonthPlanService.monthlyCompletionVolumeForChart(formattedDate, deptId);
        String yearPlannedQuantityStr = iMineOuputMonthPlanService.yearPlannedQuantityForChart(formattedDate, deptId);
        String yearCompletionVolumeStr = iMineOuputMonthPlanService.yearCompletionVolumeForChart(formattedDate, deptId);

        // 转换为 BigDecimal（如果为空则默认为 0）
        BigDecimal monthlyPlannedQuantity = StringUtils.isEmpty(monthlyPlannedQuantityStr) ? BigDecimal.ZERO : new BigDecimal(monthlyPlannedQuantityStr);
        BigDecimal monthlyCompletionVolume = StringUtils.isEmpty(monthlyCompletionVolumeStr) ? BigDecimal.ZERO : new BigDecimal(monthlyCompletionVolumeStr);
        BigDecimal yearPlannedQuantity = StringUtils.isEmpty(yearPlannedQuantityStr) ? BigDecimal.ZERO : new BigDecimal(yearPlannedQuantityStr);
        BigDecimal yearCompletionVolume = StringUtils.isEmpty(yearCompletionVolumeStr) ? BigDecimal.ZERO : new BigDecimal(yearCompletionVolumeStr);

        yearCompletionVolume = yearCompletionVolume.add(monthlyCompletionVolume);
        // 构建响应数据
        Map<String, Object> response = new LinkedHashMap<>();

        // dimensions
        List<String> dimensions = Arrays.asList("product", "计划量", "完成量");
        response.put("dimensions", dimensions);

        // source
        List<Map<String, Object>> source = new ArrayList<>();

        // 当月数据
        Map<String, Object> monthlyData = new LinkedHashMap<>();
        monthlyData.put("product", "当月");
        monthlyData.put("计划量", monthlyPlannedQuantity);
        monthlyData.put("完成量", monthlyCompletionVolume);
        source.add(monthlyData);

        // 当年数据
        Map<String, Object> yearlyData = new LinkedHashMap<>();
        yearlyData.put("product", "当年");
        yearlyData.put("计划量", yearPlannedQuantity);
        yearlyData.put("完成量", yearCompletionVolume);
        source.add(yearlyData);

        response.put("source", source);

        // 返回结果
        return success(response);
    }

    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MineOuputMonthPlan entity) {
        new ExcelUtil<>(MineOuputMonthPlan.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "矿区月度采出矿计划数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @GetMapping(value = "/mining/{accountYear}/{accountMonth}/{deptId}")
    public AjaxResult getInfo(@PathVariable("accountYear") String accountYear, @PathVariable("accountMonth") String accountMonth, @PathVariable("deptId") String deptId) {
        return success(dao.getByYMD(Long.valueOf(accountYear), Long.valueOf(accountMonth), Long.valueOf(deptId)));
    }

    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MineOuputMonthPlan entity) {
        if (iMineOuputMonthPlanService.IsItRepeated(entity.getAccountYear(), entity.getAccountMonth(), entity.getDeptId())) {
            return iMineOuputMonthPlanService.save(entity) ? toAjax(entity.getId()) : toAjax(false);
        } else {
            return AjaxResult.warn("已存在" + entity.getAccountYear() + "月的采出计划");
        }

    }

    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MineOuputMonthPlan entity) {
        return iMineOuputMonthPlanService.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "矿区月度采出矿计划审批通过后修改", businessType = BusinessType.UPDATE)
    @PutMapping("/approveAfterEdit")
    public AjaxResult approveAfterEdit(@RequestBody MineOuputMonthPlan entity) {
        return iMineOuputMonthPlanService.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        boolean b = iMineOuputMonthPlanService.DateApply(ids);
        if (!b) {
            return warn("该月已提交，请勿重复提交");
        }
        return toAjax(iMineOuputMonthPlanService.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交矿区月度采出矿计划", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(iMineOuputMonthPlanService.submitApply(Long.valueOf(id)));
    }

    /**
     * 导出 用于“使用excel更新数据”功能的 excel 模板
     *
     * @param entity 矿区月度采出矿计划
     */
    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcelSub")
    public void exportExcelSub(MineOuputMonthPlan entity) {
        if (entity.getId() == null) {
            new ExcelUtil<>(MineOuputMonthPlanSub.class).importTemplateExcel(ServletUtils.getResponse(), "矿区月度采出矿计划数据");
        } else {
            MineOuputMonthPlanSub mineOuputMonthPlanSub = new MineOuputMonthPlanSub();
            mineOuputMonthPlanSub.setParentId(entity.getId());
            new ExcelUtil<>(MineOuputMonthPlanSub.class).exportExcel(ServletUtils.getResponse(),  iMineOuputMonthPlanSubDao.list(mineOuputMonthPlanSub), "矿区月度采出矿计划数据");
        }
    }

    /**
     * 导入更新矿区月度采出矿计划子表
     *
     * @param file
     * @return
     */
    @Log(title = "矿区月度采出矿计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<MineOuputMonthPlanSub> util = new ExcelUtil<>(MineOuputMonthPlanSub.class);
        List<MineOuputMonthPlanSub> mineOuputMonthPlanSubList = null;
        String message = null;
        try {
            mineOuputMonthPlanSubList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }
        String operName = getUsername();
        if (message == null) {
            message = iMineOuputMonthPlanService.importUpdateMineOuputMonthPlanSub(mineOuputMonthPlanSubList, operName);
        }
        return success(message);
    }

}

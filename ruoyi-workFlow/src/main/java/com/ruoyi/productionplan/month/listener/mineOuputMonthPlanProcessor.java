package com.ruoyi.productionplan.month.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.util.bean.BeanValidators;
import com.ruoyi.productionplan.month.dao.IMineOuputMonthPlanDao;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlan;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;
import com.ruoyi.productionplan.month.validation.Update;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class mineOuputMonthPlanProcessor implements TaskListener {
    private final IMineOuputMonthPlanDao iMineOuputMonthPlanDao;
    private final Validator validator;

    @Override
    public void notify(DelegateTask delegateTask) {
        log.info("执行了监听器 ReportBackEndProcessor");

        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        // 获取子表数据
        String subDataString = delegateTask.getVariable("subData").toString();

        // 获取通过状态
        String BlueRedFlag = delegateTask.getVariable("pass").toString();
        // 获取子表数据
        JSONArray mineOuputMonthPlanSubListArray = JSONArray.parseArray(subDataString);

        // 判断是否通过
        if ("true".equals(BlueRedFlag)) {
            //  循环处理 JSONObject
            List<MineOuputMonthPlanSub> mineOuputMonthPlanSubList = new ArrayList<>();

            // 遍历 JSONArray
            for (int i = 0; i < mineOuputMonthPlanSubListArray.size(); i++) {
                JSONObject jsonObject = mineOuputMonthPlanSubListArray.getJSONObject(i);
                MineOuputMonthPlanSub sub = new MineOuputMonthPlanSub().setId(jsonObject.getLong("id")).setParentId(formData.getLong("id")).setYearPlanSubId(jsonObject.getLong("yearPlanSubId")).setWellheadId(jsonObject.getLong("wellheadId")).setWorkAreaId(jsonObject.getLong("workAreaId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setMiningMethodsValue(jsonObject.getString("miningMethodsValue")).setUnitThroughput(jsonObject.getLong("unitThroughput")).setUnitNumber(jsonObject.getLong("unitNumber")).setOreConsumption(jsonObject.getBigDecimal("oreConsumption")).setGeologicalGrade(jsonObject.getBigDecimal("geologicalGrade")).setGeologicalMetallicity(jsonObject.getBigDecimal("geologicalMetallicity")).setFirstLoss(jsonObject.getBigDecimal("firstLoss")).setFirstDilution(jsonObject.getBigDecimal("firstDilution")).setMiningGrade(jsonObject.getBigDecimal("miningGrade")).setMiningCapacity(jsonObject.getBigDecimal("miningCapacity")).setMiningMetallicity(jsonObject.getBigDecimal("miningMetallicity")).setSecondLoss(jsonObject.getBigDecimal("secondLoss")).setSecondDilution(jsonObject.getBigDecimal("secondDilution")).setOreOutputGrade(jsonObject.getBigDecimal("oreOutputGrade")).setOreOutputCapacity(jsonObject.getBigDecimal("oreOutputCapacity")).setOreOutputMetallicity(jsonObject.getBigDecimal("oreOutputMetallicity")).setTailingsFillingCapacity(jsonObject.getLong("tailingsFillingCapacity")).setWasteStoneFillingCapacity(jsonObject.getLong("wasteStoneFillingCapacity")).setFillingTerm(jsonObject.getLong("fillingTerm")).setInvestmentUnitPrice(jsonObject.getBigDecimal("investmentUnitPrice")).setInvestmentAmount(jsonObject.getBigDecimal("investmentAmount"));
                BeanValidators.validateWithException(validator, sub, Update.class);
                mineOuputMonthPlanSubList.add(sub);
            }
            MineOuputMonthPlan mineOuputMonthPlan = new MineOuputMonthPlan();
            mineOuputMonthPlan.setId(formData.getLong("id"));
            mineOuputMonthPlan.setMineOuputMonthPlanSubList(mineOuputMonthPlanSubList);
            iMineOuputMonthPlanDao.saveOrUpdateSub(mineOuputMonthPlan);
            log.info("通过");
        } else {
            log.error("不通过");
        }
    }
}

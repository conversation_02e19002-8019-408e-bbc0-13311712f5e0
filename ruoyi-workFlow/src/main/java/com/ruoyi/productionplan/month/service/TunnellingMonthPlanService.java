package com.ruoyi.productionplan.month.service;

import com.ruoyi.productionplan.month.domain.TunnellingMonthPlan;
import com.ruoyi.productionplan.month.domain.TunnellingMonthPlanSub;

import java.io.Serializable;
import java.util.List;

public interface TunnellingMonthPlanService {


    // 判断年 和月 是否存在
    boolean DetermineWhetherTheYearAndMonthExist(Long accountYear, Long accountMonth, Long depId, List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList);

    // 导入
    String importUpdateMineOuputMonthPlanSub(List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList, String operName);

    // 判断是否重复
    boolean isItRepeated(List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList);

    // 判断是否是月度计划
    boolean isTunnellingMonthPlan(TunnellingMonthPlan tunnellingMonthPlanSubList);

    // 删除判断
    boolean DateApply(String[] ids);

    boolean submitApply(Serializable id);

    // 当日掘进计划
    String dailyPlanForChart(String dispatchDate, String deptId);

    // 当日掘进量
    String dailyQuantityForChart(String dispatchDate, String deptId);

    // 月度计划量
    String monthlyPlannedQuantityForChart(String dispatchDate, String deptId);

    // 月度完成量
    String monthlyCompletionVolumeForChart(String dispatchDate, String deptId);

    // 年度计划量
    String yearPlannedQuantityForChart(String dispatchDate, String deptId);

    // 年度完成量
    String yearCompletionVolumeForChart(String dispatchDate, String deptId);
}

package com.ruoyi.productionplan.month.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.productionplan.month.dao.ISupportMonthPlanDao;
import com.ruoyi.productionplan.month.dao.ISupportMonthPlanSubDao;
import com.ruoyi.productionplan.month.domain.SupportMonthPlanSub;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class supportMonthPlanProcessor implements TaskListener {
    private final ISupportMonthPlanDao iSupportMonthPlanDao;
    private final ISupportMonthPlanSubDao iSupportMonthPlanSubDao;
    private final Validator validator;

    @Override
    public void notify(DelegateTask delegateTask) {
        System.out.println("执行了监听器 ReportBackEndProcessor");
        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        // 获取通过状态
        String BlueRedFlag = delegateTask.getVariable("pass").toString();
        // 获取修改后的子表数据
        String subDateString = delegateTask.getVariable("subData").toString();
        JSONArray subDateArray = JSONArray.parseArray(subDateString);
        if ("true".equals(BlueRedFlag)) {
            List<SupportMonthPlanSub> supportMonthPlanSubs = parseSubData(formData, subDateArray);
            handleSubData(supportMonthPlanSubs, formData.getLong("id"));
            updateSubData(supportMonthPlanSubs, formData.getLong("id"));
        } else {
            System.out.println("不通过");
        }
    }

    private void updateSubData(List<SupportMonthPlanSub> supportMonthPlanSubs, Long id) {

        List<SupportMonthPlanSub> tunnellingMonthPlanSubs = iSupportMonthPlanSubDao.lambdaQuery().eq(SupportMonthPlanSub::getParentId, id).list();

        Set<Long> tunnellingMonthPlanSubIds = tunnellingMonthPlanSubs.stream().map(SupportMonthPlanSub::getId).collect(Collectors.toSet());

        Set<Long> existingIds = supportMonthPlanSubs.stream().map(SupportMonthPlanSub::getId).collect(Collectors.toSet());

        Set<Long> newIds = tunnellingMonthPlanSubIds.stream().filter(ids -> !existingIds.contains(ids)).collect(Collectors.toSet());

        for (Long ide : newIds) {
            boolean deleted = iSupportMonthPlanSubDao.removeById(ide);
            if (!deleted) {
                throw new RuntimeException("删除子表数据失败");
            }
        }

        boolean allUpdated = supportMonthPlanSubs.stream().allMatch(iSupportMonthPlanSubDao::updateById);
        if (!allUpdated) {
            throw new RuntimeException("部分或全部子表数据修改失败");
        }
    }

    private void handleSubData(List<SupportMonthPlanSub> supportMonthPlanSubs, Long id) {
        for (SupportMonthPlanSub su : supportMonthPlanSubs) {
            if (su.getId() == null) {
                if (su.getWellheadId() == null || su.getParagraphId() == null || su.getProjectItemId() == null || su.getSupportTypeValue().isEmpty()) {
                    throw new RuntimeException("中段、井口、工程名称,支护类别不能为空");
                } else {
                    System.out.println("当前对象没有 id");
                    List<SupportMonthPlanSub> tunnellingMonthPlanSubs = iSupportMonthPlanSubDao.lambdaQuery().eq(SupportMonthPlanSub::getWellheadId, su.getWellheadId()).eq(SupportMonthPlanSub::getParagraphId, su.getParagraphId()).eq(SupportMonthPlanSub::getProjectItemId, su.getProjectItemId()).eq(SupportMonthPlanSub::getSupportTypeValue, su.getSupportTypeValue()).list();
                    if (tunnellingMonthPlanSubs.size() > 0) {
                        throw new RuntimeException("中段、井口、工程名称已存在");
                    }
                    su.setParentId(id);
                    boolean flag = iSupportMonthPlanSubDao.save(su);
                    if (!flag) {
                        throw new RuntimeException("新增子表数据失败");
                    }
                }
            }
        }
    }

    private List<SupportMonthPlanSub> parseSubData(JSONObject formData, JSONArray subDateArray) {
        List<SupportMonthPlanSub> supportMonthPlanSubs = new ArrayList<>();
        for (int i = 0; i < subDateArray.size(); i++) {
            JSONObject jsonObject = subDateArray.getJSONObject(i);
            SupportMonthPlanSub supportMonthPlanSub = new SupportMonthPlanSub().setWorkAreaId(jsonObject.getLong("workAreaId")).setId(jsonObject.getLong("id")).setParentId(formData.getLong("id")).setWellheadId(jsonObject.getLong("wellheadId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setSupportTypeValue(jsonObject.getString("supportTypeValue")).setSpecifications(jsonObject.getString("specifications")).setUnitValue(jsonObject.getString("unitValue")).setPlanVolume(jsonObject.getLong("planVolume")).setSupportMethodValue(jsonObject.getString("supportMethodValue")).setBeltNumber(jsonObject.getLong("beltNumber")).setNetArea(jsonObject.getLong("netArea")).setConcreteVolume(jsonObject.getLong("concreteVolume")).setUsteelNumber(jsonObject.getLong("usteelNumber")).setBoltCost(jsonObject.getBigDecimal("boltCost")).setBeltCost(jsonObject.getBigDecimal("beltCost")).setNetCost(jsonObject.getBigDecimal("netCost")).setConcreteCost(jsonObject.getBigDecimal("concreteCost")).setUsteelCost(jsonObject.getBigDecimal("usteelCost")).setTotalSupportCost(jsonObject.getBigDecimal("totalSupportCost"));
            supportMonthPlanSubs.add(supportMonthPlanSub);
        }
        return supportMonthPlanSubs;
    }
}

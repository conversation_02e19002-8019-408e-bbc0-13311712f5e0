package com.ruoyi.productionplan.month.dao;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;
import com.ruoyi.productionplan.month.domain.SupportMonthPlan;
import com.ruoyi.productionplan.month.domain.SupportMonthPlanSub;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlan;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface ISupportMonthPlanDao extends IActDao<SupportMonthPlan> {
    ISupportMonthPlanSubDao iSubDao();

    /**
     * 保存矿区月度支护计划子表信息
     * 传参时若不携带子表update会造成子表全部删除的情况，若仅update主表请用基于baseMapper中的方法
     */
    @DSTransactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateSub(SupportMonthPlan entity) {
        return entity.getSupportMonthPlanSubList().stream()
                .peek(x -> x.setParentId(entity.getId()))
                .allMatch(x -> Objects.nonNull(x.getId()) && Objects.nonNull(iSubDao().getById(x.getId())) ? iSubDao().updateById(x) : iSubDao().save(x))
                &&
                iSubDao().removeById(
                        iSubDao().lambdaQuery()
                                .eq(SupportMonthPlanSub::getParentId, entity.getId())
                                .list().stream()
                                .map(SupportMonthPlanSub::getId)
                                .filter(x -> !
                                        entity.getSupportMonthPlanSubList().stream()
                                                .map(SupportMonthPlanSub::getId)
                                                .collect(Collectors.toList())
                                                .contains(x)
                                ).toArray(Long[]::new)
                );
    }

    /**
     * 删除关联 id 等于主表 id 的子表
     *
     * @param id
     */
    @DSTransactional(rollbackFor = Exception.class)
    default boolean removeSubByParentId(Serializable... id) {
        return iSubDao().removeById(
                Arrays.stream(id)
                        .map(x ->
                                iSubDao().lambdaQuery()
                                        .eq(SupportMonthPlanSub::getParentId, x)
                                        .list()
                        ).flatMap(List::stream)
                        .map(SupportMonthPlanSub::getId)
                        .toArray(Long[]::new)
        );
    }

    /**
     * 新增矿区月度支护计划
     * saveOrUpdate(Batch)基于该方法，请谨慎修改（尤其在主子表场景下）
     *
     * @param entity 矿区月度支护计划
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean save(SupportMonthPlan entity) {
        boolean rows = IActDao.super.save(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    /**
     * 修改矿区月度支护计划
     * saveOrUpdate(Batch)基于该方法，请谨慎修改（尤其在主子表场景下）
     *
     * @param entity 矿区月度支护计划
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    default boolean updateById(SupportMonthPlan entity) {
        boolean rows = IActDao.super.updateById(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    /**
     * 批量删除矿区月度支护计划
     *
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean removeById(Serializable[] id) {
        boolean rows = IActDao.super.removeById(id);
        if (rows) {
            removeSubByParentId(id);
        }
        return rows;
    }

    /* 上述代码是处理子表的，若存在多个子表请仿照该部分代码编码其他子表；记得在insert update if条件里增加 */
    @Override
    default String processKey() {
        return "supportMonthPlan";
    }
    @DataScope(deptAlias = "d")
    @Override
    default List<SupportMonthPlan> list(SupportMonthPlan entity){
        return IActDao.super.list(entity);
    }

    List<SupportMonthPlanSub> excavationAddExtractionPlan(SupportMonthPlan entity);
}

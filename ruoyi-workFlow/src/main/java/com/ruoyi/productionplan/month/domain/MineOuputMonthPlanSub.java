package com.ruoyi.productionplan.month.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
import com.ruoyi.archives.paragraph.domain.Paragraph;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.productionplan.month.validation.Import;
import com.ruoyi.productionplan.month.validation.Save;
import com.ruoyi.productionplan.month.validation.Update;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import javax.validation.constraints.*;
import java.math.BigDecimal;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_mine_ouput_month_plan_sub")
public class MineOuputMonthPlanSub extends BaseEntity<MineOuputMonthPlanSub> {

    /**
     * id
     */
    @NotNull(message = "id不能为空", groups = {Import.class})
    @Excel(name = "id", width = 6, sort = 0, align = HorizontalAlignment.RIGHT)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主表id
     */
    @NotNull(message = "主表id不能为空", groups = {Import.class})
    @Excel(name = "主表id", width = 6, sort = 1, align = HorizontalAlignment.RIGHT)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 矿区年度采出矿计划子表id
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long yearPlanSubId;

    /**
     * 工区id
     */
    @NotNull(message = "工区不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workAreaId;


    /**
     * 提升井口
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long improveWellheadId;

    /**
     * 提升量
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal improveCapactity;
    /**
     * 工区
     */
    @Excel(name = "工区名称", width = 12, sort = 2)
    @TableField(exist = false)
    @FieldMapping(tag = BizWorkArea.class, thisField = Fields.workAreaId, select = BizWorkArea.Fields.name)
    private String workAreaName;

    /**
     * 井口id
     */
    @NotNull(message = "井口不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;

    /**
     * 井口
     */
    @Excel(name = "井口名称", width = 12, sort = 3)
    @FieldMapping(tag = BizWellhead.class, thisField = Fields.wellheadId, select = BizWellhead.Fields.name)
    @TableField(exist = false)
    private String wellheadName;

    /**
     * 中段id
     */
    @NotNull(message = "中段不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;

    /**
     * 中段
     */
    @Excel(name = "中段名称", width = 12, sort = 4)
    @FieldMapping(tag = Paragraph.class, thisField = Fields.paragraphId, select = Paragraph.Fields.name)
    @TableField(exist = false)
    private String paragraphName;

    /**
     * 工程名称id
     */
    @NotNull(message = "工程名称不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectItemId;

    /**
     * 工程名称
     */
    @Excel(name = "工程名称", sort = 5, width = 40)
    @FieldMapping(tag = ProjectItem.class, thisField = Fields.projectItemId, select = ProjectItem.Fields.name)
    @TableField(exist = false)
    private String projectItemName;

    /**
     * 增加采场描述，自己输入，比如某采场 1层、2层 by guanzpjune 2025.04.02
     */
    @Excel(name = "采场描述",sort = 6, width = 30)
    private String stopeDescribe;

    /**
     * 采矿方法
     */
    @NotNull(message = "采矿方法不能为空", groups = {Save.class, Update.class})
    @Excel(name = "采矿方法", sort = 7, dictType = "mining_methods")
    private String miningMethodsValue;

    /**
     * 台效（m/班）
     */
    @Max(value = 2147483647L, message = "请输入正确的台效数据")
    @Min(value = 0L, message = "请输入正确的台效数据")
    @Excel(name = "台效（m/班）", sort = 8,
            align = HorizontalAlignment.RIGHT, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long unitThroughput;

    /**
     * 台班
     */
    @Max(value = 2147483647L, message = "请输入正确的台班数据")
    @Min(value = 0L, message = "请输入正确的台班数据")
    @Excel(name = "台班（个）", sort = 9,
            align = HorizontalAlignment.RIGHT, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long unitNumber;

    /**
     * 地质储量消耗矿量（t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的地质储量消耗矿量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的地质储量消耗矿量数据")
    @Excel(name = "地质储量消耗矿量（t）", sort = 10, align = HorizontalAlignment.RIGHT, width = 20,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oreConsumption;

    /**
     * 地质储量品位（g/t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的地质储量品位数据")
    @DecimalMin(value = "0.00", message = "请输入正确的地质储量品位数据")
    @Excel(name = "地质储量品位（g/t）", sort = 11, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal geologicalGrade;

    /**
     * 地质储量金属量（kg）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的地质储量金属量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的地质储量金属量数据")
    @Excel(name = "地质储量金属量（kg）", sort = 12, align = HorizontalAlignment.RIGHT, width = 20,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal geologicalMetallicity;

    /**
     * 一次损失率（%）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的一次损失率数据")
    @DecimalMin(value = "0.00", message = "请输入正确的一次损失率数据")
    @Excel(name = "一次损失率（%）", sort = 13, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal firstLoss;

    /**
     * 一次贫化率（%）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的一次贫化率数据")
    @DecimalMin(value = "0.00", message = "请输入正确的一次贫化率数据")
    @Excel(name = "一次贫化率（%）", sort = 14, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal firstDilution;

    /**
     * 采矿品位（g/t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的采矿品位数据")
    @DecimalMin(value = "0.00", message = "请输入正确的采矿品位数据")
    @Excel(name = "采矿品位（g/t）", sort = 15, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal miningGrade;

    /**
     * 采矿落矿量（t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的采矿落矿量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的采矿落矿量数据")
    @Excel(name = "采矿落矿量（t）", sort = 16, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal miningCapacity;

    /**
     * 采矿金属量（kg）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的采矿金属量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的采矿金属量数据")
    @Excel(name = "采矿金属量（kg）", sort = 17, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal miningMetallicity;

    /**
     * 二次损失率（%）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的二次损失率数据")
    @DecimalMin(value = "0.00", message = "请输入正确的二次损失率数据")
    @Excel(name = "二次损失率（%）", sort = 18, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal secondLoss;

    /**
     * 二次贫化率（%）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的二次贫化率数据")
    @DecimalMin(value = "0.00", message = "请输入正确的二次贫化率数据")
    @Excel(name = "二次贫化率（%）", sort = 19, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal secondDilution;

    /**
     * 出矿品位（g/t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的出矿品位数据")
    @DecimalMin(value = "0.00", message = "请输入正确的出矿品位数据")
    @Excel(name = "出矿品位（g/t）", sort = 20, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oreOutputGrade;

    /**
     * 出矿量（t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的出矿量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的出矿量数据")
    @Excel(name = "出矿量（t）", sort = 21, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oreOutputCapacity;

    /**
     * 出矿金属量（kg）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的出矿金属量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的出矿金属量数据")
    @Excel(name = "出矿金属量（kg）", sort = 22, align = HorizontalAlignment.RIGHT, width = 18,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal oreOutputMetallicity;

    /**
     * 尾砂充填量（m3）
     */
    @Max(value = 2147483647L, message = "请输入正确的尾砂充填量数据")
    @Min(value = 0L, message = "请输入正确的尾砂充填量数据")
    @Excel(name = "尾砂充填量（m3）", sort = 23, width = 18,
            align = HorizontalAlignment.RIGHT, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsFillingCapacity;

    /**
     * 废石充填量（m3）
     */
    @Max(value = 2147483647L, message = "请输入正确的废石充填量数据")
    @Min(value = 0L, message = "请输入正确的废石充填量数据")
    @Excel(name = "废石充填量（m3）", sort = 24, width = 18,
            align = HorizontalAlignment.RIGHT, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wasteStoneFillingCapacity;

    /**
     * 充填时间
     */
    @Max(value = 2147483647L, message = "请输入正确的充填时间数据")
    @Min(value = 0L, message = "请输入正确的充填时间数据")
    @Excel(name = "充填时间", sort = 25,
            align = HorizontalAlignment.RIGHT, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long fillingTerm;

    /**
     * 投资单价（元/t）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的投资单价数据")
    @DecimalMin(value = "0.00", message = "请输入正确的投资单价数据")
    @Excel(name = "投资单价（元/t）", sort = 26, align = HorizontalAlignment.RIGHT,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal investmentUnitPrice;

    /**
     * 投资额（万元）
     */
    @DecimalMax(value = "999999999999.9999", message = "请输入正确的投资额数据")
    @DecimalMin(value = "0.00", message = "请输入正确的投资额数据")
    @Excel(name = "投资额（万元）", sort = 27, align = HorizontalAlignment.RIGHT,
            roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal investmentAmount;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;



    @Excel(name = "施工时间")
    private String constructionTime;

    @Excel(name = "钻机编号")
    private String rigNo;


    @Excel(name = "是否倒运")
    private String yesornoValue;

    @Excel(name = "运输路线")
    private String transportRoute;



    private String drawingFile;


}

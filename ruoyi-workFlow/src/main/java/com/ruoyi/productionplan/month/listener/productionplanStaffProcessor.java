package com.ruoyi.productionplan.month.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.productionplan.month.dao.ITunnellingMonthPlanDao;
import com.ruoyi.productionplan.month.dao.ITunnellingMonthPlanSubDao;
import com.ruoyi.productionplan.month.domain.TunnellingMonthPlan;
import com.ruoyi.productionplan.month.domain.TunnellingMonthPlanSub;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class productionplanStaffProcessor implements TaskListener {
    private final ITunnellingMonthPlanSubDao tunnellingMonthPlanSubDao;
    private final ITunnellingMonthPlanDao tunnellingMonthPlanDao;
    private final Validator validator;

    @Override
    public void notify(DelegateTask delegateTask) {
        System.out.println("执行了监听器 ReportBackEndProcessor");

        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        // 获取通过状态
        String BlueRedFlag = delegateTask.getVariable("pass").toString();
        // 获取修改后的子表数据
        String subDateString = delegateTask.getVariable("subData").toString();
        // 获取修改后的子表数据
        JSONArray subDateArray = JSONArray.parseArray(subDateString);
        // 判断是否通过
        if ("true".equals(BlueRedFlag)) {
            List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList = parseSubData(subDateArray, formData);
            handleSubData(tunnellingMonthPlanSubList, formData);
            updateSubData(tunnellingMonthPlanSubList, formData);
            System.out.println("通过");
        } else {
            List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList = parseSubData(subDateArray, formData);
            handleSubData(tunnellingMonthPlanSubList, formData);
            updateMainTableStatus(tunnellingMonthPlanSubList);
            System.out.println("结束");
        }
    }

    private List<TunnellingMonthPlanSub> parseSubData(JSONArray subDateArray, JSONObject formData) {
        List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList = new ArrayList<>();
        for (int i = 0; i < subDateArray.size(); i++) {
            JSONObject jsonObject = subDateArray.getJSONObject(i);
            TunnellingMonthPlanSub sub = new TunnellingMonthPlanSub().setId(jsonObject.getLong("id")).setWorkAreaId(jsonObject.getLong("workAreaId")).setParentId(formData.getLong("id")).setWellheadId(jsonObject.getLong("wellheadId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setUnitNumber(jsonObject.getLong("unitNumber")).setUnitThroughput(jsonObject.getLong("unitThroughput")).setSpecifications(jsonObject.getString("specifications")).setCrossSectional(jsonObject.getString("crossSectional")).setLength(jsonObject.getLong("length")).setVolume(jsonObject.getLong("volume")).setExcavationVolume(jsonObject.getLong("excavationVolume")).setByproductCapacity(jsonObject.getBigDecimal("byproductCapacity")).setByproductGrade(jsonObject.getBigDecimal("byproductGrade")).setByproductMetallicity(jsonObject.getBigDecimal("byproductMetallicity"));
            tunnellingMonthPlanSubList.add(sub);
        }
        return tunnellingMonthPlanSubList;
    }

    private void handleSubData(List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList, JSONObject formData) {
        for (TunnellingMonthPlanSub su : tunnellingMonthPlanSubList) {
            if (su.getId() == null) {
                if (su.getWellheadId() == null || su.getParagraphId() == null || su.getProjectItemId() == null) {
                    throw new RuntimeException("中段、井口、工程名称不能为空");
                } else {
                    List<TunnellingMonthPlanSub> tunnellingMonthPlanSubs = tunnellingMonthPlanSubDao.lambdaQuery().eq(TunnellingMonthPlanSub::getWellheadId, su.getWellheadId()).eq(TunnellingMonthPlanSub::getParagraphId, su.getParagraphId()).eq(TunnellingMonthPlanSub::getProjectItemId, su.getProjectItemId()).list();
                    if (tunnellingMonthPlanSubs.size() > 0) {
                        throw new RuntimeException("中段、井口、工程名称已存在");
                    }
                    System.out.println("当前对象没有 id");
                    su.setParentId(formData.getLong("id"));
                    boolean flag = tunnellingMonthPlanSubDao.save(su);
                    if (!flag) {
                        throw new RuntimeException("新增子表数据失败");
                    }
                }
            }
        }
    }

    private void updateSubData(List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList, JSONObject formData) {
        List<TunnellingMonthPlanSub> tunnellingMonthPlanSubs = tunnellingMonthPlanSubDao.lambdaQuery().eq(TunnellingMonthPlanSub::getParentId, formData.getLong("id")).list();

        Set<Long> tunnellingMonthPlanSubIds = tunnellingMonthPlanSubs.stream().map(TunnellingMonthPlanSub::getId).collect(Collectors.toSet());

        Set<Long> existingIds = tunnellingMonthPlanSubList.stream().map(TunnellingMonthPlanSub::getId).collect(Collectors.toSet());

        Set<Long> newIds = tunnellingMonthPlanSubIds.stream().filter(id -> !existingIds.contains(id)).collect(Collectors.toSet());

        for (Long id : newIds) {
            boolean deleted = tunnellingMonthPlanSubDao.removeById(id);
            if (!deleted) {
                throw new RuntimeException("删除子表数据失败");
            }
        }

        boolean allUpdated = tunnellingMonthPlanSubList.stream().allMatch(sub -> tunnellingMonthPlanSubDao.updateById(sub));
        if (!allUpdated) {
            throw new RuntimeException("部分或全部子表数据修改失败");
        }
    }

    private void updateMainTableStatus(List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList) {
        Set<Long> parentIds = tunnellingMonthPlanSubList.stream().map(TunnellingMonthPlanSub::getParentId).collect(Collectors.toSet());

        for (Long parentId : parentIds) {
            LambdaQueryWrapper<TunnellingMonthPlan> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TunnellingMonthPlan::getId, parentId);
            TunnellingMonthPlan plan = new TunnellingMonthPlan();
            plan.setId(parentId);
            plan.setStatus("0");
            boolean rows = tunnellingMonthPlanDao.update(plan, queryWrapper);
            if (!rows) {
                throw new RuntimeException("主表数据修改失败，ID: " + parentId);
            }
        }
    }

}

package com.ruoyi.productionplan.month.dao.impl;

import com.ruoyi.activiti.dao.impl.ActDaoImpl;
import com.ruoyi.productionplan.month.dao.ISupportMonthPlanDao;
import com.ruoyi.productionplan.month.dao.ISupportMonthPlanSubDao;
import com.ruoyi.productionplan.month.domain.SupportMonthPlan;
import com.ruoyi.productionplan.month.domain.SupportMonthPlanSub;
import com.ruoyi.productionplan.month.mapper.SupportMonthPlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SupportMonthPlanDaoImpl extends ActDaoImpl<SupportMonthPlanMapper, SupportMonthPlan> implements ISupportMonthPlanDao {
    private final SupportMonthPlanMapper mapper;
    private final ISupportMonthPlanSubDao iSubDao;

    @Override
    public ISupportMonthPlanSubDao iSubDao() {
        return iSubDao;
    }

    public String processKey() {
        return "supportMonthPlan";
    }

    @Override
    public List<SupportMonthPlanSub> excavationAddExtractionPlan(SupportMonthPlan entity) {
        return mapper.excavationAddExtractionPlan(entity);
    }
}

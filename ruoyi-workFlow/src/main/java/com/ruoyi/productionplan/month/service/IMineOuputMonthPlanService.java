package com.ruoyi.productionplan.month.service;

import com.ruoyi.productionplan.month.domain.MineOuputMonthPlan;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2024-12-02 14:41
 */
public interface IMineOuputMonthPlanService {
    /**
     * 矿区月度采出矿计划
     *
     * @param mineOuputMonthPlanSubList 金精矿化验结果列表
     * @param operName                  操作用户
     * @return 结果
     */
    String importUpdateMineOuputMonthPlanSub(List<MineOuputMonthPlanSub> mineOuputMonthPlanSubList, String operName);

    /**
     * 新增矿区月度采出矿计划
     *
     * @param entity 矿区月度采出矿计划
     * @return 结果
     */
    boolean save(MineOuputMonthPlan entity);

    /**
     * 修改矿区月度采出矿计划
     *
     * @param entity 矿区月度采出矿计划
     * @return 结果
     */
    boolean updateById(MineOuputMonthPlan entity);

    /**
     * 删除矿区月度采出矿计划
     *
     * @param id 需要删除的目标
     * @return 结果
     */
    boolean removeById(Long[] id);

    boolean IsItRepeated(Long accountYear, Long accountMonth, Long deptId);

    boolean DateApply(String[] ids);

    boolean submitApply(Serializable id);
    // 当日采矿计划
    String dailyPlanForChart(String dispatchDate, String deptId);
    // 当日采矿计划
    String dailyQuantityForChart(String dispatchDate, String deptId);
    // 月度计划量
    String monthlyPlannedQuantityForChart(String dispatchDate, String deptId);

    // 月度完成量
    String monthlyCompletionVolumeForChart(String dispatchDate, String deptId);

    // 年度计划量
    String yearPlannedQuantityForChart(String dispatchDate, String deptId);

    // 年度完成量
    String yearCompletionVolumeForChart(String dispatchDate, String deptId);
}

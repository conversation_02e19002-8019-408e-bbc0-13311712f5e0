package com.ruoyi.productionplan.month.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_tunnelling_month_plan")
public class TunnellingMonthPlan extends ProcessEntity<TunnellingMonthPlan> {
    /**
     * 部门id
     */
    @Excel(name = "部门id")
    @NotNull(message = "部门不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /**
     * 计划年度
     */
    @Excel(name = "计划年度")
    @NotNull(message = "计划年度不能为空")
    @Min(value = 2024, message = "请检查计划年度数据")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountYear;

    /**
     * 计划年度
     */
    @Excel(name = "计划年度")
    @NotNull(message = "计划月度不能为空")
    @Max(value = 12, message = "请检查计划月度数据")
    @Min(value = 1, message = "请检查计划月度数据")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountMonth;

    /**
     * 矿区月度掘进计划子表信息 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = TunnellingMonthPlanSub.Fields.parentId)
    private List<TunnellingMonthPlanSub> tunnellingMonthPlanSubList = new ArrayList<>();

    @FieldMapping(tag = SysUser.class, thisField = BaseEntity.Fields.createBy, joinField = SysUser.Fields.userName, select = SysUser.Fields.nickName)
    @TableField(exist = false)
    private String nickName;


    @FieldMapping(tag = SysDept.class, thisField = TunnellingMonthPlan.Fields.deptId, joinField = SysDept.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;
}

package com.ruoyi.productionplan.yearPlan.service.impl;

import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.enums.BizplanTypeValue;
import com.ruoyi.productionplan.yearPlan.dao.IYearPlanDao;
import com.ruoyi.productionplan.yearPlan.domain.YearPlan;
import com.ruoyi.productionplan.yearPlan.service.YearPlanService;
import com.ruoyi.system.dao.ISysDeptDao;
import lombok.RequiredArgsConstructor;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.time.Year;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class YearPlanServiceImpl implements YearPlanService {

    private final IYearPlanDao yearPlanDao;
    private final ISysDeptDao sysDeptDao;

    /*
     * @Description: 判断部门是否已经存在计划和时间
     */
    @Override
    @Cacheable(value = "yearPlanCache", key = "{#deptId, #accountYear}")
    public boolean isExist(Long deptId, Long accountYear) {
        // 根据部门id和计划类型值查询计划
        YearPlan yearPlan = yearPlanDao.lambdaQuery().eq(YearPlan::getDeptId, deptId).eq(YearPlan::getAccountYear, accountYear).one();
        // 如果查询结果为空，则部门不存在计划，返回true
        if (yearPlan == null) {
            return true;
        }
       throw new RuntimeException("部门已经存在计划，请勿重复添加");
    }


    /**
     * 根据计划类型值查询部门计划
     *
     * @param planTypeValue 计划类型值
     * @return list
     */

    @Override
    @Cacheable(value = "yearPlanCache", key = "{#planTypeValue,#deptId}")
    public boolean getSelectPlanType(String planTypeValue, Long deptId) {
        SysDept sysDept = sysDeptDao.lambdaQuery().eq(SysDept::getDeptId, deptId).one();

        if (sysDept == null) {
            return false;
        }
        //判断部门是否是金洲公司，金洲公司返回true
        if (BizplanTypeValue.JINZHOUCOMPANYSANNUALPRODUCTIONTARGETPLAN.getCode().equals(planTypeValue)) {
            if (isInDepartmentList(sysDept, new int[]{100})) {
                return true;
            } else {
                throw new RuntimeException("划类型选择金洲公司年度生产指标计划时，部门只能选金洲公司");
            }
            // 判断部门是否是矿区或选矿车间，返回true
        } else if (BizplanTypeValue.ANNUALPRODUCTIONTARGETPLANFORMINING.getCode().equals(planTypeValue)) {
            if (isInDepartmentList(sysDept, new int[]{327, 334, 324})) {
                return true;
            } else {
                throw new RuntimeException("计划类型选择矿区年度生产指标计划，部门只能选择金青顶矿区、英格庄矿区、宋家庄矿区。");
            }
            //判断部门是否是选矿车间，返回true
        } else if (BizplanTypeValue.ANNURLPRODUCTIONTARGETPLANFORMINERALPROCESSINGWORKSHOP.getCode().equals(planTypeValue)) {
            if (isInDepartmentList(sysDept, new int[]{327, 335})) {
                return true;
            } else {
                throw new RuntimeException("计划类型选择选矿车间年度生产指标计划时，部门只能选择金青顶选厂、英格庄选厂。");
            }
        }
        return false;
    }

    /**
     * 判断部门是否在部门列表中
     */
    private boolean isInDepartmentList(SysDept sysDept, int[] departmentIds) {
        List<Long> longDepartmentIds = Arrays.stream(departmentIds).mapToLong(id -> id).boxed().collect(Collectors.toList());
        List<SysDept> list = sysDeptDao.lambdaQuery().in(SysDept::getDeptId, longDepartmentIds).list();
        boolean flag = list.stream().anyMatch(item -> item.getDeptId().equals(sysDept.getDeptId()));
        return flag;
    }


}

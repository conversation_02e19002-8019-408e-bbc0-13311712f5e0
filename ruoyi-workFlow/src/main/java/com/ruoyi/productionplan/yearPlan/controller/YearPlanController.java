package com.ruoyi.productionplan.yearPlan.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productionplan.yearPlan.dao.IYearPlanDao;
import com.ruoyi.productionplan.yearPlan.domain.YearPlan;
import com.ruoyi.productionplan.yearPlan.service.YearPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;

@RestController
@RequestMapping("/workflow/yearPlan")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class YearPlanController extends BaseController implements IBaseController {

    private final IYearPlanDao yearPlanDao;
    private final YearPlanService yearPlanService;


    @GetMapping
    public AjaxResult list(YearPlan yearPlan) {
        return success(getPageInfo(() -> yearPlanDao.list(yearPlan)));
    }

    @Log(title = "年度生产指标计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, YearPlan yearPlan) {
        new ExcelUtil<>(YearPlan.class).exportExcel(response, getPageInfo(() -> yearPlanDao.list(yearPlan)).getList(), "年度生产指标计划数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(yearPlanDao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "年度生产指标计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody YearPlan yearPlan) {
        boolean isExist = yearPlanService.isExist(yearPlan.getDeptId(), yearPlan.getAccountYear());
        if (isExist) {
            boolean selectPlanType = yearPlanService.getSelectPlanType(yearPlan.getPlanTypeValue(), yearPlan.getDeptId());
            if (selectPlanType) {
                return yearPlanDao.save(yearPlan) ? toAjax(yearPlan.getId()) : toAjax(false);
            }
        } else {
            throw new RuntimeException("该年度该部门已存在该计划类型");
        }
        throw new RuntimeException("没有填写数据！");
    }

    @Log(title = "年度生产指标计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody YearPlan yearPlan) {
        return yearPlanDao.updateById(yearPlan) ? toAjax(yearPlan.getId()) : toAjax(false);
    }

    @Log(title = "年度生产指标计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(yearPlanDao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @GetMapping(value = "/getInternalPlanningData")
    public AjaxResult getInternalPlanningData(YearPlan yearPlan) {
        return success(yearPlanDao.getInternalPlanningData(yearPlan));
    }

}

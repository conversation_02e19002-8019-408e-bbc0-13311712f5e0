package com.ruoyi.productionplan.yearPlan.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;


@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_year_plan")
public class YearPlan extends BaseEntity<YearPlan> {
    /**
     * 部门id
     */
    @Excel(name = "部门id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /**
     * 计划年度
     */
    @Excel(name = "计划年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountYear;

    @FieldMapping(tag = SysUser.class, thisField = BaseEntity.Fields.createBy, joinField = SysUser.Fields.userName, select = SysUser.Fields.userName)
    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private String uName;
    /**
     * 计划类型
     */
    @Excel(name = "计划类型")
    private String planTypeValue;

    /**
     * 采掘总量(吨)
     */
    @Excel(name = "采掘总量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long miningExcavationVolume;

    /**
     * 采矿量(吨)
     */
    @Excel(name = "采矿量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long miningVolume;

    /**
     * 掘进量(吨)
     */
    @Excel(name = "掘进量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long excavationVolume;

    /**
     * 总掘进量(米)
     */
    @Excel(name = "总掘进量(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long excavationMeters;

    /**
     * 地质探矿(米)
     */
    @Excel(name = "地质探矿(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long geologicalProspectingMeters;

    /**
     * 生产探矿(米)
     */
    @Excel(name = "生产探矿(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long productiveProspectingMeters;

    /**
     * 开拓(技措)(米)
     */
    @Excel(name = "开拓(技措)(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long developingTechnicalMeters;

    /**
     * 坑内钻(米)
     */
    @Excel(name = "坑内钻(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long pitDrillMeters;

    /**
     * 地表钻(米)
     */
    @Excel(name = "地表钻(米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long surfaceDrillingMeters;

    /**
     * 出矿量合计(吨)
     */
    @Excel(name = "出矿量合计(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long totalOreOutput;

    /**
     * 采场出矿量(吨)
     */
    @Excel(name = "采场出矿量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stopeOreOutput;

    /**
     * 副产矿量(吨)
     */
    @Excel(name = "副产矿量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long byproductOreOutput;

    /**
     * 采场充填量合计(立方米)
     */
    @Excel(name = "采场充填量合计(立方米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stopeTotalFillingCapacity;

    /**
     * 尾砂充填量(立方米)
     */
    @Excel(name = "尾砂充填量(立方米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsFillingCapacity;

    /**
     * 废石回填量(立方米)
     */
    @Excel(name = "废石回填量(立方米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wasteStoneFillingCapacity;

    /**
     * 空区充填量(立方米)
     */
    @Excel(name = "空区充填量(立方米)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emptySpaceFillingCapacity;

    /**
     * 产尾砂量(吨)
     */
    @Excel(name = "产尾砂量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsCapacity;

    @Excel(name = "采准切割工程(米) ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accurateCuttingMeters;


    @Excel(name = "采准切割工程(米) ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accurateCuttingMeters2;
    /**
     * 尾砂外排量(吨)
     */
    @Excel(name = "尾砂外排量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsDischargeCapacity;

    /**
     * 尾砂治理空区量(吨)
     */
    @Excel(name = "尾砂治理空区量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsEmptySpaceFillingCapacity;

    /**
     * 选矿处理矿量合计(吨)
     */
    @Excel(name = "选矿处理矿量合计(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oreProcessingCapacity;

    /**
     * 浮选处理量(吨)
     */
    @Excel(name = "浮选处理量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long flotationProcessingCapacity;

    /**
     * 直磨处理量(吨)
     */
    @Excel(name = "直磨处理量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long directProcessingCapacity;

    /**
     * 入选品位(克/吨)
     */
    @Excel(name = "入选品位(克/吨)")
    private BigDecimal gradeOfCrudeOre;

    /**
     * 原矿金属量(千克)
     */
    @Excel(name = "原矿金属量(千克)")
    private BigDecimal crudeOreMetallicity;

    /**
     * 选矿回收率(%)
     */
    @Excel(name = "选矿回收率(%)")
    private BigDecimal oreDressingRecoveryRate;

    /**
     * 精矿返金率(%)
     */
    @Excel(name = "精矿返金率(%)")
    private BigDecimal concentrateGoldReturnRate;

    /**
     * 黄金产量(千克)
     */
    @Excel(name = "黄金产量(千克)")
    private BigDecimal goldProduction;

    /**
     * 采掘总量(吨)(内部)
     */
    @Excel(name = "采掘总量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long miningExcavationVolume2;

    /**
     * 采矿量(吨)(内部)
     */
    @Excel(name = "采矿量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long miningVolume2;

    /**
     * 掘进量(吨)(内部)
     */
    @Excel(name = "掘进量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long excavationVolume2;

    /**
     * 总掘进量(米)(内部)
     */
    @Excel(name = "总掘进量(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long excavationMeters2;

    /**
     * 地质探矿(米)(内部)
     */
    @Excel(name = "地质探矿(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long geologicalProspectingMeters2;

    /**
     * 生产探矿(米)(内部)
     */
    @Excel(name = "生产探矿(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long productiveProspectingMeters2;

    /**
     * 开拓(技措)(米)(内部)
     */
    @Excel(name = "开拓(技措)(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long developingTechnicalMeters2;

    /**
     * 坑内钻(米)(内部)
     */
    @Excel(name = "坑内钻(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long pitDrillMeters2;

    /**
     * 地表钻(米)(内部)
     */
    @Excel(name = "地表钻(米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long surfaceDrillingMeters2;

    /**
     * 出矿量合计(吨)(内部)
     */
    @Excel(name = "出矿量合计(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long totalOreOutput2;

    /**
     * 采场出矿量(吨)(内部)
     */
    @Excel(name = "采场出矿量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stopeOreOutput2;

    /**
     * 副产矿量(吨)(内部)
     */
    @Excel(name = "副产矿量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long byproductOreOutput2;

    /**
     * 采场充填量合计(立方米)(内部)
     */
    @Excel(name = "采场充填量合计(立方米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long stopeTotalFillingCapacity2;

    /**
     * 尾砂充填量(立方米)(内部)
     */
    @Excel(name = "尾砂充填量(立方米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsFillingCapacity2;

    /**
     * 废石回填量(立方米)(内部)
     */
    @Excel(name = "废石回填量(立方米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wasteStoneFillingCapacity2;

    /**
     * 空区充填量(立方米)(内部)
     */
    @Excel(name = "空区充填量(立方米)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long emptySpaceFillingCapacity2;

    /**
     * 产尾砂量(吨)
     */
    @Excel(name = "产尾砂量(吨)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsCapacity2;

    /**
     * 尾砂外排量(吨)(内部)
     */
    @Excel(name = "尾砂外排量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsDischargeCapacity2;

    /**
     * 尾砂治理空区量(吨)(内部)
     */
    @Excel(name = "尾砂治理空区量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long tailingsEmptySpaceFillingCapacity2;

    /**
     * 选矿处理矿量合计(吨)(内部)
     */
    @Excel(name = "选矿处理矿量合计(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long oreProcessingCapacity2;

    /**
     * 浮选处理量(吨)(内部)
     */
    @Excel(name = "浮选处理量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long flotationProcessingCapacity2;

    /**
     * 直磨处理量(吨)(内部)
     */
    @Excel(name = "直磨处理量(吨)(内部)")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long directProcessingCapacity2;

    /**
     * 入选品位(克/吨)(内部)
     */
    @Excel(name = "入选品位(克/吨)(内部)")
    private BigDecimal gradeOfCrudeOre2;

    /**
     * 原矿金属量(千克)(内部)
     */
    @Excel(name = "原矿金属量(千克)(内部)")
    private BigDecimal crudeOreMetallicity2;

    /**
     * 选矿回收率(%)(内部)
     */
    @Excel(name = "选矿回收率(%)(内部)")
    private BigDecimal oreDressingRecoveryRate2;

    /**
     * 精矿返金率(%)(内部)
     */
    @Excel(name = "精矿返金率(%)(内部)")
    private BigDecimal concentrateGoldReturnRate2;

    /**
     * 黄金产量(千克)(内部)
     */
    @Excel(name = "黄金产量(千克)(内部)")
    private BigDecimal goldProduction2;


    @FieldMapping(tag = SysDept.class, thisField = YearPlan.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;


    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
}

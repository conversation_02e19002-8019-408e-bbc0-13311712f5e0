package com.ruoyi.productionplan.year.dao.impl;

import com.ruoyi.productionplan.year.dao.ISupportYearPlanDao;
import com.ruoyi.productionplan.year.dao.ISupportYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.SupportYearPlan;
import com.ruoyi.productionplan.year.mapper.SupportYearPlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import com.ruoyi.activiti.dao.impl.ActDaoImpl;
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SupportYearPlanDaoImpl extends ActDaoImpl<SupportYearPlanMapper, SupportYearPlan> implements ISupportYearPlanDao {
        private final ISupportYearPlanSubDao iSubDao;

        @Override
        public ISupportYearPlanSubDao iSubDao () {
        return iSubDao;
    }
    @Override
    public String processKey() {
        return "supportYearPlan";
    }
}

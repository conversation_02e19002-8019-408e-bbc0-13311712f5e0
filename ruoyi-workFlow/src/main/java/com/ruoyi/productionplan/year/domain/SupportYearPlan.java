package com.ruoyi.productionplan.year.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.ArrayList;
import com.alibaba.excel.annotation.*;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.productionplan.month.domain.SupportMonthPlan;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.activiti.domain.ProcessEntity;
import lombok.experimental.FieldNameConstants;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_support_year_plan")
public class SupportYearPlan extends ProcessEntity<SupportYearPlan> {
    @Excel(name = "部门id")
    @NotNull(message = "部门不能为空")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;
    /**
     * 计划年度
     */
    @Excel(name = "计划年度")
    @NotNull(message = "计划年度不能为空")
    @Min(value = 2024, message = "请检查计划年度数据")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountYear;

    /**
     * 矿区月度支护计划子表信息 列表查询不会查出来 若是一对一用 T 、一对多用 List<T>
     */
    @TableField(exist = false)
    @EntityMapping(joinField = SupportYearPlanSub.Fields.parentId)
    private List<SupportYearPlanSub> supportYearPlanSubList = new ArrayList<>();


    /**
     * 部门
     */
    @FieldMapping(tag = SysDept.class, thisField = SupportMonthPlan.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    @FieldMapping(tag = SysUser.class, thisField = BaseEntity.Fields.createBy, joinField = SysUser.Fields.userName, select = SysUser.Fields.nickName)
    @TableField(exist = false)
    private String nickName;


    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;
}

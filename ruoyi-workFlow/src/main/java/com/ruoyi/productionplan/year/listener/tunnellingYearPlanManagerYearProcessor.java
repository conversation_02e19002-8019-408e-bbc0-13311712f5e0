package com.ruoyi.productionplan.year.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.ruoyi.activiti.dao.IProcessService;
import com.ruoyi.activiti.mapper.ActTaskMapper;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.productionplan.year.dao.ITunnellingYearPlanDao;
import com.ruoyi.productionplan.year.dao.ITunnellingYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlan;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlanSub;
import com.ruoyi.system.dao.ISysPostDao;
import com.ruoyi.system.dao.ISysUserDao;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.SysUserPost;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.hibernate.service.spi.ServiceException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class tunnellingYearPlanManagerYearProcessor implements TaskListener {
    private final ITunnellingYearPlanSubDao tunnellingYearPlanSubDao;
    private final ITunnellingYearPlanDao tunnellingYearPlanDao;
    private final Validator validator;
    private final ISysUserDao iSysUserDao;
    private final ISysPostDao iSysPostDao;
    private final IProcessService iProcessService;
    private final ActTaskMapper actTaskMapper;

    @Override
    public void notify(DelegateTask delegateTask) {
        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        String instanceId = formData.getString("instanceId");
        // 获取通过状态
        String pass = delegateTask.getVariable("pass").toString();
        String type = delegateTask.getVariable("type").toString();
        // 通过SysUserPost 和SysUser 和 SysPost 关联查询出部门下的所有用户
        SysPost post = iSysPostDao.lambdaQuery().eq(SysPost::getPostCode, "fgld").one();
        List<SysUser> sysUserList = iSysUserDao.lambdaQuery().list();
        List<SysUserPost> sysUserPosts = iSysUserDao.SysUserPostList(post.getPostId());
        List<Long> notifyUserList = Lists.newArrayList();
        StringBuffer sysUserId = new StringBuffer();
        sysUserList.forEach(sysUser -> {
            sysUserPosts.forEach(sysUserPost -> {
                if (sysUserPost.getUserId().equals(sysUser.getUserId())) {
                    sysUserId.append(sysUser.getUserId()).append(",");
                    notifyUserList.add(sysUserPost.getUserId());
                }
            });
        });
        sysUserPosts.forEach(sysUserPost -> {
                    sysUserId.append(sysUserPost.getUserId()).append(",");
                }
        );
        String notify = sysUserId.substring(0, sysUserId.length() - 1);

        // 获取修改后的子表数据
        JSONArray subDateArray = JSONArray.parseArray(delegateTask.getVariable("subData").toString());

        // 判断是否通过
        if ("true".equals(pass) || type != null) {
            List<TunnellingYearPlanSub> supportYearPlanSub = parseSubData(formData, subDateArray);
            List<Long> parentIds = supportYearPlanSub.stream().map(TunnellingYearPlanSub::getParentId).distinct().collect(Collectors.toList());

            handleSubData(supportYearPlanSub, formData.getLong("id"));
            updateMainTableStatus(notify, parentIds);
            System.out.println("通过");


            //            抄送

            notifyUserList.forEach(userId -> actTaskMapper.insertBusinessNotify(instanceId, userId));

        } else {
            System.out.println("不通过");
        }
    }

    private List<TunnellingYearPlanSub> parseSubData(JSONObject formData, JSONArray subDateArray) {
        List<TunnellingYearPlanSub> supportYearPlanSub = new ArrayList<>();
        for (int i = 0; i < subDateArray.size(); i++) {
            JSONObject jsonObject = subDateArray.getJSONObject(i);
            TunnellingYearPlanSub sub = new TunnellingYearPlanSub().setId(jsonObject.getLong("id")).setParentId(formData.getLong("id")).setWorkAreaId(jsonObject.getLong("workAreaId")).setWellheadId(jsonObject.getLong("wellheadId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setUnitNumber(jsonObject.getLong("unitNumber")).setUnitThroughput(jsonObject.getLong("unitThroughput")).setSpecifications(jsonObject.getString("specifications")).setCrossSectional(jsonObject.getString("crossSectional")).setLength(jsonObject.getLong("length")).setVolume(jsonObject.getLong("volume")).setExcavationVolume(jsonObject.getLong("excavationVolume")).setByproductCapacity(jsonObject.getBigDecimal("byproductCapacity")).setByproductGrade(jsonObject.getBigDecimal("byproductGrade")).setByproductMetallicity(jsonObject.getBigDecimal("byproductMetallicity"));
            supportYearPlanSub.add(sub);
        }
        return supportYearPlanSub;
    }

    private void handleSubData(List<TunnellingYearPlanSub> supportYearPlanSub, Long parentId) {
        List<TunnellingYearPlanSub> existingSubs = tunnellingYearPlanSubDao.lambdaQuery().eq(TunnellingYearPlanSub::getParentId, parentId).list();

        Set<Long> existingIds = existingSubs.stream().map(TunnellingYearPlanSub::getId).collect(Collectors.toSet());

        for (TunnellingYearPlanSub sub : supportYearPlanSub) {
            if (sub.getId() == null) {
                validateSubData(sub);
                sub.setParentId(parentId);
                saveSubData(sub);
            } else if (!existingIds.contains(sub.getId())) {
                throw new ServiceException("子表数据 ID 不匹配");
            }
        }

        Set<Long> currentIds = supportYearPlanSub.stream().map(TunnellingYearPlanSub::getId).collect(Collectors.toSet());

        Set<Long> idsToDelete = existingIds.stream().filter(id -> !currentIds.contains(id)).collect(Collectors.toSet());

        deleteSubData(idsToDelete);
        updateSubData(supportYearPlanSub);
    }

    private void validateSubData(TunnellingYearPlanSub sub) {
        if (sub.getWellheadId() == null || sub.getParagraphId() == null || sub.getProjectItemId() == null) {
            throw new ServiceException("中段、井口、工程名称不能为空");
        }
    }

    private void saveSubData(TunnellingYearPlanSub sub) {
        List<TunnellingYearPlanSub> tunnellingMonthPlanSubs = tunnellingYearPlanSubDao.lambdaQuery().eq(TunnellingYearPlanSub::getWellheadId, sub.getWellheadId()).eq(TunnellingYearPlanSub::getParagraphId, sub.getParagraphId()).eq(TunnellingYearPlanSub::getProjectItemId, sub.getProjectItemId()).eq(TunnellingYearPlanSub::getParentId, sub.getParentId()).list();
        if (tunnellingMonthPlanSubs.size() > 0) {
            throw new ServiceException("中段、井口、工程名称已存在");
        }
        boolean flag = tunnellingYearPlanSubDao.save(sub);
        if (!flag) {
            throw new ServiceException("新增子表数据失败");
        }
    }

    private void deleteSubData(Set<Long> idsToDelete) {
        for (Long id : idsToDelete) {
            boolean deleted = tunnellingYearPlanSubDao.removeById(id);
            if (!deleted) {
                throw new ServiceException("删除子表数据失败");
            }
        }
    }

    private void updateSubData(List<TunnellingYearPlanSub> supportYearPlanSub) {
        boolean allUpdated = supportYearPlanSub.stream().allMatch(sub -> tunnellingYearPlanSubDao.updateById(sub));
        if (!allUpdated) {
            throw new ServiceException("部分或全部子表数据修改失败");
        }
    }

    private void updateMainTableStatus(String str3, List<Long> parentIds) {
        LambdaQueryWrapper<TunnellingYearPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TunnellingYearPlan::getId, parentIds);
        TunnellingYearPlan updatePlan = new TunnellingYearPlan();
        updatePlan.setNotify(str3);
        updatePlan.setStatus("6");
        boolean rows = tunnellingYearPlanDao.update(updatePlan, queryWrapper);
        if (!rows) {
            throw new ServiceException("修改主表状态失败，IDs: " + parentIds);
        }
    }

}

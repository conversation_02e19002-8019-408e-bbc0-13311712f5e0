package com.ruoyi.productionplan.year.dao;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.productionplan.year.domain.SupportYearPlan;
import com.ruoyi.productionplan.year.domain.SupportYearPlanSub;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlan;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface ISupportYearPlanDao extends IActDao<SupportYearPlan> {
    ISupportYearPlanSubDao iSubDao ();

    @DSTransactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateSub (SupportYearPlan entity) {
       return entity.getSupportYearPlanSubList().stream()
               .peek(x -> x.setParentId(entity.getId()))
               .allMatch(x -> Objects.nonNull(x.getId()) && Objects.nonNull(iSubDao().getById(x.getId())) ? iSubDao().updateById(x) : iSubDao().save(x))
               &&
               iSubDao().removeById(
                        iSubDao().lambdaQuery()
                             .eq(SupportYearPlanSub::getParentId, entity.getId())
                             .list().stream()
                                .map(SupportYearPlanSub::getId)
                                .filter(x ->
                                        !entity.getSupportYearPlanSubList().stream()
                                                .map(SupportYearPlanSub::getId)
                                                .collect(Collectors.toList())
                                                .contains(x)
                                ).toArray(Long[]::new)
               );
    }

    @DSTransactional(rollbackFor = Exception.class)
    default boolean removeSubByParentId(Serializable... id){
        return iSubDao().removeById(
                Arrays.stream(id)
                                .map(x ->
                                        iSubDao().lambdaQuery()
                                                .eq(SupportYearPlanSub::getParentId, x)
                                                .list()
                                ).flatMap(List::stream)
                                .map(SupportYearPlanSub::getId)
                                .toArray(Long[]::new)
        );
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean save (SupportYearPlan entity) {
        boolean rows = IActDao.super.save(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean updateById (SupportYearPlan entity) {
        boolean rows = IActDao.super.updateById(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean removeById (Serializable[] id) {
        boolean rows = IActDao.super.removeById(id);
        if (rows) {
            removeSubByParentId(id);
        }
        return rows;
    }

    @Override
    default String processKey () {
        return "supportYearPlan";
    }

    @DataScope(deptAlias = "d")
    @Override
    default List<SupportYearPlan> list(SupportYearPlan entity){
        return IActDao.super.list(entity);
    }
}

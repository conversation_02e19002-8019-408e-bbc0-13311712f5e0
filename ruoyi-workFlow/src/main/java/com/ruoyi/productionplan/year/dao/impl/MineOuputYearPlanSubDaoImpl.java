package com.ruoyi.productionplan.year.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.productionplan.year.dao.IMineOuputYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlanSub;
import com.ruoyi.productionplan.year.mapper.MineOuputYearPlanSubMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MineOuputYearPlanSubDaoImpl extends BaseDaoImpl<MineOuputYearPlanSubMapper, MineOuputYearPlanSub> implements IMineOuputYearPlanSubDao {
}

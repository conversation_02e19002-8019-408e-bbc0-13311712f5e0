package com.ruoyi.productionplan.year.dao.impl;

import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.ruoyi.activiti.dao.impl.ActDaoImpl;
import com.ruoyi.archives.projectItem.dao.ProjectItemDao;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.productionplan.year.dao.IMineOuputYearPlanDao;
import com.ruoyi.productionplan.year.dao.IMineOuputYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlan;
import com.ruoyi.productionplan.year.mapper.MineOuputYearPlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MineOuputYearPlanDaoImpl extends ActDaoImpl<MineOuputYearPlanMapper, MineOuputYearPlan> implements IMineOuputYearPlanDao {
    private final IMineOuputYearPlanSubDao iSubDao;
    private final ProjectItemDao projectItemDao;

    @Override
    public IMineOuputYearPlanSubDao iSubDao() {
        return iSubDao;
    }

    @Override
    public String processKey() {
        return "mineOuputYearPlan";
    }
}

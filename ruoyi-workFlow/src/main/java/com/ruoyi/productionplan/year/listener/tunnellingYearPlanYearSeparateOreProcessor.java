package com.ruoyi.productionplan.year.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.productionplan.year.dao.ITunnellingYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlanSub;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class tunnellingYearPlanYearSeparateOreProcessor implements TaskListener {
    private final ITunnellingYearPlanSubDao tunnellingYearPlanSubDao;
    private final Validator validator;

    @Override
    public void notify(DelegateTask delegateTask) {
        System.out.println("执行了监听器 ReportBackEndProcessor");

        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        // 获取通过状态
        String BlueRedFlag = delegateTask.getVariable("pass").toString();

        // 获取修改后的子表数据
        String subDateString = delegateTask.getVariable("subData").toString();
        JSONArray subDateArray = JSONArray.parseArray(subDateString);

        // 判断是否通过
        if ("true".equals(BlueRedFlag)) {
            List<TunnellingYearPlanSub> supportYearPlanSub = parseSubData(subDateArray, formData);
            handleSubData(supportYearPlanSub, formData);
            updateSubData(supportYearPlanSub, formData);
            System.out.println("通过");
        } else {
            System.out.println("不通过");
        }
    }

    private List<TunnellingYearPlanSub> parseSubData(JSONArray subDateArray, JSONObject formData) {
        List<TunnellingYearPlanSub> supportYearPlanSub = new ArrayList<>();
        for (int i = 0; i < subDateArray.size(); i++) {
            JSONObject jsonObject = subDateArray.getJSONObject(i);
            TunnellingYearPlanSub sub = new TunnellingYearPlanSub().setId(jsonObject.getLong("id")).setParentId(formData.getLong("id")).setWellheadId(jsonObject.getLong("wellheadId")).setWorkAreaId(jsonObject.getLong("workAreaId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setUnitNumber(jsonObject.getLong("unitNumber")).setUnitThroughput(jsonObject.getLong("unitThroughput")).setSpecifications(jsonObject.getString("specifications")).setCrossSectional(jsonObject.getString("crossSectional")).setLength(jsonObject.getLong("length")).setVolume(jsonObject.getLong("volume")).setExcavationVolume(jsonObject.getLong("excavationVolume")).setByproductCapacity(jsonObject.getBigDecimal("byproductCapacity")).setByproductGrade(jsonObject.getBigDecimal("byproductGrade")).setByproductMetallicity(jsonObject.getBigDecimal("byproductMetallicity"));
            supportYearPlanSub.add(sub);
        }
        return supportYearPlanSub;
    }

    private void handleSubData(List<TunnellingYearPlanSub> supportYearPlanSub, JSONObject formData) {
        for (TunnellingYearPlanSub su : supportYearPlanSub) {
            if (su.getId() == null) {
                if (su.getWellheadId() == null || su.getParagraphId() == null || su.getProjectItemId() == null) {
                    throw new ServiceException("中段、井口、工程名称不能为空");
                } else {
                    List<TunnellingYearPlanSub> tunnellingMonthPlanSubs = tunnellingYearPlanSubDao.lambdaQuery().eq(TunnellingYearPlanSub::getWellheadId, su.getWellheadId()).eq(TunnellingYearPlanSub::getParentId, su.getParentId()).eq(TunnellingYearPlanSub::getParagraphId, su.getParagraphId()).eq(TunnellingYearPlanSub::getProjectItemId, su.getProjectItemId()).list();
                    if (tunnellingMonthPlanSubs.size() > 0) {
                        throw new ServiceException("中段、井口、工程名称已存在");
                    }
                    System.out.println("当前对象没有 id");
                    su.setParentId(formData.getLong("id"));
                    boolean flag = tunnellingYearPlanSubDao.save(su);
                    if (!flag) {
                        throw new ServiceException("新增子表数据失败");
                    }
                }
            }
        }
    }

    private void updateSubData(List<TunnellingYearPlanSub> supportYearPlanSub, JSONObject formData) {
        List<TunnellingYearPlanSub> tunnellingMonthPlanSubs = tunnellingYearPlanSubDao.lambdaQuery().eq(TunnellingYearPlanSub::getParentId, formData.getLong("id")).list();

        Set<Long> tunnellingMonthPlanSubIds = tunnellingMonthPlanSubs.stream().map(TunnellingYearPlanSub::getId).collect(Collectors.toSet());

        Set<Long> existingIds = supportYearPlanSub.stream().map(TunnellingYearPlanSub::getId).collect(Collectors.toSet());

        Set<Long> newIds = tunnellingMonthPlanSubIds.stream().filter(id -> !existingIds.contains(id)).collect(Collectors.toSet());

        for (Long id : newIds) {
            boolean deleted = tunnellingYearPlanSubDao.removeById(id);
            if (!deleted) {
                throw new ServiceException("删除子表数据失败");
            }
        }

        boolean allUpdated = supportYearPlanSub.stream().allMatch(tunnellingYearPlanSubDao::updateById);
        if (!allUpdated) {
            throw new ServiceException("部分或全部子表数据修改失败");
        }
    }
}

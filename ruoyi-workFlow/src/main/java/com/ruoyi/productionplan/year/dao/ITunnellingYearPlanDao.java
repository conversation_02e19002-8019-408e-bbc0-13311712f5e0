package com.ruoyi.productionplan.year.dao;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlan;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlanSub;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.List;

public interface ITunnellingYearPlanDao extends IActDao<TunnellingYearPlan> {
    ITunnellingYearPlanSubDao iSubDao ();

    @DSTransactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateSub (TunnellingYearPlan entity) {
       return entity.getTunnellingYearPlanSubList().stream()
               .peek(x -> x.setParentId(entity.getId()))
               .allMatch(x -> Objects.nonNull(x.getId()) && Objects.nonNull(iSubDao().getById(x.getId())) ? iSubDao().updateById(x) : iSubDao().save(x))
               &&
               iSubDao().removeById(
                        iSubDao().lambdaQuery()
                             .eq(TunnellingYearPlanSub::getParentId, entity.getId())
                             .list().stream()
                                .map(TunnellingYearPlanSub::getId)
                                .filter(x ->
                                        !entity.getTunnellingYearPlanSubList().stream()
                                                .map(TunnellingYearPlanSub::getId)
                                                .collect(Collectors.toList())
                                                .contains(x)
                                ).toArray(Long[]::new)
               );
    }

    @DSTransactional(rollbackFor = Exception.class)
    default boolean removeSubByParentId(Serializable... id){
        return iSubDao().removeById(
                Arrays.stream(id)
                                .map(x ->
                                        iSubDao().lambdaQuery()
                                                .eq(TunnellingYearPlanSub::getParentId, x)
                                                .list()
                                ).flatMap(List::stream)
                                .map(TunnellingYearPlanSub::getId)
                                .toArray(Long[]::new)
        );
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean save (TunnellingYearPlan entity) {
        boolean rows = IActDao.super.save(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean updateById (TunnellingYearPlan entity) {
        boolean rows = IActDao.super.updateById(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean removeById (Serializable[] id) {
        boolean rows = IActDao.super.removeById(id);
        if (rows) {
            removeSubByParentId(id);
        }
        return rows;
    }

    @Override
    default String processKey () {
        return "tunnellingYearPlan";
    }

    @DataScope(deptAlias = "d")
    @Override
    default List<TunnellingYearPlan> list( TunnellingYearPlan entity){
        return IActDao.super.list(entity);
    }
}

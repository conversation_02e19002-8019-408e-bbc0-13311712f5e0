package com.ruoyi.productionplan.year.dao;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.common.annotation.DataScope;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlan;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlanSub;
import com.ruoyi.productionplan.year.domain.SupportYearPlan;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface IMineOuputYearPlanDao extends IActDao<MineOuputYearPlan> {
    IMineOuputYearPlanSubDao iSubDao();

    /**
     * 保存矿区年度采出矿计划子表信息
     * 传参时若不携带子表update会造成子表全部删除的情况，若仅update主表请用基于baseMapper中的方法
     */
    @DSTransactional(rollbackFor = Exception.class)
    default boolean saveOrUpdateSub(MineOuputYearPlan entity) {
        return entity.getMineOuputYearPlanSubList().stream()
                .peek(x -> x.setParentId(entity.getId()))
                .allMatch(x -> Objects.nonNull(x.getId()) && Objects.nonNull(iSubDao().getById(x.getId())) ? iSubDao().updateById(x) : iSubDao().save(x))
                &&
                iSubDao().removeById(
                        iSubDao().lambdaQuery()
                                .eq(MineOuputYearPlanSub::getParentId, entity.getId())
                                .list().stream()
                                .map(MineOuputYearPlanSub::getId)
                                .filter(x -> !
                                        entity.getMineOuputYearPlanSubList().stream()
                                                .map(MineOuputYearPlanSub::getId)
                                                .collect(Collectors.toList())
                                                .contains(x)
                                ).toArray(Long[]::new)
                );
    }

    /**
     * 删除关联 id 等于主表 id 的子表
     *
     * @param id
     */
    @DSTransactional(rollbackFor = Exception.class)
    default boolean removeSubByParentId(Serializable... id) {
        return iSubDao().removeById(
                Arrays.stream(id)
                        .map(x ->
                                iSubDao().lambdaQuery()
                                        .eq(MineOuputYearPlanSub::getParentId, x)
                                        .list()
                        ).flatMap(List::stream)
                        .map(MineOuputYearPlanSub::getId)
                        .toArray(Long[]::new)
        );
    }

    /**
     * 新增矿区年度采出矿计划
     * saveOrUpdate(Batch)基于该方法，请谨慎修改（尤其在主子表场景下）
     *
     * @param entity 矿区年度采出矿计划
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean save(MineOuputYearPlan entity) {
        boolean rows = IActDao.super.save(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    /**
     * 修改矿区年度采出矿计划
     * saveOrUpdate(Batch)基于该方法，请谨慎修改（尤其在主子表场景下）
     *
     * @param entity 矿区年度采出矿计划
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean updateById(MineOuputYearPlan entity) {
        boolean rows = IActDao.super.updateById(entity);
        if (rows) {
            saveOrUpdateSub(entity);
        }
        return rows;
    }

    /**
     * 批量删除矿区年度采出矿计划
     *
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    @Override
    default boolean removeById(Serializable[] id) {
        boolean rows = IActDao.super.removeById(id);
        if (rows) {
            removeSubByParentId(id);
        }
        return rows;
    }

    /* 上述代码是处理子表的，若存在多个子表请仿照该部分代码编码其他子表；记得在insert update if条件里增加 */
    @Override
    default String processKey() {
        return "mineOuputYearPlan";
    }

    @DataScope(deptAlias = "d")
    @Override
    default List<MineOuputYearPlan> list(MineOuputYearPlan entity){
        return IActDao.super.list(entity);
    }
}

package com.ruoyi.productionplan.year.dao.impl;

import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.productionplan.year.dao.ISupportYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.SupportYearPlanSub;
import com.ruoyi.productionplan.year.mapper.SupportYearPlanSubMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SupportYearPlanSubDaoImpl extends BaseDaoImpl<SupportYearPlanSubMapper, SupportYearPlanSub> implements ISupportYearPlanSubDao {
}

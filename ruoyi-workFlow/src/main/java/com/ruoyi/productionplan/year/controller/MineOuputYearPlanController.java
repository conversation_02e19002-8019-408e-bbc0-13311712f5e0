package com.ruoyi.productionplan.year.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productionplan.year.dao.IMineOuputYearPlanDao;
import com.ruoyi.productionplan.year.dao.IMineOuputYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlan;
import com.ruoyi.productionplan.year.domain.MineOuputYearPlanSub;
import com.ruoyi.productionplan.year.service.IMineOuputYearPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/mineOuputYearPlan")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MineOuputYearPlanController extends BaseController implements IBaseController {

    private final IMineOuputYearPlanDao dao;
    private final IMineOuputYearPlanSubDao iMineOuputYearPlanSubDao;
    private final IMineOuputYearPlanService iMineOuputYearPlanService;

    @GetMapping
    public AjaxResult list(MineOuputYearPlan entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MineOuputYearPlan entity) {
        new ExcelUtil<>(MineOuputYearPlan.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "矿区年度采出矿计划数据");
    }


    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MineOuputYearPlan entity) {
        // 验证是否重复
        if (iMineOuputYearPlanService.IsItRepeated(entity.getAccountYear(), entity.getDeptId())) {
            return iMineOuputYearPlanService.save(entity) ? toAjax(entity.getId()) : toAjax(false);
        } else {
            return AjaxResult.error("已存在" + entity.getAccountYear() + "年的掘进计划");
        }

    }

    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MineOuputYearPlan entity) {
        return iMineOuputYearPlanService.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "矿区年度采出矿计划审批通过后修改", businessType = BusinessType.UPDATE)
    @PutMapping("/approveAfterEdit")
    public AjaxResult approveAfterEdit(@RequestBody MineOuputYearPlan entity) {
        return iMineOuputYearPlanService.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        if (iMineOuputYearPlanService.DateApply(ids)) {
            return AjaxResult.error("该数据已提交审批，请勿重复删除");
        }
        return toAjax(iMineOuputYearPlanService.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交矿区年度采出矿计划", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
//        return toAjax(dao.submitApply(Long.valueOf(id)));
        return toAjax(iMineOuputYearPlanService.submitApply(Long.valueOf(id)));
    }

    /**
     * 导出 用于“使用excel更新数据”功能的 excel 模板
     *
     * @param entity 矿区年度采出矿计划
     */
    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcelSub")
    public void exportExcelSub(MineOuputYearPlan entity) {
        if (entity.getId() == null) {
            new ExcelUtil<>(MineOuputYearPlanSub.class)
                    .importTemplateExcel(ServletUtils.getResponse(), "矿区年度采出矿计划数据");
        } else {
            MineOuputYearPlanSub mineOuputYearPlanSub = new MineOuputYearPlanSub();
            mineOuputYearPlanSub.setParentId(entity.getId());
            new ExcelUtil<>(MineOuputYearPlanSub.class)
                    .exportExcel(ServletUtils.getResponse(), iMineOuputYearPlanSubDao.list(mineOuputYearPlanSub), "矿区年度采出矿计划数据");
        }
    }

    /**
     * 导入更新矿区年度采出矿计划子表
     *
     * @param file
     * @return
     */
    @Log(title = "矿区年度采出矿计划", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<MineOuputYearPlanSub> util = new ExcelUtil<>(MineOuputYearPlanSub.class);
        List<MineOuputYearPlanSub> mineOuputYearPlanSubList = null;
        String message = null;
        try {
            mineOuputYearPlanSubList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }
        String operName = getUsername();
        if (message == null) {
            message = iMineOuputYearPlanService.importUpdateMineOuputYearPlanSub(mineOuputYearPlanSubList, operName);
        }
        return success(message);
    }

}

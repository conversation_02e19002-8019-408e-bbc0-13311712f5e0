package com.ruoyi.productionplan.year.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
import com.ruoyi.archives.paragraph.domain.Paragraph;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.archives.workArea.domain.BizWorkArea;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.productionplan.month.validation.Import;
import com.ruoyi.productionplan.month.validation.Save;
import com.ruoyi.productionplan.month.validation.Update;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.FieldNameConstants;
import org.apache.poi.ss.usermodel.HorizontalAlignment;

import javax.validation.constraints.*;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName(value = "biz_tunnelling_year_plan_sub")
public class TunnellingYearPlanSub extends BaseEntity<TunnellingYearPlanSub> {

    @Excel(name = "id")
    @NotNull(message = "id不能为空", groups = {Import.class, Update.class})
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主表id
     */
    @Excel(name = "主表id")
    @NotNull(message = "主表id不能为空", groups = {Import.class, Update.class})
    @ExcelProperty(value = "主表id", index = 1)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;


    /**
     * 井口
     */
    @NotNull(message = "井口不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;


    @Excel(name = "井口")
    @FieldMapping(tag = BizWellhead.class, thisField = TunnellingYearPlanSub.Fields.wellheadId, select = BizWellhead.Fields.name)
    @TableField(exist = false)
    private String wellheadName;


    /**
     * 中段
     */
    @NotNull(message = "中段不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;

    @Excel(name = "中段")
    @FieldMapping(tag = Paragraph.class, thisField = TunnellingYearPlanSub.Fields.paragraphId, select = Paragraph.Fields.name)
    @TableField(exist = false)
    private String paragraphName;


    @NotNull(message = "工区不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workAreaId;


    @Excel(name = "工区")
    @FieldMapping(tag = BizWorkArea.class, thisField = TunnellingYearPlanSub.Fields.workAreaId, select = BizWorkArea.Fields.name)
    @TableField(exist = false)
    private String workAreaName;
    /**
     * 工程名称id
     */
    @NotNull(message = "工程名称不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectItemId;

    @Excel(name = "工程作业名称",width = 40,align = HorizontalAlignment.LEFT)
    @FieldMapping(tag = ProjectItem.class, thisField = TunnellingYearPlanSub.Fields.projectItemId, select = ProjectItem.Fields.name)
    @TableField(exist = false)
    private String projectItemName;

    /**
     * 台效（m/班）
     */
    @Max(value = 99999999999L, message = "请输入正确的台效数据")
    @Min(value = 0L, message = "请输入正确的台效数据")
    @Excel(name = "台效（m/班）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long unitThroughput;

    /**
     * 台班
     */
    @Max(value = 99999999999L, message = "请输入正确的台班数据")
    @Min(value = 0L, message = "请输入正确的台班数据")
    @Excel(name = "台班")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long unitNumber;

    /**
     * 规格（m×m）
     */
    @Excel(name = "规格（m×m）")
    private String specifications;

    /**
     * 断面积（m2）
     */
    @Excel(name = "断面积（m2）")
    private String crossSectional;

    /**
     * 长度（m）
     */
    @Max(value = 99999999999L, message = "请输入正确的长度数据")
    @Min(value = 0L, message = "请输入正确的长度数据")
    @Excel(name = "长度（m）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long length;

    /**
     * 体积（m³）
     */
    @Max(value = 99999999999L, message = "请输入正确的体积数据")
    @Min(value = 0L, message = "请输入正确的体积数据")
    @Excel(name = "体积（m³）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long volume;

    /**
     * 掘进量（t）
     */
    @Max(value = 99999999999L, message = "请输入正确的掘进量数据")
    @Min(value = 0L, message = "请输入正确的掘进量数据")
    @Excel(name = "掘进量（t）")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long excavationVolume;

    /**
     * 副产量（t）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的副产量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的副产量数据")
    @Excel(name = "副产量（t）")
    private BigDecimal byproductCapacity;

    /**
     * 副产品位（g/t）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的副产品位数据")
    @DecimalMin(value = "0.00", message = "请输入正确的副产品位数据")
    @Excel(name = "副产品位（g/t）")
    private BigDecimal byproductGrade;

    /**
     * 副产金属量（t）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的副产金属量数据")
    @DecimalMin(value = "0.00", message = "请输入正确的副产金属量数据")
    @Excel(name = "副产金属量（t）")
    private BigDecimal byproductMetallicity;

}

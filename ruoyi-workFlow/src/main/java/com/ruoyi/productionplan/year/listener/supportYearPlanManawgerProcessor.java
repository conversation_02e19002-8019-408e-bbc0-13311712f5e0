package com.ruoyi.productionplan.year.listener;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.ruoyi.activiti.dao.IProcessService;
import com.ruoyi.activiti.mapper.ActTaskMapper;
import com.ruoyi.common.domain.SysUser;
import com.ruoyi.productionplan.year.dao.ISupportYearPlanDao;
import com.ruoyi.productionplan.year.dao.ISupportYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.SupportYearPlan;
import com.ruoyi.productionplan.year.domain.SupportYearPlanSub;
import com.ruoyi.system.dao.ISysPostDao;
import com.ruoyi.system.dao.ISysUserDao;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.SysUserPost;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.hibernate.service.spi.ServiceException;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class supportYearPlanManawgerProcessor implements TaskListener {
    private final ISupportYearPlanDao iSupportYearPlanDao;
    private final ISupportYearPlanSubDao iSupportYearPlanSubDao;
    private final IProcessService iProcessService;
    private final ISysUserDao iSysUserDao;
    private final ISysPostDao iSysPostDao;
    private final ActTaskMapper actTaskMapper;


    @Override
    public void notify(DelegateTask delegateTask) {

        // 获取表单数据
        JSONObject formData = (JSONObject) delegateTask.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = delegateTask.getVariable("pass").toString();
        String instanceId = formData.getString("instanceId");

        // 通过SysUserPost 和SysUser 和 SysPost 关联查询出部门下的所有用户
        SysPost post = iSysPostDao.lambdaQuery().eq(SysPost::getPostCode, "fgld").one();
        List<SysUser> sysUserList = iSysUserDao.lambdaQuery().list();
        List<SysUserPost> sysUserPosts = iSysUserDao.SysUserPostList(post.getPostId());
        List<Long> notifyUserList = Lists.newArrayList();
        StringBuffer sysUserId = new StringBuffer();
       sysUserList.forEach(sysUser -> {
            sysUserPosts.forEach(sysUserPost -> {
                if (sysUserPost.getUserId().equals(sysUser.getUserId())) {
                    sysUserId.append(sysUser.getUserId()).append(",");
                    notifyUserList.add(sysUserPost.getUserId());
                }
            });
        });
        sysUserPosts.forEach(sysUserPost -> {
            sysUserId.append(sysUserPost.getUserId()).append(",");
        });
        String str3 = sysUserId.substring(0, sysUserId.length() - 1);

        // 获取修改后的子表数据
        JSONArray subDateArray = JSONArray.parseArray(delegateTask.getVariable("subData").toString());

        if ("true".equals(blueRedFlag)) {
            // 修改子表数据
            List<SupportYearPlanSub> supportYearPlanSubs = parseSubData(formData, subDateArray);
            List<Long> parentIds = supportYearPlanSubs.stream().map(SupportYearPlanSub::getParentId).distinct().collect(Collectors.toList());
            handleSubData(supportYearPlanSubs, formData.getLong("id"));
            updateMainTableStatus(str3, parentIds);
            System.out.println("通过");
//            抄送
//            System.out.println();
//            SupportYearPlan supportYearPlan = new SupportYearPlan();
//            supportYearPlan.setNotify(str3);
//            supportYearPlan.setInstanceId(formData.getString("instanceId"));
//            supportYearPlan.setProcessKey("supportYearPlan");
//            supportYearPlan.setId(formData.getLong("id"))
//                    .setCreateBy(formData.getString("createBy"))
//                    .setCreateById(formData.getString("createById"))
//                    .setCreateTime(formData.getDate("createTime"))
//                    .setDelFlag(formData.getString("delFlag"))
//                    .setMobileParams(formData.getString("mobileParams"))
//                    .setMobileParams(formData.getString("mobileParams"))
//                    .setRemark(formData.getString("remark"))
//                    .setRemark(formData.getString("remark"))
//                    .setSearchValue(formData.getString("searchValue"))
//                    .setSearchValue(formData.getString("searchValue"))
//                    .setUpdateBy(formData.getString("updateBy"))
//                    .setUpdateById(formData.getString("updateById"))
//                    .setUpdateTime(formData.getDate("updateTime"));
//            iProcessService.submitApply(supportYearPlan, "supportYearPlan", formData);
            notifyUserList.forEach(userId -> actTaskMapper.insertBusinessNotify(instanceId, userId));

        } else {
            System.out.println("不通过");
        }
    }

    private void updateMainTableStatus(String str3, List<Long> parentIds) {
        LambdaQueryWrapper<SupportYearPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SupportYearPlan::getId, parentIds);
        SupportYearPlan updatePlan = new SupportYearPlan();
        updatePlan.setNotify(str3);
        updatePlan.setStatus("6");
        boolean rows = iSupportYearPlanDao.update(updatePlan, queryWrapper);
        if (!rows) {
            throw new ServiceException("修改主表状态失败，IDs: " + parentIds);
        }
    }

    private void handleSubData(List<SupportYearPlanSub> supportYearPlanSubs, Long id) {
        List<SupportYearPlanSub> existingSubs = iSupportYearPlanSubDao.lambdaQuery().eq(SupportYearPlanSub::getParentId, id).list();

        Set<Long> existingIds = existingSubs.stream().map(SupportYearPlanSub::getId).collect(Collectors.toSet());

        for (SupportYearPlanSub sub : supportYearPlanSubs) {
            if (sub.getId() == null) {
                validateSubData(sub);
                sub.setParentId(id);
                saveSubData(sub);
            } else if (!existingIds.contains(sub.getId())) {
                throw new ServiceException("子表数据 ID 不匹配");
            }
        }

        Set<Long> currentIds = supportYearPlanSubs.stream().map(SupportYearPlanSub::getId).collect(Collectors.toSet());

        Set<Long> idsToDelete = existingIds.stream().filter(id1 -> !currentIds.contains(id1)).collect(Collectors.toSet());
        deleteSubData(idsToDelete);
        updateSubData(supportYearPlanSubs);
    }

    private void validateSubData(SupportYearPlanSub sub) {
        if (sub.getWellheadId() == null || sub.getParagraphId() == null || sub.getProjectItemId() == null || sub.getSupportTypeValue().isEmpty()) {
            throw new ServiceException("中段、井口、工程名称,支护类别不能为空");
        }
    }

    private void updateSubData(List<SupportYearPlanSub> supportYearPlanSubs) {
        boolean allUpdated = iSupportYearPlanSubDao.updateBatchById(supportYearPlanSubs);
        if (!allUpdated) {
            throw new ServiceException("部分或全部子表数据修改失败");
        }
    }

    private void deleteSubData(Set<Long> idsToDelete) {
        for (Long id : idsToDelete) {
            boolean deleted = iSupportYearPlanSubDao.removeById(id);
            if (!deleted) {
                throw new ServiceException("删除子表数据失败");
            }
        }
    }

    private void saveSubData(SupportYearPlanSub sub) {
        boolean flag = iSupportYearPlanSubDao.save(sub);
        if (!flag) {
            throw new ServiceException("新增子表数据失败");
        }
    }

    private List<SupportYearPlanSub> parseSubData(JSONObject formData, JSONArray subDateArray) {
        List<SupportYearPlanSub> supportYearPlanSubs = new ArrayList<>();
        for (int i = 0; i < subDateArray.size(); i++) {
            JSONObject jsonObject = subDateArray.getJSONObject(i);
            SupportYearPlanSub supportYearPlanSub = new SupportYearPlanSub().setId(jsonObject.getLong("id")).setWorkAreaId(jsonObject.getLong("workAreaId")).setParentId(formData.getLong("id")).setWellheadId(jsonObject.getLong("wellheadId")).setParagraphId(jsonObject.getLong("paragraphId")).setProjectItemId(jsonObject.getLong("projectItemId")).setSupportTypeValue(jsonObject.getString("supportTypeValue")).setSpecifications(jsonObject.getString("specifications")).setUnitValue(jsonObject.getString("unitValue")).setPlanVolume(jsonObject.getLong("planVolume")).setSupportMethodValue(jsonObject.getString("supportMethodValue")).setBeltNumber(jsonObject.getLong("beltNumber")).setNetArea(jsonObject.getLong("netArea")).setConcreteVolume(jsonObject.getLong("concreteVolume")).setUsteelNumber(jsonObject.getLong("usteelNumber")).setBoltCost(jsonObject.getBigDecimal("boltCost")).setBeltCost(jsonObject.getBigDecimal("beltCost")).setNetCost(jsonObject.getBigDecimal("netCost")).setConcreteCost(jsonObject.getBigDecimal("concreteCost")).setUsteelCost(jsonObject.getBigDecimal("usteelCost")).setTotalSupportCost(jsonObject.getBigDecimal("totalSupportCost"));
            supportYearPlanSubs.add(supportYearPlanSub);
        }

        return supportYearPlanSubs;
    }
}

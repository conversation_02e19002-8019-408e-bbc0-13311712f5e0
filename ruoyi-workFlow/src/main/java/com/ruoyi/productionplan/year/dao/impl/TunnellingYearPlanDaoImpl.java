package com.ruoyi.productionplan.year.dao.impl;

import com.ruoyi.productionplan.year.dao.ITunnellingYearPlanDao;
import com.ruoyi.productionplan.year.dao.ITunnellingYearPlanSubDao;
import com.ruoyi.productionplan.year.domain.TunnellingYearPlan;
import com.ruoyi.productionplan.year.mapper.TunnellingYearPlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import com.ruoyi.activiti.dao.impl.ActDaoImpl;
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class TunnellingYearPlanDaoImpl extends ActDaoImpl<TunnellingYearPlanMapper, TunnellingYearPlan> implements ITunnellingYearPlanDao {
        private final ITunnellingYearPlanSubDao iSubDao;

        @Override
        public ITunnellingYearPlanSubDao iSubDao () {
        return iSubDao;
    }

    @Override
    public String processKey() {
        return "tunnellingYearPlan";
    }
}

package com.ruoyi.productionplan.year.domain;

    import java.math.BigDecimal;

    import com.baomidou.mybatisplus.annotation.*;
    import com.github.yulichang.annotation.FieldMapping;
    import com.ruoyi.archives.BizWellhead.domain.BizWellhead;
    import com.ruoyi.archives.paragraph.domain.Paragraph;
    import com.ruoyi.archives.projectItem.domain.ProjectItem;
    import com.ruoyi.archives.workArea.domain.BizWorkArea;
    import com.ruoyi.common.annotation.Excel;
    import com.ruoyi.common.domain.BaseEntity;
    import com.ruoyi.productionplan.month.validation.Import;
    import com.ruoyi.productionplan.month.validation.Save;
    import com.ruoyi.productionplan.month.validation.Update;
    import lombok.*;
import com.fasterxml.jackson.annotation.JsonFormat;
    import lombok.experimental.FieldNameConstants;
    import org.apache.poi.ss.usermodel.HorizontalAlignment;

    import javax.validation.constraints.*;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_support_year_plan_sub")
public class SupportYearPlanSub extends BaseEntity<SupportYearPlanSub> {

    @Excel(name = "id", sort = 0, align = HorizontalAlignment.RIGHT)
    @NotNull(message = "id不能为空", groups = {Import.class, Update.class})
    @TableId(type = IdType.AUTO)
    private Long id;


    /**
     * 主表id
     */
    @Excel(name = "主表id", sort = 1, align = HorizontalAlignment.RIGHT)
    @NotNull(message = "主表id不能为空", groups = {Import.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 井口
     */
    @NotNull(message = "井口不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;

    @Excel(name = "井口", sort = 2)
    @FieldMapping(tag = BizWellhead.class, thisField = SupportYearPlanSub.Fields.wellheadId, select = BizWellhead.Fields.name)
    @TableField(exist = false)
    private String wellheadName;

    /**
     * 中段
     */
    @NotNull(message = "中段不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;

    @Excel(name = "中段", sort = 3)
    @FieldMapping(tag = Paragraph.class, thisField = SupportYearPlanSub.Fields.paragraphId, select = Paragraph.Fields.name)
    @TableField(exist = false)
    private String paragraphName;

    @NotNull(message = "工区不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workAreaId;


    @Excel(name = "工区")
    @FieldMapping(tag = BizWorkArea.class, thisField = SupportYearPlanSub.Fields.workAreaId, select = BizWorkArea.Fields.name)
    @TableField(exist = false)
    private String workAreaName;
    /**
     * 工程名称id
     */
    @NotNull(message = "工程名称不能为空", groups = {Save.class, Update.class})
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectItemId;

    @Excel(name = "工程名称", sort = 4, width = 40)
    @FieldMapping(tag = ProjectItem.class, thisField = SupportYearPlanSub.Fields.projectItemId, select = ProjectItem.Fields.name)
    @TableField(exist = false)
    private String projectItemName;

    /**
     * 支护类别
     */
    @NotNull(message = "采矿方法不能为空", groups = {Save.class, Update.class})
    @Excel(name = "支护类别", sort = 5, dictType = "support_type")
    private String supportTypeValue;

    /**
     * 设计规格
     */
    @Excel(name = "设计规格", sort = 5)
    private String specifications;

    /**
     * 计划任务量单位
     */
    @Excel(name = "计划任务量单位", sort = 5)
    private String unitValue;

    /**
     * 计划任务量
     */
    @Max(value = 99999999999L, message = "请输入正确的计划任务量")
    @Min(value = 0L, message = "请输入正确的计划任务量")
    @Excel(name = "计划任务量", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long planVolume;

    /**
     * 支护方式
     */
    @Excel(name = "支护方式", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private String supportMethodValue;

    /**
     * 材料消耗锚杆（根）
     */
    @Max(value = 99999999999L, message = "请输入正确的材料消耗锚杆")
    @Min(value = 0L, message = "请输入正确的材料消耗锚杆")
    @Excel(name = "材料消耗锚杆", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long boltNumber;

    /**
     * 材料消耗穿带（根）
     */
    @Max(value = 99999999999L, message = "请输入正确的材料消耗穿带")
    @Min(value = 0L, message = "请输入正确的材料消耗穿带")
    @Excel(name = "材料消耗穿带", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long beltNumber;

    /**
     * 材料消耗金属网（㎡）
     */
    @Max(value = 99999999999L, message = "请输入正确的材料消耗金属网")
    @Min(value = 0L, message = "请输入正确的材料消耗金属网")
    @Excel(name = "材料消耗金属网", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long netArea;

    /**
     * 材料消耗混凝土（m³）
     */
    @Max(value = 99999999999L, message = "请输入正确的材料消耗混凝土")
    @Min(value = 0L, message = "请输入正确的材料消耗混凝土")
    @Excel(name = "材料消耗混凝土", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long concreteVolume;

    /**
     * 材料消耗U型钢（道）
     */
    @Max(value = 99999999999L, message = "请输入正确的材料消耗U型钢")
    @Min(value = 0L, message = "请输入正确的材料消耗U型钢")
    @Excel(name = "材料消耗U型钢", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long usteelNumber;

    /**
     * 单项费用锚杆（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的单项费用锚杆")
    @DecimalMin(value = "0.00", message = "请输入正确的单项费用锚杆")
    @Excel(name = "单项费用锚杆", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal boltCost;

    /**
     * 单项费用穿带（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的单项费用穿带")
    @DecimalMin(value = "0.00", message = "请输入正确的单项费用穿带")
    @Excel(name = "单项费用穿带", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal beltCost;

    /**
     * 单项费用金属网（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的单项费用金属网")
    @DecimalMin(value = "0.00", message = "请输入正确的单项费用金属网")
    @Excel(name = "单项费用金属网", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal netCost;

    /**
     * 单项费用混凝土（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的单项费用混凝土")
    @DecimalMin(value = "0.00", message = "请输入正确的单项费用混凝土")
    @Excel(name = "单项费用混凝土", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal concreteCost;

    /**
     * 单项费用U型钢（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的单项费用U型钢")
    @DecimalMin(value = "0.00", message = "请输入正确的单项费用U型钢")
    @Excel(name = "单项费用U型钢", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal usteelCost;

    /**
     * 支护费用合计（元）
     */
    @DecimalMax(value = "99999999999999.99", message = "请输入正确的支护费用合计")
    @DecimalMin(value = "0.00", message = "请输入正确的支护费用合计")
    @Excel(name = "支护费用合计", sort = 8, align = HorizontalAlignment.RIGHT, width = 20, roundingMode = BigDecimal.ROUND_DOWN, scale = 2, cellType = Excel.ColumnType.NUMERIC)
    private BigDecimal totalSupportCost;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}

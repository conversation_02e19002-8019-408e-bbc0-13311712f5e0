package com.ruoyi.accept.support.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.ruoyi.accept.domain.MineOuputMonthAcceptSub;
import com.ruoyi.accept.support.dao.ISupportMonthAcceptDao;
import com.ruoyi.accept.support.dao.ISupportMonthAcceptSubDao;
import com.ruoyi.accept.support.domain.SupportMonthAccept;
import com.ruoyi.accept.support.domain.SupportMonthAcceptSub;
import com.ruoyi.accept.support.service.SupportMonthAcceptService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/SupportMonthAccept")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SupportMonthAcceptController extends BaseController implements IBaseController {

    private final ISupportMonthAcceptDao dao;
    private final ISupportMonthAcceptSubDao subDao;
    private final SupportMonthAcceptService supportMonthAcceptService;


    @GetMapping
    public AjaxResult list(SupportMonthAccept entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "支护验收", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SupportMonthAccept entity) {
        new ExcelUtil<>(SupportMonthAccept.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "支护验收数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "支护验收", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SupportMonthAccept entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "支护验收", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SupportMonthAccept entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "支护验收", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    //提交
    @Log(title = "支护验收", businessType = BusinessType.UPDATE)
    @PostMapping("/submit/{ids}")
    public AjaxResult submit(@PathVariable String[] ids) {
        return toAjax(supportMonthAcceptService.submit(ids));
    }

    //反提交
    @Log(title = "支护验收", businessType = BusinessType.UPDATE)
    @PostMapping("/revert/{ids}")
    public AjaxResult revert(@PathVariable String[] ids) {
        return toAjax(supportMonthAcceptService.revert(ids));
    }

    @Log(title = "矿区支护验收", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<SupportMonthAcceptSub> util = new ExcelUtil<>(SupportMonthAcceptSub.class);
        List<SupportMonthAcceptSub> subList = null;
        String message = null;
        try {
            subList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
            return success(message);
        }
        List<SupportMonthAcceptSub> list =
                supportMonthAcceptService.getSupportMonthAcceptSubList(subList);

        return success(list);
    }
    @GetMapping("/listSub")
    public AjaxResult listSub(SupportMonthAcceptSub entity) {
        return success(getPageInfo(() -> subDao.list(entity)));
    }

    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception{

        ClassPathResource classPathResource = new ClassPathResource("file/supportaccept.xlsx");
        @Cleanup InputStream inputStream = classPathResource.getStream();
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        @Cleanup Workbook template = WorkbookFactory.create(inputStream);
        template.write(outputStream);
    }

}

package com.ruoyi.accept.support.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_support_month_accept_sub")
public class SupportMonthAcceptSub extends BaseEntity<SupportMonthAcceptSub> {

    /**
     * 主表id
     */
    @Excel(name = "主表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 计划子表id
     */
    @Excel(name = "计划子表id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long planSubId;

    /**
     * 井口
     */
    @Excel(name = "井口")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long wellheadId;

    /**
     * 中段
     */
    @Excel(name = "中段")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long paragraphId;


    /**
     * 工程名称id
     */
    @Excel(name = "工程名称id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectItemId;

    @TableField(exist = false)
    @Excel(name = "工程名称")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private String projectItemName;
    /**
     * 工区
     */
    @Excel(name = "工区")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workAreaId;
    /**
     * 工程类别
     */
    @Excel(name = "工程类别")
    private String projectCategoryValue;

    /**
     * 喷砼(m³)
     */
    @Excel(name = "喷砼")
    private BigDecimal shotcrete;

    /**
     * 树脂锚杆（根)
     */
    @Excel(name = "树脂锚杆")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long resinBolt;

    /**
     * 钢筋网（㎡）
     */
    @Excel(name = "钢筋网")
    private BigDecimal reinforcingMesh;

    /**
     * 管缝锚杆（根）
     */
    @Excel(name = "管缝锚杆")
    private BigDecimal pipeSeamAnchorRod;

    /**
     * 穿带（条）
     */
    @Excel(name = "穿带" )
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long threading;

    /**
     * 钢丝网（㎡）
     */
    @Excel(name = "钢丝网")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long steelWireGauze;

    /**
     * 假顶（底）（㎡）
     */
    @Excel(name = "假顶")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long falseTopBottom;

    /**
     * 钎杆（支）
     */
    @Excel(name = "钎杆")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long drillRod;

    /**
     * 勾花网（㎡）
     */
    @Excel(name = "勾花网")
    private BigDecimal chainLinkFence;

    /**
     * 超前支护钎杆（支）
     */
    @Excel(name = "超前支护钎杆")
    private String advancedSupportDrillRod;

    /**
     * 验收编号
     */
    @Excel(name = "验收编号")
    private String acceptNo;

    /**
     * 工程负责人
     */
    @Excel(name = "工程负责人")
    private String chargePerson;

    /**
     * 公司项目标注
     */
    @Excel(name = "公司项目标注")
    private String companyProjectAnnotation;

    @Excel(name = "备注")
    private String remark;
}

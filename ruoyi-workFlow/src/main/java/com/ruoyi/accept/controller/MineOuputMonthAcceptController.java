package com.ruoyi.accept.controller;

import cn.hutool.core.io.resource.ClassPathResource;
import com.ruoyi.accept.dao.IMineOuputMonthAcceptDao;
import com.ruoyi.accept.dao.IMineOuputMonthAcceptSubDao;
import com.ruoyi.accept.domain.MineOuputMonthAccept;
import com.ruoyi.accept.domain.MineOuputMonthAcceptSub;
import com.ruoyi.accept.service.IMineOuputMonthAcceptService;
import com.ruoyi.archives.projectItem.dao.ProjectItemDao;
import com.ruoyi.archives.projectItem.domain.ProjectItem;
import com.ruoyi.archives.projectItem.service.ProjectItemService;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlan;
import com.ruoyi.productionplan.month.domain.MineOuputMonthPlanSub;
import com.ruoyi.productionplan.month.service.IMineOuputMonthPlanService;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

@RestController
@RequestMapping("/workflow/MineOuputMonthAccept")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class MineOuputMonthAcceptController extends BaseController implements IBaseController {

    private final IMineOuputMonthAcceptDao dao;
    private final IMineOuputMonthAcceptSubDao iMineOuputMonthAcceptSubDao;
    private final IMineOuputMonthAcceptService iMineOuputMonthAcceptService;


    @GetMapping
    public AjaxResult list(MineOuputMonthAccept entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "采出工程验收", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(MineOuputMonthAccept entity) {
        new ExcelUtil<>(MineOuputMonthAccept.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "采出工程验收数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "采出工程验收", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MineOuputMonthAccept entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "采出工程验收", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MineOuputMonthAccept entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "采出工程验收", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    /**
     * 状态修改
     */

    @Log(title = "采出工程验收", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody MineOuputMonthAccept entity) {
        return toAjax(dao.lambdaUpdate()
                .eq(MineOuputMonthAccept::getId, entity.getId())
                .set(MineOuputMonthAccept::getSubmitStatusValue, entity.getSubmitStatusValue())
                .update());
    }

    @Log(title = "矿区月度采出验收", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcelSub")
    public void exportExcelSub(MineOuputMonthAccept entity) {
        if (entity.getId() == null) {
            new ExcelUtil<>(MineOuputMonthAcceptSub.class).importTemplateExcel(ServletUtils.getResponse(), "矿区采出验收数据");
        } else {
            MineOuputMonthAcceptSub mineOuputMonthAcceptSub = new MineOuputMonthAcceptSub();
            mineOuputMonthAcceptSub.setParentId(entity.getId());
            new ExcelUtil<>(MineOuputMonthAcceptSub.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> iMineOuputMonthAcceptSubDao.list(mineOuputMonthAcceptSub)).getList(), "矿区月度采出矿计划数据");
        }
    }

    @Log(title = "矿区采出验收", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<MineOuputMonthAcceptSub> util = new ExcelUtil<>(MineOuputMonthAcceptSub.class);
        List<MineOuputMonthAcceptSub> mineOuputMonthAcceptSubList = null;
        String message = null;
        try {
            mineOuputMonthAcceptSubList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
            return success(message);
        }
        List<MineOuputMonthAcceptSub> list =
        iMineOuputMonthAcceptService.getMineOuputMonthAcceptSubList(mineOuputMonthAcceptSubList);
        return success(list);
    }

    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) throws Exception{

        ClassPathResource classPathResource = new ClassPathResource("file/outputaccept.xlsx");
        @Cleanup InputStream inputStream = classPathResource.getStream();
        @Cleanup ServletOutputStream outputStream = response.getOutputStream();
        @Cleanup Workbook template = WorkbookFactory.create(inputStream);
        template.write(outputStream);
    }

}

package com.ruoyi.cm.cmLabor.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.ruoyi.cm.cmElementCost.domain.ElementCost;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import com.ruoyi.common.page.TableDataInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("cm_labor_element_cost")
public class LaborElementCost  {
    /** 劳务表的主键 */
    @Excel(name = "劳务表的主键")
    @ExcelProperty("劳务表的主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long laborId;

    /** 要素费用表的主键 */
    @Excel(name = "要素费用表的主键")
    @ExcelProperty("要素费用表的主键")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long elementCostId;

    @TableField(exist = false)
    private Long[] elementCostIds;

    /**
     * 要素费用
     */
    @TableField(exist = false)
    @EntityMapping(thisField = Fields.elementCostId, joinField = BaseEntity.Fields.id, tag = ElementCost.class)
    private ElementCost elementCost;

    @TableField(exist = false)
    @EntityMapping(thisField = Fields.laborId, joinField = BaseEntity.Fields.id, tag = Labor.class)
    private Labor labor;
}

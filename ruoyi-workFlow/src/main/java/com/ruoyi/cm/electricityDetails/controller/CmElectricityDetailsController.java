package com.ruoyi.cm.electricityDetails.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.cm.ProductYield.domain.ProductYield;
import com.ruoyi.cm.electricityDetails.service.ICmElectricityDetailsService;
import com.ruoyi.cm.electricityDetails.service.impl.ICmElectricityDetailsServiceImpl;
import com.ruoyi.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.cm.electricityDetails.utils.ExcelMergeUtils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;
import com.ruoyi.cm.electricityDetails.dao.ICmElectricityDetailsDao;
import com.ruoyi.common.util.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/workflow/electricityDetails")
@RequiredArgsConstructor(onConstructor = @__({ @Lazy }))
public class CmElectricityDetailsController extends BaseController implements IBaseController {

    private final ICmElectricityDetailsDao dao;
    private final ICmElectricityDetailsService service;

    @GetMapping
    public AjaxResult list(CmElectricityDetails entity) {
        PageInfo<CmElectricityDetails> pageInfo = getPageInfo(() -> dao.list(entity));
        pageInfo.getList().sort((a, b) -> {
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(b.getId(), a.getId());
            }
            return 0;
        });
        return success(pageInfo);
    }

    @Log(title = "电耗明细", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(CmElectricityDetails entity) {
        new ExcelUtil<>(CmElectricityDetails.class).exportExcel(ServletUtils.getResponse(),
                getPageInfo(() -> dao.list(entity)).getList(), "电耗明细数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "电耗明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CmElectricityDetails entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "电耗明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CmElectricityDetails entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "电耗明细", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    /**
     * 下载导入模板
     */
    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) {
        new ExcelUtil<>(ProductYield.class).importTemplateExcel(response, "劳务耗用数据");
    }

    /**
     * 导入耗电明细数据（处理带有合并单元格的Excel文件）
     *
     * @param file            上传的Excel文件
     * @param accountPeriodId 会计期间ID
     * @param updateSupport   是否更新支持
     * @return 处理结果
     */
    @Log(title = "电耗明细数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(@RequestParam("file") MultipartFile file,
            @RequestParam("accountPeriodId") String accountPeriodId,
            boolean updateSupport) throws Exception {
        // 1. 基本校验
        if (file == null || file.isEmpty()) {
            return AjaxResult.error("导入文件不能为空！");
        }

        try (InputStream inputStream = file.getInputStream()) {
            // 读取Excel文件
            Workbook workbook = WorkbookFactory.create(inputStream);

            // 使用Service中的方法解析带有合并单元格的Excel
            List<CmElectricityDetails> cmElectricityDetailsList = service
                    .parseExcelWithMergedCells(workbook);

            if (CollectionUtils.isEmpty(cmElectricityDetailsList)) {
                return AjaxResult.error("导入数据不能为空！");
            }

            // 导入数据
            String message = service.importUpdateCmElectricityDetails(cmElectricityDetailsList, updateSupport,
                    getLoginUser().getUsername(), accountPeriodId);

            return success(message);
        } catch (Exception e) {
            log.error("导入失败！", e);
            return AjaxResult.error("导入失败！" + e.getMessage());
        }
    }
}

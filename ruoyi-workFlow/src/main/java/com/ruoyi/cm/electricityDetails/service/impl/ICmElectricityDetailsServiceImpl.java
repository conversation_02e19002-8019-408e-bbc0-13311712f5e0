package com.ruoyi.cm.electricityDetails.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.cm.electricityDetails.dao.ICmElectricityDetailsDao;
import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;
import com.ruoyi.cm.electricityDetails.service.ICmElectricityDetailsService;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor_ = { @Lazy })
public class ICmElectricityDetailsServiceImpl implements ICmElectricityDetailsService {
    private final ICmElectricityDetailsDao iCmElectricityDetailsDao;

    @Override
    public String importUpdateCmElectricityDetails(List<CmElectricityDetails> cmElectricityDetailsList,
            Boolean isUpdateSupport, String operName, String accId) {
        if (StringUtil.isNull(cmElectricityDetailsList) || cmElectricityDetailsList.size() == 0) {
            throw new ServiceException("导入电耗明细不能为空！");
        }
        int line = 1;
        int successCount = 0;
        StringBuilder successMsg = new StringBuilder();

        // 获取当前时间作为基准时间，确保批量插入时保持Excel原始顺序
        long baseTime = System.currentTimeMillis();

        for (int i = 0; i < cmElectricityDetailsList.size(); i++) {
            CmElectricityDetails details = cmElectricityDetailsList.get(i);
            line++;
            // 设置基础字段
            if (accId != null && !accId.isEmpty()) {
                details.setAccountPeriodId(Long.valueOf(accId));
            }

            // 如果耗电量和单价存在但金额不存在，计算金额
            if (details.getAmount() == null && details.getElectricityConsumption() != null
                    && details.getUnitPrice() != null) {
                details.setAmount(details.getElectricityConsumption().multiply(details.getUnitPrice()));
            }

            details.setCreateBy(operName);
            details.setUpdateBy(operName);

            // 关键修改：为每条记录设置递增的创建时间，确保Excel顺序得以保持
            // Excel中靠前的数据创建时间更早，这样排序时能保持原始顺序
            details.setCreateTime(new java.util.Date(baseTime + i));
            details.setUpdateTime(new java.util.Date(baseTime + i));

            LambdaQueryWrapper<CmElectricityDetails> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(CmElectricityDetails::getAccountPeriodId, details.getAccountPeriodId());
            // 查询数据库是否存在相同的记录
            CmElectricityDetails dbDetails = iCmElectricityDetailsDao.getOne(queryWrapper);
            if (dbDetails != null && isUpdateSupport) {
                details.setId(dbDetails.getId());
                iCmElectricityDetailsDao.updateById(details);
                log.info("第" + line + "条数据更新成功");
            } else if (dbDetails == null) {
                iCmElectricityDetailsDao.save(details);
                log.info("第" + line + "条数据保存成功");
            } else {
                throw new ServiceException("第" + line + "条数据已存在！");
            }
        }
        successMsg.insert(0, "已成功导入" + (line-1) + "条电耗明细数据");
        return successMsg.toString();
    }

    @Override
    public String impportUpdateCmElectricityDetails(List<CmElectricityDetails> list) {
        if (StringUtil.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入电耗明细不能为空！");
        }
        int line = 1;
        int successNum = 0;
        StringBuilder successMsg = new StringBuilder();
        List<CmElectricityDetails> dbList = new ArrayList<>();
        for (CmElectricityDetails entity : list) {
            line++;
            successNum++;
            dbList.add(entity);

        }
        if (successNum > 0) {
            for (CmElectricityDetails entity : dbList) {
                iCmElectricityDetailsDao.save(entity);
            }
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条");
        } else if (successNum == 0) {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    /**
     * 从Workbook中解析电耗明细数据，处理合并单元格
     * 
     * @param workbook Excel工作簿
     * @return 电耗明细数据列表
     */
    public List<CmElectricityDetails> parseExcelWithMergedCells(Workbook workbook) {
        List<CmElectricityDetails> resultList = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);

        // 获取表头行索引
        int headerRowIndex = findHeaderRowIndex(sheet);
        if (headerRowIndex == -1) {
            return resultList; // 找不到表头，返回空列表
        }

        // 解析表头，获取列索引映射
        Map<String, Integer> headerMap = parseHeaders(sheet, headerRowIndex);

        // 创建合并单元格值的映射
        Map<String, String> mergedRegionValues = new HashMap<>();
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();

        // 处理数据行
        for (int i = headerRowIndex + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            CmElectricityDetails details = new CmElectricityDetails();

            // 设置部门
            Integer deptColIndex = headerMap.get("部门");
            if (deptColIndex != null) {
                String deptName = getCellValueFromMergedRegion(sheet, i, deptColIndex, mergedRegions,
                        mergedRegionValues);
                details.setDeptName(deptName);
            }

            // 设置项目
            Integer projectColIndex = headerMap.get("项目");
            if (projectColIndex != null) {
                String projectName = getCellValueFromMergedRegion(sheet, i, projectColIndex, mergedRegions,
                        mergedRegionValues);
                details.setProjectName(projectName);
            }

            // 设置耗电量
            Integer consumptionColIndex = headerMap.get("耗电量(kwh)");
            if (consumptionColIndex != null) {
                Cell cell = row.getCell(consumptionColIndex);
                if (cell != null) {
                    details.setElectricityConsumption(getBigDecimalValue(cell));
                }
            }

            // 设置单价
            Integer priceColIndex = headerMap.get("单价(元/kwh)");
            if (priceColIndex != null) {
                Cell cell = row.getCell(priceColIndex);
                if (cell != null) {
                    details.setUnitPrice(getBigDecimalValue(cell));
                }
            }

            // 设置金额
            Integer amountColIndex = headerMap.get("金额(元)");
            if (amountColIndex != null) {
                Cell cell = row.getCell(amountColIndex);
                if (cell != null) {
                    details.setAmount(getBigDecimalValue(cell));
                }
            }

            // 检查必填项
            if (StringUtils.isNotBlank(details.getDeptName()) &&
                    StringUtils.isNotBlank(details.getProjectName()) &&
                    details.getElectricityConsumption() != null) {

                // 计算金额（如果没有）
                if (details.getAmount() == null && details.getElectricityConsumption() != null
                        && details.getUnitPrice() != null) {
                    details.setAmount(details.getElectricityConsumption().multiply(details.getUnitPrice()));
                }

                resultList.add(details);
            }
        }

        return resultList;
    }

    /**
     * 查找表头行的索引
     */
    private int findHeaderRowIndex(Sheet sheet) {
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            // 检查是否包含特定的表头列名
            for (int j = 0; j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                if (cell != null) {
                    String value = cell.toString().trim();
                    if ("部门".equals(value) || "项目".equals(value) || "耗电量(kwh)".equals(value)) {
                        return i;
                    }
                }
            }
        }
        return -1; // 找不到表头
    }

    /**
     * 解析表头，建立列名与列索引的映射
     */
    private Map<String, Integer> parseHeaders(Sheet sheet, int headerRowIndex) {
        Map<String, Integer> headerMap = new HashMap<>();
        Row headerRow = sheet.getRow(headerRowIndex);

        if (headerRow != null) {
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerName = cell.toString().trim();
                    headerMap.put(headerName, i);
                }
            }
        }

        return headerMap;
    }

    /**
     * 获取单元格的值，考虑合并单元格的情况
     */
    private String getCellValueFromMergedRegion(Sheet sheet, int rowIndex, int colIndex,
            List<CellRangeAddress> mergedRegions, Map<String, String> mergedRegionValues) {
        // 检查是否是合并单元格
        for (CellRangeAddress region : mergedRegions) {
            if (region.isInRange(rowIndex, colIndex)) {
                // 如果是合并单元格，使用缓存或者获取合并区域左上角单元格的值
                String regionKey = region.getFirstRow() + ":" + region.getFirstColumn();
                if (mergedRegionValues.containsKey(regionKey)) {
                    return mergedRegionValues.get(regionKey);
                } else {
                    Row firstRow = sheet.getRow(region.getFirstRow());
                    if (firstRow != null) {
                        Cell firstCell = firstRow.getCell(region.getFirstColumn());
                        if (firstCell != null) {
                            String value = firstCell.toString().trim();
                            mergedRegionValues.put(regionKey, value);
                            return value;
                        }
                    }
                }
                return "";
            }
        }

        // 如果不是合并单元格，直接获取单元格的值
        Row row = sheet.getRow(rowIndex);
        if (row != null) {
            Cell cell = row.getCell(colIndex);
            if (cell != null) {
                return cell.toString().trim();
            }
        }

        return "";
    }

    /**
     * 从单元格获取BigDecimal值
     */
    private BigDecimal getBigDecimalValue(Cell cell) {
        if (cell == null)
            return null;

        switch (cell.getCellType()) {
            case NUMERIC:
                return BigDecimal.valueOf(cell.getNumericCellValue());
            case STRING:
                String value = cell.getStringCellValue().trim();
                try {
                    return new BigDecimal(value);
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }
}

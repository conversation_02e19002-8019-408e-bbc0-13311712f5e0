package com.ruoyi.cm.electricityDetails.service;

import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

public interface ICmElectricityDetailsService {
    /**
     * 电耗明细数据导入
     * @param cmElectricityDetailsList 电耗明细数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在则进行更新数据
     * @param operName 操作用户
     * @param accId 会计期间ID
     * @return 导入结果信息
     */
    String importUpdateCmElectricityDetails(List<CmElectricityDetails> cmElectricityDetailsList,
                                            Boolean isUpdateSupport,
                                            String operName,
                                            String accId);

    String impportUpdateCmElectricityDetails(List<CmElectricityDetails> list);

    List<CmElectricityDetails> parseExcelWithMergedCells(Workbook workbook);

//    String getId(String accId);
}

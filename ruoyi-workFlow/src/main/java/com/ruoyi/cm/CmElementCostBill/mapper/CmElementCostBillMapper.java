package com.ruoyi.cm.CmElementCostBill.mapper;

import com.ruoyi.cm.CmElementCostBill.domain.CmElementCostBill;
import com.ruoyi.common.mapper.IBaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CmElementCostBillMapper extends IBaseMapper<CmElementCostBill> {
    /**
     * 统计list的相关值的合计
     * @param entity
     * @return
     */
    CmElementCostBill getTableBottomSum(CmElementCostBill entity);

    /**
     * 统计辅助车间的劳务费用
     * @param entity
     * @return
     */
    List<CmElementCostBill> selectStatisticsForUnitPrice(CmElementCostBill entity);
}

package com.ruoyi.cm.cmTeam.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.archives.BizEquipment.domain.BizEquipment;
import com.ruoyi.cm.Trades.domain.Trades;
import com.ruoyi.cm.cmDept.domain.CmDept;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("cm_team")
public class Team extends BaseEntity<Team> {
    /**
     * 编码
     */
    @Excel(name = "编码")
    @ExcelProperty("编码")
    private String code;

    /**
     * 设备
     */
    private String equipmentId;

    /**
     * 班组（机台）名称
     */
    @Excel(name = "班组")
    @ExcelProperty("班组")
    private String name;

    /**
     * 全称
     */
    @Excel(name = "全称")
    @ExcelProperty("全称")
    private String fullName;

    /**
     * 核算部门
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    @Excel(name = "核算部门")
    @ExcelProperty("核算部门")
    @TableField(exist = false)
    @FieldMapping(tag = CmDept.class, thisField = Team.Fields.deptId, select = CmDept.Fields.deptName)
    private String deptName;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

}

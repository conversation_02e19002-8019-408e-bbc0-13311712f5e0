package com.ruoyi.cm.ProductYield.controller;

import com.ruoyi.cm.LaborConsumption.domain.LaborConsumption;
import com.ruoyi.cm.ProductYield.service.IProductYieldService;
import com.ruoyi.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;
import java.util.List;

import com.ruoyi.cm.ProductYield.domain.ProductYield;
import com.ruoyi.cm.ProductYield.dao.IProductYieldDao;
import com.ruoyi.common.util.poi.ExcelUtil;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@RestController
@RequestMapping("/workflow/ProductYield")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ProductYieldController extends BaseController implements IBaseController {

    private final IProductYieldDao dao;

    private final IProductYieldService service;

    @GetMapping
    public AjaxResult list(ProductYield entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "产品产量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( ProductYield entity) {
        new ExcelUtil<>(ProductYield. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "产品产量数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "产品产量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ProductYield entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "产品产量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ProductYield entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "产品产量", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    /**
     * 下载导入模板
     */
    @PostMapping("downloadImportTemplate")
    public void downloadImportTemplate(HttpServletResponse response) {
        new ExcelUtil<>(ProductYield.class).importTemplateExcel(response, "劳务耗用数据");
    }

    /**
     * 导入产品产量数据
     *
     * @param file
     * @return
     */
    @Log(title = "劳务耗用数据", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData( @RequestParam("file") MultipartFile file,
                                  @RequestParam("accountPeriodId") String accountPeriodId,
                                  @RequestParam("deptId") String deptId,
                                  boolean updateSupport) throws Exception {
        ExcelUtil<ProductYield> util = new ExcelUtil<>(ProductYield.class);
        List<ProductYield> productYieldList = null;
        String message = null;
        try {
            productYieldList = util.importExcelForConvertException(StringUtil.EMPTY, file.getInputStream(), 0);
        } catch (Exception e) {
            message = "导入失败！" + e.getMessage();
        }
        String operName = getUsername();
        if (message == null) {
            message = service.importUpdateProductYield(productYieldList, updateSupport, operName,accountPeriodId,deptId);
        }
        return success(message);
    }

}

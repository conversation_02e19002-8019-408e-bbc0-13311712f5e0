package com.ruoyi.cm.cmElementCost.dao.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ruoyi.cm.cmElementCost.dao.IElementCostDao;
import com.ruoyi.cm.cmElementCost.domain.ElementCost;
import com.ruoyi.cm.cmElementCost.mapper.ElementCostMapper;
import com.ruoyi.cm.cmElementCostType.domain.ElementCostType;
import com.ruoyi.cm.cmElementProject.dao.IElementProjectDao;
import com.ruoyi.cm.cmElementProject.domain.ElementProject;
import com.ruoyi.cm.cmLabor.domain.LaborElementCost;
import com.ruoyi.cm.cmLabor.mapper.LaborElementCostMapper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.system.domain.SysUserPost;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ElementCostDaoImpl extends BaseDaoImpl<ElementCostMapper, ElementCost> implements IElementCostDao {

    private final IElementProjectDao elementProjectDao;

    private final LaborElementCostMapper laborElementCostMapper;

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean removeBatch(List<Long> ids) {
        QueryWrapper<ElementCost> wrapper = new QueryWrapper<>();
        //  存在有效的子数据
        wrapper.in("parent_id", ids).eq("del_flag", 0);
        if (exists(wrapper)) {
            throw new RuntimeException("存在子数据，无法删除，请先删除子数据");
        }
        UpdateWrapper<ElementCost> updateWrapper = new UpdateWrapper<>();
        //  删除主键符合项
        updateWrapper.in("id", ids).set("del_flag", 2);
        return update(updateWrapper);
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean add(ElementCost entity) {
        QueryWrapper<ElementCost> wrapper = new QueryWrapper<>();
        wrapper.eq("code", entity.getCode());
        if (exists(wrapper))
            throw new RuntimeException("编码已存在");
        save(entity);
        return elementProjectDao.newSave(ElementProject.builder().elementCostId(entity.getId()).costProjectId(entity.getCostProjectId()).build());
    }

    @Override
    @DSTransactional(rollbackFor = Exception.class)
    public boolean update(ElementCost entity) {
        QueryWrapper<ElementCost> wrapper = new QueryWrapper<>();
        wrapper.eq("code", entity.getCode()).ne("id", entity.getId());
        if (exists(wrapper))
            throw new RuntimeException("编码已存在");
        UpdateWrapper<ElementProject> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("element_cost_id", entity.getId()).set("cost_project_id", entity.getCostProjectId());
        elementProjectDao.update(updateWrapper);
        return updateById(entity);
    }

    @Override
    public List<Long> elementCostIdlistById(String id) {
        return laborElementCostMapper.selectByLaborId(id).stream().map(LaborElementCost::getElementCostId).distinct().collect(Collectors.toList());
    }
}

package com.ruoyi.cm.Trades.service.impl;

import com.ruoyi.archives.financeItem.dao.IFinanceItemDao;
import com.ruoyi.archives.financeItem.domain.FinanceItem;
import com.ruoyi.cm.Trades.dao.ITradesDao;
import com.ruoyi.cm.Trades.domain.Trades;
import com.ruoyi.cm.Trades.service.ITradesService;
import com.ruoyi.cm.cmDept.dao.ICmDeptDao;
import com.ruoyi.cm.cmDept.domain.CmDept;
import com.ruoyi.cm.cmLabor.dao.ILaborDao;
import com.ruoyi.cm.cmLabor.domain.Labor;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.util.StringUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Repository
@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ITradesServiceImpl implements ITradesService {
    private final ITradesDao tradesDao;

    private final IFinanceItemDao financeItemDao;

    private final ILaborDao laborDao;

    private final ICmDeptDao cmDeptDao;

    /**
     * 添加判重逻辑
     *
     * @param entity 数据
     * @return 添加结果
     */
    @Override
    public boolean save(Trades entity) {
        if (!isTradesExist(entity)) {
            return tradesDao.save(entity);
        }
        throw new RuntimeException("该工种已存在");
    }


    @Override
    public String importUpdateLossDepletionRate(List<Trades> list) {
        if (StringUtil.isNull(list) || list.size() == 0) {
            throw new ServiceException("导入工种不能为空！");
        }
        int line = 1;
        int successNum = 0;
        StringBuilder successMsg = new StringBuilder();
        List<Trades> trades = new ArrayList<>();
        for (Trades entity : list) {
            line++;
            if (StringUtil.isNull(entity.getCode())) {
                throw new ServiceException("第" + line + "行工种code不能为空！");
            }
            if (StringUtil.isNull(entity.getName())) {
                throw new ServiceException("第" + line + "行工种名称不能为空！");
            }
            if (StringUtil.isNull(entity.getFinanceItemName())) {
                throw new ServiceException("第" + line + "行作业项目不能为空！");
            } else {
                FinanceItem financeItem = financeItemDao.lambdaQuery().eq(FinanceItem::getName, entity.getFinanceItemName()).one();
                if (financeItem == null) {
                    throw new ServiceException("第" + line + "行作业项目不存在！");
                } else {
                    entity.setFinanceItemId(financeItem.getId());
                }
            }
            if (StringUtil.isNull(entity.getLaborName())) {
                throw new ServiceException("第" + line + "行劳务不能为空！");
            } else {
                Labor labor = laborDao.lambdaQuery().eq(Labor::getName, entity.getLaborName()).one();
                if (labor == null) {
                    throw new ServiceException("第" + line + "行劳务不存在！");
                } else {
                    entity.setLaborId(labor.getId());
                }
            }
            if (entity.getDeptName() != "") {
                CmDept cmDept = cmDeptDao.lambdaQuery().eq(CmDept::getDeptName, entity.getDeptName()).one();
                if (cmDept == null) {
                    throw new ServiceException("第" + line + "行部门不存在！");
                } else {
                    entity.setDeptId(cmDept.getId());
                }
            }
            // 检查 code 是否重复（考虑 delFlag）
            Trades t = tradesDao.lambdaQuery().eq(Trades::getCode, entity.getCode()).eq(Trades::getDelFlag, 0) // <<<<< 添加此行
                    .one();
            if (t != null) {
                throw new RuntimeException("该工种code已存在");
            }

// 构建查询 name + deptId（支持 deptId 为 null，并且考虑 delFlag）
            var query = tradesDao.lambdaQuery().eq(Trades::getName, entity.getName()).eq(Trades::getDelFlag, 0); // <<<<< 添加此行

            if (entity.getDeptId() == null) {
                query.isNull(Trades::getDeptId);
            } else {
                query.eq(Trades::getDeptId, entity.getDeptId());
            }

            t = query.one();
            if (t != null) {
                throw new RuntimeException("第" + line + "行工种已存在");
            }
            successNum++;
            trades.add(entity);
        }

        if (successNum > 0) {
            for (Trades entity : trades) {
                tradesDao.save(entity);
            }
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条");
        } else if (successNum == 0) {
            successMsg.insert(0, "数据已全部导入成功！共 " + successNum + " 条");
        }
        return successMsg.toString();
    }

    @Override
    public boolean updateById(Trades entity) {
        Trades trades = tradesDao.lambdaQuery().eq(Trades::getId, entity.getId()).one();
        if (trades != null) {
            if (trades.getName().equals(entity.getName()) && trades.getDeptId().equals(entity.getDeptId())) {
                return tradesDao.updateById(entity);
            } else {
                Trades trades1 = tradesDao.lambdaQuery().eq(Trades::getName, entity.getName()).eq(Trades::getDeptId, entity.getDeptId()).one();
                if (trades1 == null) {
                    return tradesDao.updateById(entity);
                }
            }
        }
        throw new RuntimeException("该工种已存在");
    }

    /**
     * 判断是否存在相同工种（根据 code 或 name + deptId）
     *
     * @param entity 数据实体
     * @return 是否存在
     */
    private boolean isTradesExist(Trades entity) {
        // 检查 code 是否重复（考虑 delFlag）
        Trades trades = tradesDao.lambdaQuery().eq(Trades::getCode, entity.getCode()).eq(Trades::getDelFlag, 0).one();
        if (trades != null) {
            throw new RuntimeException("该工种code已存在");
        }

        // 检查 name + deptId 是否重复（同样要考虑 delFlag）
        var query = tradesDao.lambdaQuery().eq(Trades::getName, entity.getName()).eq(Trades::getDelFlag, 0); // <<<<< 加上这一行

        if (entity.getDeptId() == null) {
            query.isNull(Trades::getDeptId);
        } else {
            query.eq(Trades::getDeptId, entity.getDeptId());
        }

        trades = query.one();
        if (trades != null) {
            throw new RuntimeException("该工种已存在");
        }
        return false;
    }
}

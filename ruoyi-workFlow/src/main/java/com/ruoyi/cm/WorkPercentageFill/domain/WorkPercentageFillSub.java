package com.ruoyi.cm.WorkPercentageFill.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("cm_work_percentage_fill_sub")
public class WorkPercentageFillSub extends BaseEntity<WorkPercentageFillSub> {

    /**
     * 工作占比填报表
     */
    @Excel(name = "工作占比填报表")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 作业项目
     */
    @Excel(name = "作业项目")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long financeItemId;

    /**
     * 劳务
     */
    @Excel(name = "劳务")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long laborId;

    /**
     * 工作占比
     */
    @Excel(name = "工作占比")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long workPercentage;


    @TableField(exist = false)
    private Long accountPeriodId;

    @TableField(exist = false)
    private String num;
    @TableField(exist = false)
    private String userName;

    @TableField(exist = false)
    private Long deptId;
}

package com.ruoyi.cm.WorkPercentageFill.dao.impl;

import com.ruoyi.cm.WorkPercentageFill.dao.IWpfManufacturingCostDao;
import com.ruoyi.cm.WorkPercentageFill.domain.WpfManufacturingCost;
import com.ruoyi.cm.WorkPercentageFill.mapper.WpfManufacturingCostMapper;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class WpfManufacturingCostDaoImpl extends BaseDaoImpl<WpfManufacturingCostMapper, WpfManufacturingCost> implements IWpfManufacturingCostDao {
}

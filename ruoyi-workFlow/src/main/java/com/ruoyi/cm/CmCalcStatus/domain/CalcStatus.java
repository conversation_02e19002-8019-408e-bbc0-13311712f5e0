package com.ruoyi.cm.CmCalcStatus.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;
import com.alibaba.excel.annotation.*;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("cm_calc_status")
public class CalcStatus extends BaseEntity<CalcStatus> {
    /**
     * 会计期间表的id
     */
    @Excel(name = "会计期间表的id")
    @ExcelProperty("会计期间表的id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long accountPeriodId;

    /**
     * 抽取状态（1:已完成，0: 进行中）
     */
    @Excel(name = "抽取状态")
    @ExcelProperty("抽取状态")
    private String extractStatus;

    /**
     * 数据归集状态（1:已完成，0: 进行中）
     */
    @Excel(name = "数据归集状态")
    @ExcelProperty("数据归集状态")
    private String dataCollectStatus;

    /**
     * 辅助车间费用确认状态（1:已完成，0: 进行中）
     */
    @Excel(name = "辅助车间费用确认状态")
    @ExcelProperty("辅助车间费用确认状态")
    private String auxiliaryShopStatus;

    /**
     * 生产车间费用确认状态（1:已完成，0: 进行中）
     */
    @Excel(name = "生产车间费用确认状态")
    @ExcelProperty("生产车间费用确认状态")
    private String produceWorkshopStatus;

    /**
     * 成本计算状态（1:已完成，0: 进行中）
     */
    @Excel(name = "成本计算状态")
    @ExcelProperty("成本计算状态")
    private String clacStatus;

    /**
     * 当前进度
     */
    @Excel(name = "当前进度")
    @ExcelProperty("当前进度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long currentProgress;
}

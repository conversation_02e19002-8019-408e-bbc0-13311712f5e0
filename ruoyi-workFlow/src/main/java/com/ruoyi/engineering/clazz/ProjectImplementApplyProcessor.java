package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.dao.IProjectImplementApplyDao;
import com.ruoyi.engineering.domain.ControlPricePlan;
import com.ruoyi.engineering.domain.ProjectImplementApply;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2024-12-23 13:48
 */
@Component("ProjectImplementApplyProcessor")
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ProjectImplementApplyProcessor implements JavaDelegate {
    @Resource
    private  IProjectImplementApplyDao projectImplementApplyDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(projectImplementApplyDao)) {
            projectImplementApplyDao = SpringUtil.getBean(IProjectImplementApplyDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        ProjectImplementApply projectImplementApply = projectImplementApplyDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            projectImplementApply.setStatus(BizStatus.DONE.getCode());
        } else {
            projectImplementApply.setStatus(BizStatus.CANCEL.getCode());
        }
        projectImplementApplyDao.updateById(projectImplementApply);
    }
}

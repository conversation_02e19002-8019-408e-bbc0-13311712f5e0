package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.domain.ControlPricePlan;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.JavaDelegate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class ControlPricePlanProcessor implements JavaDelegate {
    @Resource
    private IControlPricePlanDao controlPricePlanDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(controlPricePlanDao)) {
            controlPricePlanDao = SpringUtil.getBean(IControlPricePlanDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        ControlPricePlan controlPricePlan = controlPricePlanDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            controlPricePlan.setStatus(BizStatus.DONE.getCode());
        } else {
            controlPricePlan.setStatus(BizStatus.CANCEL.getCode());
        }
        controlPricePlanDao.updateById(controlPricePlan);
    }
}

package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.dao.IWorkReportDao;
import com.ruoyi.engineering.domain.ControlPricePlan;
import com.ruoyi.engineering.domain.WorkReport;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("WorkReportProcessor")
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class WorkReportProcessor implements JavaDelegate {
    @Resource
    private  IWorkReportDao iWorkReportDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(iWorkReportDao)) {
            iWorkReportDao = SpringUtil.getBean(IWorkReportDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        WorkReport workReport = iWorkReportDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            workReport.setStatus(BizStatus.DONE.getCode());
        } else {
            workReport.setStatus(BizStatus.CANCEL.getCode());
        }
        iWorkReportDao.updateById(workReport);
    }
}

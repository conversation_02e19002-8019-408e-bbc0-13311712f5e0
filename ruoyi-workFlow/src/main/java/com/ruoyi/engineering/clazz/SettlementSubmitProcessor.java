package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IControlPriceInitialDao;
import com.ruoyi.engineering.dao.ISettlementSubmitDao;
import com.ruoyi.engineering.domain.ControlPriceInitial;
import com.ruoyi.engineering.domain.SettlementSubmit;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

@Component("SettlementSubmitProcessor")
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class SettlementSubmitProcessor implements JavaDelegate {
    private  ISettlementSubmitDao settlementSubmitDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(settlementSubmitDao)){
            settlementSubmitDao = SpringUtil.getBean(ISettlementSubmitDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        SettlementSubmit settlementSubmit = settlementSubmitDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            settlementSubmit.setStatus(BizStatus.DONE.getCode());
        } else {
            settlementSubmit.setStatus(BizStatus.CANCEL.getCode());
        }
        settlementSubmitDao.updateById(settlementSubmit);

    }
}

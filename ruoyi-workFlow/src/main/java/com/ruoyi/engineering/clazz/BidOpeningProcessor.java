package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IBidOpeningDao;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.domain.BidOpening;
import com.ruoyi.engineering.domain.ControlPricePlan;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component("BidOpeningProcessor")
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class BidOpeningProcessor implements JavaDelegate {
    @Resource
    private  IBidOpeningDao iBigOpeningDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(iBigOpeningDao)) {
            iBigOpeningDao = SpringUtil.getBean(IBidOpeningDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        BidOpening bidOpening = iBigOpeningDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            bidOpening.setStatus(BizStatus.DONE.getCode());
        } else {
            bidOpening.setStatus(BizStatus.CANCEL.getCode());
        }
        iBigOpeningDao.updateById(bidOpening);
    }
}

package com.ruoyi.engineering.clazz;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.util.spring.SpringUtil;
import com.ruoyi.engineering.dao.IBidDocumentDao;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.domain.BidDocument;
import com.ruoyi.engineering.domain.ControlPricePlan;
import lombok.RequiredArgsConstructor;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.JavaDelegate;
import org.activiti.engine.delegate.TaskListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2024-12-24 13:52
 */
@Component("BidDocumentProcessor")
@DSTransactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class BidDocumentProcessor implements JavaDelegate {
    @Resource
    private  IBidDocumentDao bidDocumentDao;

    @Override
    public void execute(DelegateExecution execution) {
        if (ObjectUtil.isEmpty(bidDocumentDao)) {
            bidDocumentDao = SpringUtil.getBean(IBidDocumentDao.class);
        }
        // 获取表单数据
        JSONObject formData = (JSONObject) execution.getVariable("formData");
        // 获取通过状态
        String blueRedFlag = execution.getVariable("pass").toString();
        BidDocument bidDocument = bidDocumentDao.getById(formData.getLong("id"));
        if ("true".equals(blueRedFlag)) {
            bidDocument.setStatus(BizStatus.DONE.getCode());
        } else {
            bidDocument.setStatus(BizStatus.CANCEL.getCode());
        }
        bidDocumentDao.updateById(bidDocument);
    }
}

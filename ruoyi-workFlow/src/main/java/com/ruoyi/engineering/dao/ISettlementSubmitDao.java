package com.ruoyi.engineering.dao;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.activiti.dao.IActDao;
import com.ruoyi.engineering.domain.SettlementSubmit;

public interface ISettlementSubmitDao extends IActDao<SettlementSubmit> {
    @Override
    default String processKey () {
        return "SettlementSubmit";
    }
}

package com.ruoyi.engineering.dao.impl;

import com.ruoyi.activiti.dao.impl.ActDaoImpl;
import com.ruoyi.engineering.dao.IControlPricePlanDao;
import com.ruoyi.engineering.domain.ControlPricePlan;
import com.ruoyi.engineering.mapper.ControlPricePlanMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ControlPricePlanDaoImpl extends ActDaoImpl<ControlPricePlanMapper, ControlPricePlan> implements IControlPricePlanDao {
}

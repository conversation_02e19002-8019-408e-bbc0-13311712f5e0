package com.ruoyi.engineering.dao.impl;

import com.github.pagehelper.PageInfo;
import com.ruoyi.activiti.dao.impl.ActDaoImpl;
import com.ruoyi.common.domain.SysDictData;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.engineering.dao.IProjectImplementApplyDao;
import com.ruoyi.engineering.domain.ProjectImplementApply;
import com.ruoyi.engineering.mapper.ProjectImplementApplyMapper;
import com.ruoyi.system.dao.ISysDictDataDao;
import com.ruoyi.system.dao.ISysDictTypeDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProjectImplementApplyDaoImpl extends ActDaoImpl<ProjectImplementApplyMapper, ProjectImplementApply> implements IProjectImplementApplyDao {

    private final ISysDictTypeDao dictTypeService;

    @Override
    public String processKey() {
        return "ProjectImplementApply";
    }

    @Override
    public List<ProjectImplementApply> listLock(ProjectImplementApply entity) {
        entity.setStatus(BizStatus.DONE.getCode());
        entity.setIsJumpControlPrice("0");
//        List<SysDictData> dataPlanType = dictTypeService.selectDictDataByType("invest_plan_type");
//        List<SysDictData> dataStatus = dictTypeService.selectDictDataByType("invest_status");
        return baseMapper.list(entity).stream().peek(item -> item.setProjectId(item.getId())).collect(Collectors.toList());
//        return baseMapper.list(entity).stream().peek(item -> {
//            //  计划类型
//            dataPlanType.stream().filter(dictItem -> Objects.equals(dictItem.getDictValue(), item.getPlanTypeValue())).findAny().ifPresent(dictData -> item.setPlanTypeLabel(dictData.getDictLabel()));
//            //  状态
//            switch (item.getStatus()) {
//                case "0": item.setStatusLabel(BizStatus.CANCEL.getInfo());
//                case "2": item.setStatusLabel(BizStatus.UNSUBMIT.getInfo());
//                case "4": item.setStatusLabel(BizStatus.CHECKING.getInfo());
//                case "6": item.setStatusLabel(BizStatus.DONE.getInfo());
//            }
//        }).collect(Collectors.toList());
    }
}

package com.ruoyi.engineering.dao.impl;

import com.ruoyi.activiti.dao.impl.ActDaoImpl;
import com.ruoyi.engineering.dao.IProjectProgressDao;
import com.ruoyi.engineering.domain.ProjectProgress;
import com.ruoyi.engineering.mapper.ProjectProgressMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class ProjectProgressDaoImpl extends ActDaoImpl<ProjectProgressMapper, ProjectProgress> implements IProjectProgressDao {
    private final ProjectProgressMapper projectProgressMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ProjectProgress> selectList(ProjectProgress projectProgress){
        return projectProgressMapper.selectProjectProgressList(projectProgress);
    }
}

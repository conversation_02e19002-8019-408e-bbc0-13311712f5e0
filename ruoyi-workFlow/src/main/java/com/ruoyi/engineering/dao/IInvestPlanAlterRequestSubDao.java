package com.ruoyi.engineering.dao;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import java.util.Objects;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.engineering.domain.InvestPlanAlterRequestSub;

public interface IInvestPlanAlterRequestSubDao extends IBaseDao<InvestPlanAlterRequestSub> {
}

package com.ruoyi.engineering.dao.impl;

import com.ruoyi.engineering.dao.ISettlementMediationDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import com.ruoyi.engineering.mapper.SettlementMediationMapper;
import com.ruoyi.engineering.domain.SettlementMediation;
import com.ruoyi.activiti.dao.impl.ActDaoImpl;
@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class SettlementMediationDaoImpl extends ActDaoImpl<SettlementMediationMapper, SettlementMediation> implements ISettlementMediationDao {
}

package com.ruoyi.engineering.dao.impl;

import com.ruoyi.engineering.dao.IInvestPlanDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import com.ruoyi.engineering.mapper.InvestPlanMapper;
import com.ruoyi.engineering.domain.InvestPlan;
import com.ruoyi.engineering.domain.InvestPlanSub;
import com.ruoyi.engineering.dao.IInvestPlanSubDao;
import com.ruoyi.activiti.dao.impl.ActDaoImpl;


@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InvestPlanDaoImpl extends ActDaoImpl<InvestPlanMapper, InvestPlan> implements IInvestPlanDao {
    private final IInvestPlanSubDao iSubDao;
    @Override
    public String processKey() {
        return "InvestPlan";
    }

    @Override
    public IInvestPlanSubDao iSubDao () {
        return iSubDao;
    }
}

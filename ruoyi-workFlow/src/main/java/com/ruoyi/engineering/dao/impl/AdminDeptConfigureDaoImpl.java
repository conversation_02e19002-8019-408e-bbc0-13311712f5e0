package com.ruoyi.engineering.dao.impl;

import com.ruoyi.engineering.dao.IAdminDeptConfigureDao;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import com.ruoyi.engineering.mapper.AdminDeptConfigureMapper;
import com.ruoyi.engineering.domain.AdminDeptConfigure;
@Repository
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class AdminDeptConfigureDaoImpl extends BaseDaoImpl<AdminDeptConfigureMapper, AdminDeptConfigure> implements IAdminDeptConfigureDao {
}

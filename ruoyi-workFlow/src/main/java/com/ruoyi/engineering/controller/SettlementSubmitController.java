package com.ruoyi.engineering.controller;

import com.ruoyi.engineering.domain.ControlPrice;
import com.ruoyi.engineering.domain.SettlementMediation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.engineering.domain.SettlementSubmit;
import com.ruoyi.engineering.dao.ISettlementSubmitDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("/workflow/SettlementSubmit")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class SettlementSubmitController extends BaseController implements IBaseController {

    private final ISettlementSubmitDao dao;

    @GetMapping
    public AjaxResult list(SettlementSubmit entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @GetMapping("/listLock")
    public AjaxResult listLock(SettlementSubmit entity) {
        entity.setStatus("6");
        entity.setMediationFlag("1");
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "结算报送", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( SettlementSubmit entity) {
        new ExcelUtil<>(SettlementSubmit. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "结算报送数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "结算报送", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettlementSubmit entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "结算报送", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettlementSubmit entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "结算报送", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交结算报送", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(dao.submitApply(Long.valueOf(id)));
    }


    @PutMapping(value = "/unlockFlag")
    public AjaxResult editUnlockFlag(@RequestBody SettlementSubmit entity) {
        return success(dao.lambdaUpdate().set(SettlementSubmit::getUnlockFlag, entity.getUnlockFlag()).eq(SettlementSubmit::getId, entity.getId()).update());
    }
}

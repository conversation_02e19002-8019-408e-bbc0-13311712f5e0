package com.ruoyi.engineering.controller;

import com.ruoyi.engineering.domain.Settlement;
import com.ruoyi.engineering.domain.WinningNotice;
import com.ruoyi.engineering.service.SettlementReviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.engineering.domain.SettlementReview;
import com.ruoyi.engineering.dao.ISettlementReviewDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("/workflow/settlementReview")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class SettlementReviewController extends BaseController implements IBaseController {

    private final ISettlementReviewDao dao;
    private final SettlementReviewService service;

    @GetMapping
    public AjaxResult list(SettlementReview entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @GetMapping("/listLock")
    public AjaxResult listLock(SettlementReview entity) {
        entity.setStatus("6");
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "结算复核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SettlementReview entity) {
        new ExcelUtil<>(SettlementReview.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "结算复核数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "结算复核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SettlementReview entity) {
        return service.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "结算复核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SettlementReview entity) {
        return service.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "结算复核", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(service.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }


    @Log(title = "提交结算复核", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(dao.submitApply(Long.valueOf(id)));
    }

    @Log(title = "解锁结算复核", businessType = BusinessType.UPDATE)
    @PutMapping("/unlockFlag")
    public AjaxResult editUnlockFlag(@RequestBody SettlementReview entity) {
        return success(dao.lambdaUpdate().set(SettlementReview::getUnlockFlag, entity.getUnlockFlag()).eq(SettlementReview::getId, entity.getId()).update());
    }

}

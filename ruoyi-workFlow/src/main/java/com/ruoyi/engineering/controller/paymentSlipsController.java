package com.ruoyi.engineering.controller;

import com.ruoyi.engineering.domain.ControlPrice;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.context.annotation.Lazy;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;

import java.util.Arrays;

import com.ruoyi.engineering.domain.paymentSlips;
import com.ruoyi.engineering.dao.IpaymentSlipsDao;
import com.ruoyi.common.util.poi.ExcelUtil;

@RestController
@RequestMapping("workflow/paymentSlips")
@RequiredArgsConstructor(onConstructor = @__({@Lazy}))
public class paymentSlipsController extends BaseController implements IBaseController {

    private final IpaymentSlipsDao dao;

    @GetMapping
    public AjaxResult list(paymentSlips entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "付款单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export( paymentSlips entity) {
        new ExcelUtil<>(paymentSlips. class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "付款单数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id")String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "付款单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody paymentSlips entity) {
        return dao.save(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "付款单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody paymentSlips entity) {
        return dao.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "付款单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(dao.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交付款单", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(dao.submitApply(Long.valueOf(id)));
    }

    @PutMapping(value = "/unlockFlag")
    public AjaxResult editUnlockFlag(@RequestBody paymentSlips entity) {
        return success(dao.lambdaUpdate().set(paymentSlips::getUnlockFlag, entity.getUnlockFlag()).eq(paymentSlips::getId, entity.getId()).update());
    }

}

package com.ruoyi.engineering.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.util.ServletUtils;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.engineering.dao.IWinningNoticeDao;
import com.ruoyi.engineering.domain.BidDocument;
import com.ruoyi.engineering.domain.BidOpening;
import com.ruoyi.engineering.domain.WinningNotice;
import com.ruoyi.engineering.service.IWinningNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

@RestController
@RequestMapping("/workflow/WinningNotice")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class WinningNoticeController extends BaseController implements IBaseController {

    private final IWinningNoticeDao dao;
    private final IWinningNoticeService service;

    @GetMapping
    public AjaxResult list(WinningNotice entity) {
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @GetMapping("/listLock")
    public AjaxResult listLock(WinningNotice entity) {
        entity.setStatus("6");
        return success(getPageInfo(() -> dao.list(entity)));
    }

    @Log(title = "中标通知书", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(WinningNotice entity) {
        new ExcelUtil<>(WinningNotice.class).exportExcel(ServletUtils.getResponse(), getPageInfo(() -> dao.list(entity)).getList(), "中标通知书数据");
    }

    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") String id) {
        return success(dao.getByIdDeep(Long.valueOf(id)));
    }

    @Log(title = "中标通知书", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WinningNotice entity) {
            return service.save(entity) ? toAjax(entity.getId()) : toAjax(false);

    }

    @Log(title = "中标通知书", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WinningNotice entity) {
        return service.updateById(entity) ? toAjax(entity.getId()) : toAjax(false);
    }

    @Log(title = "中标通知书", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String[] ids) {
        return toAjax(service.removeById(Arrays.stream(ids).map(Long::valueOf).toArray(Long[]::new)));
    }

    @Log(title = "提交中标通知书", businessType = BusinessType.UPDATE)
    @PostMapping("/submitApply/{id}")
    public AjaxResult submitApply(@PathVariable String id) {
        return toAjax(dao.submitApply(Long.valueOf(id)));
    }

    @Log(title = "解锁中标通知书", businessType = BusinessType.UPDATE)
    @PutMapping("/unlockFlag")
    public AjaxResult editUnlockFlag(@RequestBody WinningNotice entity) {
        return success(dao.lambdaUpdate().set(WinningNotice::getUnlockFlag, entity.getUnlockFlag()).eq(WinningNotice::getId, entity.getId()).update());
    }

}

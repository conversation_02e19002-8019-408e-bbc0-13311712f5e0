package com.ruoyi.engineering.service.impl;

import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.util.SecurityUtil;
import com.ruoyi.engineering.dao.IInvestPlanDao;
import com.ruoyi.engineering.dao.IInvestPlanSubDao;
import com.ruoyi.engineering.domain.InvestPlan;
import com.ruoyi.engineering.domain.InvestPlanSub;
import com.ruoyi.engineering.mapper.InvestPlanMapper;
import com.ruoyi.engineering.service.InvestPlanService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class InvestPlanServiceImpl implements InvestPlanService {

    private final IInvestPlanDao investPlanDao;
    private final InvestPlanMapper investPlanMapper;

    /**
     * 判断项目编码是否重复
     *
     * @param entity 集合
     * @return true:不重复 false:重复
     */
    @Override
    public boolean isProjectCodeUnique(InvestPlan entity) {
        InvestPlan one = investPlanDao.list()
                .stream()
                .filter(plan -> plan.getProjectCode().equals(entity.getProjectCode()))
                .findFirst()
                .orElse(null);
        if (one != null) {
            throw new ServiceException("项目编码重复请重新输入");
        }
        return true;
    }

    @Override
    public boolean isStatusRemove(String[] ids) {
        for (String id : ids) {
            InvestPlan investPlan = investPlanDao.getById(id);
            if (investPlan.getStatus().equals(BizStatus.CHECKING.getCode()) || investPlan.getStatus().equals(BizStatus.DONE.getCode())) {
                throw new ServiceException("存在已提交的投资计划，请勿删除");
            }
        }
        return true;
    }


    /**
     * @param entity
     * @return
     */
    @Override
    public List<InvestPlan> listInvestPlanWithoutPermi(InvestPlan entity) {
        return investPlanMapper.list(entity);
    }
}

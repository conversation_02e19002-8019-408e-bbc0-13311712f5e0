package com.ruoyi.engineering.service.impl;

import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.engineering.dao.ISettlementDao;
import com.ruoyi.engineering.dao.ISettlementReviewDao;
import com.ruoyi.engineering.domain.CompletionAcceptance;
import com.ruoyi.engineering.domain.Settlement;
import com.ruoyi.engineering.domain.SettlementReview;
import com.ruoyi.engineering.domain.WinningNotice;
import com.ruoyi.engineering.service.SettlementReviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SettlementReviewServiceImpl implements SettlementReviewService {

    private final ISettlementReviewDao dao;

    @Override
    public boolean save(SettlementReview entity) {

        if (dao.isCodeUnique(SettlementReview::getProjectCode, SettlementReview::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' " + entity.getProjectCode() + " '的项目已存在");
        }
        return dao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(SettlementReview entity) {

        if (dao.isCodeUnique(SettlementReview::getProjectCode, SettlementReview::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' " + entity.getProjectCode() + " '的项目已存在");
        }
        return dao.updateById(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Long[] id) {
        isApply(id);
        return dao.removeById(id);
    }

    /**
     * 判断是否申请
     *
     * @param ids 需要删除的目标
     */
    private void isApply(Long[] ids) {
        for (Long id : ids) {
            SettlementReview settlementReview = dao.getById(id);
            if (settlementReview != null &&
                    (BizStatus.CHECKING.getCode().equals(settlementReview.getStatus()) ||
                            BizStatus.DONE.getCode().equals(settlementReview.getStatus()))) {
                throw new ServiceException("单据为审核中或已通过状态，不可以删除");
            }
        }
    }
}

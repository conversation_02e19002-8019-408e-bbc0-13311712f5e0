package com.ruoyi.engineering.service.impl;

import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.engineering.dao.*;
import com.ruoyi.engineering.domain.*;
import com.ruoyi.engineering.service.IProjectImplementApplyService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2024-12-23 13:47
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ProjectImplementApplyServiceImpl implements IProjectImplementApplyService {
    private final IProjectImplementApplyDao projectImplementApplyDao;
    private final IControlPriceDao controlPriceDao;
    private final IControlPriceFinalDao controlPriceFinalDao;
    private final IBidDocumentDao bidDocumentDao;
    private final IBidOpeningDao bidOpeningDao;
    private final IWinningNoticeDao winningNoticeDao;
    private final IContractDao contractDao;
    private final IWorkReportDao workReportDao;
    private final IConstructionProgressFeedbackDao constructionProgressFeedbackDao;
    private final ICompletionAcceptanceDao completionAcceptanceDao;
    private final ISettlementDao settlementDao;
    private final ISettlementReviewDao settlementReviewDao;
    private final IDataArchivingDao dataArchivingDao;


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @Override
    public boolean save(ProjectImplementApply entity) {
        if (projectImplementApplyDao.isCodeUnique(ProjectImplementApply::getProjectCode, ProjectImplementApply::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new ServiceException("项目编码不能重复");
        }
        return projectImplementApplyDao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(ProjectImplementApply entity) {
        if (projectImplementApplyDao.isCodeUnique(ProjectImplementApply::getProjectCode, ProjectImplementApply::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new ServiceException("项目编码不能重复");
        }
        return projectImplementApplyDao.updateById(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Long[] id) {
        this.isApply(id);
        return projectImplementApplyDao.removeById(id);
    }

    /**
     * 判断是否申请
     *
     * @param ids 需要删除的目标
     */
    private void isApply(Long[] ids) {
        for (Long id : ids) {
            ProjectImplementApply projectImplementApply = projectImplementApplyDao.getById(id);
            if (projectImplementApply != null &&
                    (BizStatus.CHECKING.getCode().equals(projectImplementApply.getStatus()) ||
                            BizStatus.DONE.getCode().equals(projectImplementApply.getStatus()))) {
                throw new ServiceException("存在已提交的项目实施申请，请勿删除");
            }
        }
    }

    /**
     * 返回节点标识
     * @param id
     * @return
     */
    @Override
    public String getCurrentNode(String id) {
        // 定义节点顺序和对应的返回值
        Map<Class<?>, String> nodeMapping = new LinkedHashMap<>();
        nodeMapping.put(ProjectImplementApply.class, "");
        nodeMapping.put(ControlPrice.class, "ProjectImplementApply");
        nodeMapping.put(ControlPriceFinal.class, "ControlPrice");
        nodeMapping.put(BidDocument.class, "ControlPriceFinal");
        nodeMapping.put(BidOpening.class, "BidDocument");
        nodeMapping.put(WinningNotice.class, "BidOpening");
        nodeMapping.put(Contract.class, "WinningNotice");
        nodeMapping.put(WorkReport.class, "Contract");
        nodeMapping.put(ConstructionProgressFeedback.class, "WorkReport");
        nodeMapping.put(CompletionAcceptance.class, "ConstructionProgressFeedback");
        nodeMapping.put(Settlement.class, "CompletionAcceptance");
        nodeMapping.put(SettlementReview.class, "Settlement");
        nodeMapping.put(DataArchiving.class, "SettlementReview");


        // 依次检查每个节点
        for (Map.Entry<Class<?>, String> entry : nodeMapping.entrySet()) {
            String nodeName = entry.getValue();
            boolean exists = findNodeByClassAndId(entry.getKey(), id);

            if (!exists) {
                return nodeName; // 如果该节点为空，返回当前节点名称
            }
        }

        return "DataArchiving"; // 最后一个节点完成，返回数据归档
    }

    private boolean findNodeByClassAndId(Class<?> clazz, String id) {
        if (clazz == ProjectImplementApply.class) {
            return projectImplementApplyDao.lambdaQuery()
                    .eq(ProjectImplementApply::getId, id)
                    .eq(ProjectImplementApply::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == ControlPrice.class) {
            return controlPriceDao.lambdaQuery()
                    .eq(ControlPrice::getProjectId, id)
                    .eq(ControlPrice::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == ControlPriceFinal.class) {
            return controlPriceFinalDao.lambdaQuery()
                    .eq(ControlPriceFinal::getProjectId, id)
                    .eq(ControlPriceFinal::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == BidDocument.class) {
            return bidDocumentDao.lambdaQuery()
                    .eq(BidDocument::getProjectId, id)
                    .eq(BidDocument::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == BidOpening.class) {
            return bidOpeningDao.lambdaQuery()
                    .eq(BidOpening::getProjectId, id)
                    .eq(BidOpening::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == WinningNotice.class) {
            return winningNoticeDao.lambdaQuery()
                    .eq(WinningNotice::getProjectId, id)
                    .eq(WinningNotice::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == Contract.class) {
            return contractDao.lambdaQuery()
                    .eq(Contract::getProjectId, id)
                    .eq(Contract::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == WorkReport.class) {
            return workReportDao.lambdaQuery()
                    .eq(WorkReport::getProjectId, id)
                    .eq(WorkReport::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == ConstructionProgressFeedback.class) {
            return constructionProgressFeedbackDao.lambdaQuery()
                    .eq(ConstructionProgressFeedback::getProjectId, id)
                    .eq(ConstructionProgressFeedback::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == CompletionAcceptance.class) {
            return completionAcceptanceDao.lambdaQuery()
                    .eq(CompletionAcceptance::getProjectId, id)
                    .eq(CompletionAcceptance::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == Settlement.class) {
            return settlementDao.lambdaQuery()
                    .eq(Settlement::getProjectId, id)
                    .eq(Settlement::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == SettlementReview.class) {
            return settlementReviewDao.lambdaQuery()
                    .eq(SettlementReview::getProjectId, id)
                    .eq(SettlementReview::getStatus, BizStatus.DONE.getCode())
                    .exists();
        } else if (clazz == DataArchiving.class) {
            return dataArchivingDao.lambdaQuery()
                    .eq(DataArchiving::getProjectId, id)
                    .eq(DataArchiving::getStatus, BizStatus.DONE.getCode())
                    .exists();
        }
        return false;
    }

}

package com.ruoyi.engineering.service;

import com.ruoyi.engineering.domain.Contract;
import com.ruoyi.engineering.domain.DataArchiving;
import com.ruoyi.engineering.domain.WinningNotice;

public interface DataArchivingService {

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    boolean save(DataArchiving entity);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    boolean updateById(DataArchiving entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean removeById(Long[] id);
}

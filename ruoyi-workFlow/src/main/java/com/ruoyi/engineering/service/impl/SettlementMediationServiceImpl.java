package com.ruoyi.engineering.service.impl;

import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.engineering.dao.ISettlementMediationDao;
import com.ruoyi.engineering.dao.ISettlementReviewDao;
import com.ruoyi.engineering.domain.SettlementMediation;
import com.ruoyi.engineering.domain.SettlementReview;
import com.ruoyi.engineering.service.SettlementMediationService;
import com.ruoyi.engineering.service.SettlementReviewService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SettlementMediationServiceImpl implements SettlementMediationService {

    private final ISettlementMediationDao dao;

    @Override
    public boolean save(SettlementMediation entity) {

        if (dao.isCodeUnique(SettlementMediation::getProjectCode, SettlementMediation::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' " + entity.getProjectCode() + " '的项目已存在");
        }
        return dao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(SettlementMediation entity) {

        if (dao.isCodeUnique(SettlementMediation::getProjectCode, SettlementMediation::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' " + entity.getProjectCode() + " '的项目已存在");
        }
        return dao.updateById(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Long[] id) {
        isApply(id);
        return dao.removeById(id);
    }

    /**
     * 判断是否申请
     *
     * @param ids 需要删除的目标
     */
    private void isApply(Long[] ids) {
        for (Long id : ids) {
            SettlementMediation settlementMediation = dao.getById(id);
            if (settlementMediation != null &&
                    (BizStatus.CHECKING.getCode().equals(settlementMediation.getStatus()) ||
                            BizStatus.DONE.getCode().equals(settlementMediation.getStatus()))) {
                throw new ServiceException("单据为审核中或已通过状态，不可以删除");
            }
        }
    }
}

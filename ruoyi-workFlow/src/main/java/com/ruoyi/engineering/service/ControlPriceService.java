package com.ruoyi.engineering.service;

import com.ruoyi.engineering.domain.ControlPrice;
import com.ruoyi.engineering.domain.ControlPriceFinal;

public interface ControlPriceService {

    /**
     * 判断单据的状态是否可删除
     */
    boolean isStatusRemove(String[] ids);


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    boolean save(ControlPrice entity);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    boolean updateById(ControlPrice entity);
}

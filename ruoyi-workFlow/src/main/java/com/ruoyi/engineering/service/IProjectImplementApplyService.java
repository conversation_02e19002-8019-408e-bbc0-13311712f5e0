package com.ruoyi.engineering.service;

import com.ruoyi.engineering.domain.ProjectImplementApply;

/**
 * <AUTHOR>
 * @create 2024-12-23 13:47
 */
public interface IProjectImplementApplyService {
    /**
     * 新增
     *
     * @param entity
     * @return
     */
    boolean save(ProjectImplementApply entity);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    boolean updateById(ProjectImplementApply entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean removeById(Long[] id);

    /**
     * 根据id查询节点标识
     * @param id
     * @return
     */
    String getCurrentNode(String id);
}

package com.ruoyi.engineering.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.engineering.dao.IBidDocumentDao;
import com.ruoyi.engineering.dao.IControlPriceDao;
import com.ruoyi.engineering.dao.IControlPriceFinalDao;
import com.ruoyi.engineering.domain.BidDocument;
import com.ruoyi.engineering.domain.ControlPrice;
import com.ruoyi.engineering.domain.ControlPriceFinal;
import com.ruoyi.engineering.service.IBidDocumentService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @create 2024-12-24 13:48
 */
@Service
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class BidDocumentServiceImpl implements IBidDocumentService {
    private final IBidDocumentDao bidDocumentDao;
    private final IControlPriceFinalDao controlPriceFinalDao;

    /**
     * 新增
     *
     * @param entity
     * @return
     */
    @Override
    public boolean save(BidDocument entity) {

        if (bidDocumentDao.isCodeUnique(BidDocument::getProjectCode, BidDocument::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' "+ entity.getProjectCode() +" '的项目已存在");
        }
        return bidDocumentDao.save(entity);
    }

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(BidDocument entity) {

        if (bidDocumentDao.isCodeUnique(BidDocument::getProjectCode, BidDocument::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' "+ entity.getProjectCode() +" '的项目已存在");
        }
        return bidDocumentDao.updateById(entity);
    }

    /**
     * 删除
     *
     * @param id
     * @return
     */
    @Override
    public boolean removeById(Long[] id) {
        isApply(id);
        return bidDocumentDao.removeById(id);
    }

    /**
     * 判断是否申请
     *
     * @param ids 需要删除的目标
     */
    private void isApply(Long[] ids) {
        for (Long id : ids) {
            BidDocument bidDocument = bidDocumentDao.getById(id);
            if (bidDocument != null &&
                    (BizStatus.CHECKING.getCode().equals(bidDocument.getStatus()) ||
                           BizStatus.DONE.getCode().equals(bidDocument.getStatus()))) {
                throw new ServiceException("单据为审核中或已通过状态，不可以删除");
            }
        }
    }
}

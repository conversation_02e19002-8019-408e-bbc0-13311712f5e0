package com.ruoyi.engineering.service;

import com.ruoyi.engineering.domain.ConstructionProgressFeedback;
import com.ruoyi.engineering.domain.WorkReport;

public interface ConstructionProgressFeedbackService {


    /**
     * 新增
     *
     * @param entity
     * @return
     */
    // boolean save(ConstructionProgressFeedback entity);

    /**
     * 修改
     *
     * @param entity
     * @return
     */
    // boolean updateById(ConstructionProgressFeedback entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    boolean removeById(Long[] id);

}

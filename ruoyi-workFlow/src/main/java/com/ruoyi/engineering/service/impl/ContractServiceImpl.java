package com.ruoyi.engineering.service.impl;

import com.ruoyi.common.enums.BizStatus;
import com.ruoyi.engineering.dao.IContractDao;
import com.ruoyi.engineering.domain.Contract;
import com.ruoyi.engineering.service.ContractService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class ContractServiceImpl implements ContractService {

    private final IContractDao contractDao;

    @Override
    public boolean save(Contract entity) {

        if (contractDao.isCodeUnique(Contract::getProjectCode, Contract::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' "+ entity.getProjectCode() +" '的项目已存在");
        }
        return contractDao.save(entity);
    }

    /**
     * @param entity
     * @return
     */
    @Override
    public boolean updateById(Contract entity) {

        if (contractDao.isCodeUnique(Contract::getProjectCode, Contract::getId,
                entity.getProjectCode(), entity.getId())) {
            throw new RuntimeException("项目编码为' "+ entity.getProjectCode() +" '的项目已存在");
        }
        return contractDao.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long[] id) {
        isDeleteContract(id);
        return contractDao.removeById(id);
    }

    /**
     * 判断删除时的状态
     * 状态为4或者6时就不可以删除
     * 并且提示单据为审核中或已通过状态，不可以删除
     * */
    public void isDeleteContract(Long[] id) {
        for (Long ids : id) {
            Contract contract = contractDao.getById(ids);
            if (BizStatus.CHECKING.getCode().equals(contract.getStatus())|| BizStatus.DONE.getCode().equals(contract.getStatus())) {
                throw new RuntimeException("单据为审核中或已通过状态，不可以删除");
            }
        }
    }
}

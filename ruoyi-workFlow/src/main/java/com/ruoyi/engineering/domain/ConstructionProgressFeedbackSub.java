package com.ruoyi.engineering.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import lombok.experimental.FieldNameConstants;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_construction_progress_feedback_sub")
public class ConstructionProgressFeedbackSub extends BaseEntity<ConstructionProgressFeedbackSub> {

    /**
     * 父ID
     */
    @Excel(name = "父ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 记录日期
     */
    @Excel(name = "记录日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recordDate;

    /**
     * 施工难点
     */
    @Excel(name = "施工难点")
    private String constructionNodus;

    /**
     * 上传图片
     */
    @Excel(name = "上传图片")
    private String uploadImage;

}

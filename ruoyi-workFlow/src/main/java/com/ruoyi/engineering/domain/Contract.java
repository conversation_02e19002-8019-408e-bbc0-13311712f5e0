package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_contract")
public class Contract extends ProcessEntity<Contract> {
    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    @ExcelProperty("项目ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @FieldMapping(tag = ProjectImplementApply.class, thisField = Contract.Fields.projectCode, joinField = ProjectImplementApply.Fields.projectCode, select = "projectName")
    private String projectName;

    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;
    /**
     * 年度
     */
    @Excel(name = "年度")
    @ExcelProperty("年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    @Excel(name = "实际完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;

    @Excel(name = "合同金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal contractAmount;

    /**
     * 附件
     */
    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;
    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 解锁标志
     */
    private String unlockFlag;
}

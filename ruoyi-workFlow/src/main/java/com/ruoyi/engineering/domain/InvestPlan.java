package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.EntityMapping;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.SysDept;
import com.ruoyi.productassay.bizSamplingAssayMethod.domain.BizSamplingAssayMethod;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_invest_plan")
public class InvestPlan extends ProcessEntity<InvestPlan> {

    /**
     * 年度
     */
    @Excel(name = "年度")
    @ExcelProperty("年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 投资分管部门
     */
    @Excel(name = "投资分管部门")
    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    private Long deptId;

    @Excel(name = "合计计划投资额")
    private BigDecimal totalPlanAmount;

    /**
     * 审定状态
     */
    @Excel(name = "审定状态",dictType = "authorize_status")
    @ExcelProperty("审定状态")
    private String investStatusValue;

    /**
     * 投资计划类别
     */
    @Excel(name = "投资计划类别", dictType = "invest_plan_type")
    private String planTypeValue;

    /**
     * 计划批复时间
     */
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "计划批复时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("计划批复时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;

    /**
     * 附件
     */
    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;


    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    @EntityMapping(joinField = InvestPlanSub.Fields.parentId)
    private List<InvestPlanSub> investPlanSubList = new ArrayList<>();

    /**
     * 年度计划/临时计划
     */
    @Excel(name = "年度计划/临时计划", dictType = "annual_interim_plan")
    private String annualInterimPlan;

    /**
     * 计划投资额
     */
    @Excel(name = "计划投资额")
    private BigDecimal amount;

    /**
     * 调整后投资金额
     */
    @Excel(name = "调整后投资金额")
    private BigDecimal adjustedAmount;

}

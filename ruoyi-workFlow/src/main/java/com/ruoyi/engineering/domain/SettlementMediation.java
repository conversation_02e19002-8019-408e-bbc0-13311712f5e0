package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.alibaba.excel.annotation.*;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.domain.SysDept;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.activiti.domain.ProcessEntity;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_settlement_mediation")
public class SettlementMediation extends ProcessEntity<SettlementMediation> {
    /**
     * 年度
     */
    @Excel(name = "年度", sort = 3)
    @ExcelProperty("年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 项目ID
     */
    //@Excel(name = "项目ID")
    @ExcelProperty("项目ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码", sort = 1)
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称", sort = 2)
    @ExcelProperty("项目名称")
    @FieldMapping(tag = ProjectImplementApply.class, thisField = BidDocument.Fields.projectCode, joinField = ProjectImplementApply.Fields.projectCode, select = "projectName")
    private String projectName;

    /**
     * 是否有中介审核 0：否，1：是，默认0
     */
    @Excel(name = "是否有中介审核 0：否，1：是，默认0")
    @ExcelProperty("是否有中介审核 0：否，1：是，默认0")
    private String mediationFlag;

    /**
     * 附件
     */
//    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;
    @Excel(name = "部门名称", sort = 4)
    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    /**
     * 实际完成日期
     */
//    @Excel(name = "实际完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;


    @Excel(name = "申请人", sort = 5)
    @ExcelProperty("申请人")
    protected String applyUserName;
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("申请时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    protected Date applyTime;

    /**
     * 解锁标志 0：未解锁，1：已解锁，默认0
     */
    private String unlockFlag;

}

package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;

import java.util.List;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;

import com.alibaba.excel.annotation.*;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.domain.SysDept;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.activiti.domain.ProcessEntity;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_construction_progress_feedback")
public class ConstructionProgressFeedback extends ProcessEntity<ConstructionProgressFeedback> {
    /**
     * 项目ID
     */
    @Excel(name = "项目ID")
    @ExcelProperty("项目ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long projectId;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @ExcelProperty("项目名称")
    @FieldMapping(tag = ProjectImplementApply.class, thisField = ConstructionProgressFeedback.Fields.projectCode, joinField = ProjectImplementApply.Fields.projectCode, select = "projectName")
    private String projectName;

    /**
     * 年度
     */
    @Excel(name = "年度")
    @ExcelProperty("年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 部门名称
     */
    @Excel(name = "部门名称")
    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    /**
     * 实际完成日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "实际完成日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("实际完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;

    /**
     * 附件
     */
    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @TableField(exist = false)
    private Date endTime;

    @TableField(exist = false)
    @EntityMapping(joinField = ConstructionProgressFeedbackSub.Fields.parentId)
    private List<ConstructionProgressFeedbackSub> constructionProgressFeedbackSubList = new ArrayList<>();

    /**
     * 解锁标志
     */
    private String unlockFlag;
}

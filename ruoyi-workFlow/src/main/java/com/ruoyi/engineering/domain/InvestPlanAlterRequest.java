package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.github.yulichang.annotation.EntityMapping;

import java.math.BigDecimal;
import java.util.List;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.ArrayList;

import com.alibaba.excel.annotation.*;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.common.domain.SysDept;
import lombok.*;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.activiti.domain.ProcessEntity;
import lombok.experimental.FieldNameConstants;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_invest_plan_alter_request")
public class InvestPlanAlterRequest extends ProcessEntity<InvestPlanAlterRequest> {
    /**
     * 年度
     */
    @Excel(name = "年度")
    @ExcelProperty("年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 投资分管部门
     */
    @Excel(name = "投资分管部门")
    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    /**
     * 投资状态
     */
    @Excel(name = "投资状态")
    @ExcelProperty("投资状态")
    private String investStatusValue;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    @ExcelProperty("项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @ExcelProperty("项目名称")
    private String projectName;

    /**
     * 附件
     */
    @Excel(name = "附件")
    @ExcelProperty("附件")
    private String attachment;

    /**
     * 合计计划投资额(万元)
     */
    @Excel(name = "合计计划投资额(万元)")
    @ExcelProperty("合计计划投资额(万元)")
    private BigDecimal totalPlanAmount;

    /**
     * 实际完成日期
     */
    @DateTimeFormat("yyyy-MM-dd")
    @Excel(name = "实际完成日期", width = 30, dateFormat = "yyyy-MM-dd")
    @ExcelProperty("实际完成日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;

    /**
     * 投资计划类别
     */
    @Excel(name = "投资计划类别")
    @ExcelProperty("投资计划类别")
    private String planTypeValue;

    /**
     * 变更原因
     */
    @Excel(name = "变更原因")
    @ExcelProperty("变更原因")
    private String alterReason;

    /**
     * 原始数据
     */
    @Excel(name = "原始数据")
    @ExcelProperty("原始数据")
    private String originalData;

    @TableField(exist = false)
    @EntityMapping(joinField = InvestPlanAlterRequestSub.Fields.parentId)
    private List<InvestPlanAlterRequestSub> investPlanAlterRequestSubList = new ArrayList<>();


    private Long originalInvestPlanId;

    /**
     * 年度计划/临时计划
     */
    @Excel(name = "年度计划/临时计划", dictType = "annual_interim_plan")
    private String annualInterimPlan;

    @TableField(exist = false)
    private BigDecimal adjustedAmount;
}

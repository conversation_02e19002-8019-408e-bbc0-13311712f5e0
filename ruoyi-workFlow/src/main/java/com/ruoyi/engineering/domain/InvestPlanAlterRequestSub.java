package com.ruoyi.engineering.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.*;
import lombok.experimental.FieldNameConstants;

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_invest_plan_alter_request_sub")
public class InvestPlanAlterRequestSub extends BaseEntity<InvestPlanAlterRequestSub> {

    /**
     * 父ID
     */
    @Excel(name = "父ID")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long parentId;

    /**
     * 类型
     */
    @Excel(name = "类型")
    private String type;

    /**
     * 计划金额(万元)
     */
    @Excel(name = "计划金额(万元)")
    private BigDecimal planAmount;

    /**
     * 登记日期
     */
    @Excel(name = "登记日期", width = 30, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date registerDate;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    private String projectCode;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    @TableField(condition = SqlCondition.LIKE)
    private String projectName;

    /**
     * 项目主管部门id
     */
    @Excel(name = "项目主管部门id")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long deptId;

    /**
     * 审定金额
     */
    @Excel(name = "审定金额")
    private BigDecimal validateAmount;

    /**
     * 审定状态
     */
    @Excel(name = "审定状态")
    private String validateStatusValue;

}

package com.ruoyi.engineering.domain;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.github.yulichang.annotation.FieldMapping;
import com.ruoyi.activiti.domain.ProcessEntity;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.SysDept;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

import java.math.BigDecimal;
import java.util.Date;

@ExcelIgnoreUnannotated
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@FieldNameConstants
@TableName("biz_project_implement_apply")
public class ProjectImplementApply extends ProcessEntity<ProjectImplementApply> {

    /**
     * 年度
     */
    @Excel(name = "年度")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long year;

    /**
     * 部门名称
     */
    @Excel(name = "部门")
    @FieldMapping(tag = SysDept.class, thisField = ProcessEntity.Fields.deptId, select = SysDept.Fields.deptName)
    @TableField(exist = false)
    private String deptName;

    /**
     * 项目名称
     */
    @Excel(name = "项目名称")
    private String projectName;

    /**
     * 投资计划id
     */
    private Long investPlanId ;

    /**
     * 项目编码
     */
    @Excel(name = "项目编码")
    private String projectCode;

    /**
     * 列支渠道
     */
    @Excel(name = "列支渠道（年度计划）", dictType = "invest_status", width = 25)
    private String listChannelValue;

    /**
     * 计划投资额(万元)
     */
    @Excel(name = "本项目计划投资额(万元)")
    private BigDecimal planInvestAmount;

    /**
     * 投资计划类别
     */
    @Excel(name = "投资计划类别", dictType = "invest_plan_type")
    private String planTypeValue;

    /**
     * 实施理由
     */
    @Excel(name = "实施理由")
    private String implementReason;

    /**
     * 主要内容
     */
    @Excel(name = "主要内容")
    private String primaryConte;

    /**
     * 项目主管单位
     */
    @Excel(name = "项目主管单位")
    private String projectManageUnit;

    /**
     * 专业管理单位
     */
    @Excel(name = "专业管理单位")
    private String professionalManageUnit;

    /**
     * 拟完成时间
     */
    @Excel(name = "拟完成时间", width = 11, dateFormat = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date proposeCompleteDate;

    /**
     * 附件
     */
    private String attachment;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    private String belongProjectName;

    private BigDecimal belongPlanInvestAmount;

    private Long applicantUserId;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyDate;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date actualCompleteDate;

    /**
     * 解锁标志
     */
    private String unlockFlag;

    private String isJumpControlPrice;

    @TableField(exist = false)
    private Long projectId;
//    @TableField(exist = false)
//    private String planTypeLabel;
//    @TableField(exist = false)
//    private String statusLabel;
}

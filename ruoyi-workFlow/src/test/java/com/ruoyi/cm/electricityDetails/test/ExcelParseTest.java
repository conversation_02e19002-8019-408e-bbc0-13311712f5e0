package com.ruoyi.cm.electricityDetails.test;

import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

/**
 * 独立测试Excel导入功能
 * 这个类可以单独运行，不依赖于Spring容器或数据库
 */
public class ExcelParseTest {

    public static void main(String[] args) {
        try {
            System.out.println("开始测试Excel导入功能...");

            // 指定Excel文件路径 - 根据实际情况修改
            String excelPath = "excel/电力成本表样.xlsx";
            File excelFile = new File(excelPath);

            if (!excelFile.exists()) {
                System.err.println("测试文件不存在: " + excelFile.getAbsolutePath());
                return;
            }

            System.out.println("找到Excel文件: " + excelFile.getAbsolutePath());

            try (FileInputStream fis = new FileInputStream(excelFile)) {
                // 读取Excel文件
                Workbook workbook = WorkbookFactory.create(fis);

                // 解析Excel
                List<CmElectricityDetails> list = parseExcelWithMergedCells(workbook);

                System.out.println("\n===== 解析结果 =====");
                System.out.println("总行数: " + list.size());
                // 打印所有数据
                int count = 0;
                for (CmElectricityDetails detail : list) {
                    count++;
                    System.out.println("数据行 #" + count + ": 部门=[" + detail.getDeptName()
                            + "], 项目=[" + detail.getProjectName() + "]"
                            + (detail.getElectricityConsumption() != null
                                    ? ", 耗电量=" + detail.getElectricityConsumption()
                                    : "")
                            + (detail.getUnitPrice() != null ? ", 单价=" + detail.getUnitPrice() : "")
                            + (detail.getAmount() != null ? ", 金额=" + detail.getAmount() : ""));
                }

                System.out.println("Excel导入测试完成");
            }
        } catch (Exception e) {
            System.err.println("测试Excel导入时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从Workbook中解析电耗明细数据，处理合并单元格
     */
    private static List<CmElectricityDetails> parseExcelWithMergedCells(Workbook workbook) {
        List<CmElectricityDetails> resultList = new ArrayList<>();
        Sheet sheet = workbook.getSheetAt(0);

        // 获取表头行索引
        int headerRowIndex = findHeaderRowIndex(sheet);
        if (headerRowIndex == -1) {
            System.out.println("找不到表头行");
            return resultList; // 找不到表头，返回空列表
        }

        System.out.println("找到表头行，索引: " + headerRowIndex);

        // 解析表头，获取列索引映射
        Map<String, Integer> headerMap = parseHeaders(sheet, headerRowIndex);
        System.out.println("表头映射: " + headerMap);

        // 创建合并单元格值的映射
        Map<String, String> mergedRegionValues = new HashMap<>();
        List<CellRangeAddress> mergedRegions = sheet.getMergedRegions();
        System.out.println("合并单元格数量: " + mergedRegions.size());

        // 打印所有合并区域的信息
        System.out.println("\n===== 合并区域信息 =====");
        for (int i = 0; i < mergedRegions.size(); i++) {
            CellRangeAddress region = mergedRegions.get(i);
            System.out.println("合并区域 #" + i + ": 从(" + region.getFirstRow() + "," + region.getFirstColumn()
                    + ")到(" + region.getLastRow() + "," + region.getLastColumn() + ")");

            // 打印合并区域的左上角单元格的值
            Row firstRow = sheet.getRow(region.getFirstRow());
            if (firstRow != null) {
                Cell firstCell = firstRow.getCell(region.getFirstColumn());
                if (firstCell != null) {
                    System.out.println("  值: " + firstCell.toString());
                }
            }
        }

        System.out.println("\n===== 处理数据行 =====");

        // 处理数据行，只获取表格内数据，表头行之后到非空行之前
        for (int i = headerRowIndex + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            CmElectricityDetails details = new CmElectricityDetails();

            // 设置部门
            Integer deptColIndex = headerMap.get("部门");
            if (deptColIndex != null) {
                String deptName = getCellValueFromMergedRegion(sheet, i, deptColIndex, mergedRegions,
                        mergedRegionValues);
                details.setDeptName(deptName);
            }

            // 设置项目
            Integer projectColIndex = headerMap.get("项目");
            if (projectColIndex != null) {
                String projectName = getCellValueFromMergedRegion(sheet, i, projectColIndex, mergedRegions,
                        mergedRegionValues);
                details.setProjectName(projectName);
            }

            // 设置耗电量
            Integer consumptionColIndex = headerMap.get("耗电量(kwh)");
            if (consumptionColIndex != null) {
                Cell cell = row.getCell(consumptionColIndex);
                if (cell != null) {
                    details.setElectricityConsumption(getBigDecimalValue(cell));
                }
            }

            // 设置单价 - 匹配实际表头中的中文括号
            Integer priceColIndex = headerMap.get("单价（元/kwh)");
            if (priceColIndex != null) {
                Cell cell = row.getCell(priceColIndex);
                if (cell != null) {
                    details.setUnitPrice(getBigDecimalValue(cell));
                }
            }

            // 设置金额 - 匹配实际表头中的中文括号
            Integer amountColIndex = headerMap.get("金额(元）");
            if (amountColIndex != null) {
                Cell cell = row.getCell(amountColIndex);
                if (cell != null) {
                    details.setAmount(getBigDecimalValue(cell));
                }
            }

            // 只要有部门或项目，就认为是有效数据
            if (isNotBlank(details.getDeptName()) || isNotBlank(details.getProjectName())) {
                // 判断是否已到表格底部（如"制表人"、"部门负责人"等行）
                if (details.getDeptName() != null && details.getDeptName().contains("负责人")) {
                    System.out.println("检测到表格结束标记: " + details.getDeptName());
                    break;
                }

                resultList.add(details);
            }
        }

        return resultList;
    }

    /**
     * 查找表头行的索引
     */
    private static int findHeaderRowIndex(Sheet sheet) {
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null)
                continue;

            // 检查是否包含特定的表头列名
            for (int j = 0; j < row.getLastCellNum(); j++) {
                Cell cell = row.getCell(j);
                if (cell != null) {
                    String value = cell.toString().trim();
                    if ("部门".equals(value) || "项目".equals(value) || "耗电量(kwh)".equals(value)) {
                        return i;
                    }
                }
            }
        }
        return -1; // 找不到表头
    }

    /**
     * 解析表头，建立列名与列索引的映射
     */
    private static Map<String, Integer> parseHeaders(Sheet sheet, int headerRowIndex) {
        Map<String, Integer> headerMap = new HashMap<>();
        Row headerRow = sheet.getRow(headerRowIndex);

        if (headerRow != null) {
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String headerName = cell.toString().trim();
                    headerMap.put(headerName, i);
                }
            }
        }

        return headerMap;
    }

    /**
     * 获取单元格的值，考虑合并单元格的情况
     */
    private static String getCellValueFromMergedRegion(Sheet sheet, int rowIndex, int colIndex,
            List<CellRangeAddress> mergedRegions, Map<String, String> mergedRegionValues) {
        // 检查是否是合并单元格
        for (CellRangeAddress region : mergedRegions) {
            if (region.isInRange(rowIndex, colIndex)) {
                // 如果是合并单元格，使用缓存或者获取合并区域左上角单元格的值
                String regionKey = region.getFirstRow() + ":" + region.getFirstColumn();
                if (mergedRegionValues.containsKey(regionKey)) {
                    return mergedRegionValues.get(regionKey);
                } else {
                    Row firstRow = sheet.getRow(region.getFirstRow());
                    if (firstRow != null) {
                        Cell firstCell = firstRow.getCell(region.getFirstColumn());
                        if (firstCell != null) {
                            String value = firstCell.toString().trim();
                            mergedRegionValues.put(regionKey, value);
                            return value;
                        }
                    }
                }
                return "";
            }
        }

        // 如果不是合并单元格，直接获取单元格的值
        Row row = sheet.getRow(rowIndex);
        if (row != null) {
            Cell cell = row.getCell(colIndex);
            if (cell != null) {
                return cell.toString().trim();
            }
        }

        return "";
    }

    /**
     * 从单元格获取BigDecimal值
     */
    private static BigDecimal getBigDecimalValue(Cell cell) {
        if (cell == null)
            return null;

        try {
            switch (cell.getCellType()) {
                case NUMERIC:
                    return BigDecimal.valueOf(cell.getNumericCellValue());
                case STRING:
                    String value = cell.getStringCellValue().trim();
                    try {
                        return new BigDecimal(value);
                    } catch (NumberFormatException e) {
                        // 忽略非数字格式错误
                        return null;
                    }
                default:
                    return null;
            }
        } catch (Exception e) {
            // 忽略所有单元格访问错误
            return null;
        }
    }

    /**
     * 检查字符串是否不为空
     */
    private static boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }
}
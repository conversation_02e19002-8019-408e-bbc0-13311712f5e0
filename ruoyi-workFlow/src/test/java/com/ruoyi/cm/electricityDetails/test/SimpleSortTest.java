package com.ruoyi.cm.electricityDetails.test;

import java.math.BigDecimal;
import java.util.*;

/**
 * 简化的排序测试类
 * 不依赖复杂的继承关系，使用简单的数据结构来测试排序逻辑
 */
public class SimpleSortTest {
    
    /**
     * 简化的电耗明细数据类
     */
    static class SimpleElectricityDetail {
        private Long id;
        private String deptName;
        private String projectName;
        private BigDecimal amount;
        private Date createTime;
        private String createBy;
        
        public SimpleElectricityDetail(String deptName, String projectName, BigDecimal amount) {
            this.deptName = deptName;
            this.projectName = projectName;
            this.amount = amount;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        
        public String getDeptName() { return deptName; }
        public void setDeptName(String deptName) { this.deptName = deptName; }
        
        public String getProjectName() { return projectName; }
        public void setProjectName(String projectName) { this.projectName = projectName; }
        
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        
        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }
        
        public String getCreateBy() { return createBy; }
        public void setCreateBy(String createBy) { this.createBy = createBy; }
        
        @Override
        public String toString() {
            return String.format("ID=%d, 部门=%s, 项目=%s, 金额=%s, 时间=%s, 操作人=%s",
                id, deptName, projectName, amount,
                createTime != null ? new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(createTime) : "null",
                createBy);
        }
    }
    
    public static void main(String[] args) {
        System.out.println("=== 简化版排序功能测试 ===\n");
        
        // 测试原始问题场景
        testOriginalProblem();
        
        System.out.println("\n" + "=".repeat(80) + "\n");
        
        // 测试解决方案
        testSolution();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 测试原始问题：批量添加时创建时间相同，排序不稳定
     */
    private static void testOriginalProblem() {
        System.out.println("【原始问题测试】批量添加时创建时间相同导致排序混乱");
        
        // 创建Excel中的原始数据顺序
        List<SimpleElectricityDetail> excelData = Arrays.asList(
            new SimpleElectricityDetail("财务部", "办公照明", new BigDecimal("150.50")),
            new SimpleElectricityDetail("财务部", "空调制冷", new BigDecimal("280.00")),
            new SimpleElectricityDetail("技术部", "服务器用电", new BigDecimal("450.80")),
            new SimpleElectricityDetail("技术部", "开发设备", new BigDecimal("120.30")),
            new SimpleElectricityDetail("人事部", "办公设备", new BigDecimal("95.20"))
        );
        
        System.out.println("Excel中的原始顺序：");
        printDataWithIndex(excelData);
        
        // 模拟批量插入：ID自增，但创建时间相同
        Date sameTime = new Date();
        for (int i = 0; i < excelData.size(); i++) {
            SimpleElectricityDetail item = excelData.get(i);
            item.setId((long) (i + 1));
            item.setCreateTime(sameTime); // 相同的创建时间！
            item.setCreateBy("张三");
        }
        
        // 使用原来的排序逻辑（只按ID降序）
        List<SimpleElectricityDetail> sortedByIdDesc = new ArrayList<>(excelData);
        sortedByIdDesc.sort((a, b) -> Long.compare(b.getId(), a.getId()));
        
        System.out.println("\n原来的排序结果（ID降序）：");
        printDataWithIndex(sortedByIdDesc);
        
        System.out.println("\n❌ 问题：Excel第1行（财务部-办公照明）变成了最后一行！");
        System.out.println("   用户期望：最新导入的数据在前面，但同批次内要保持Excel原始顺序");
    }
    
    /**
     * 测试解决方案：创建时间微调 + 新排序逻辑
     */
    private static void testSolution() {
        System.out.println("【解决方案测试】创建时间微调 + 新排序逻辑");
        
        // 模拟多批次数据导入
        List<SimpleElectricityDetail> allData = new ArrayList<>();
        
        // 第一批数据（10秒前导入）
        long firstBatchTime = System.currentTimeMillis() - 10000;
        List<SimpleElectricityDetail> firstBatch = Arrays.asList(
            new SimpleElectricityDetail("财务部", "办公照明", new BigDecimal("150.50")),
            new SimpleElectricityDetail("财务部", "空调制冷", new BigDecimal("280.00")),
            new SimpleElectricityDetail("财务部", "电脑设备", new BigDecimal("95.30"))
        );
        
        // 为第一批数据设置ID和创建时间（每条记录间隔1毫秒）
        for (int i = 0; i < firstBatch.size(); i++) {
            SimpleElectricityDetail item = firstBatch.get(i);
            item.setId((long) (i + 1));
            item.setCreateTime(new Date(firstBatchTime + i)); // 关键：时间递增
            item.setCreateBy("张三");
        }
        allData.addAll(firstBatch);
        
        // 第二批数据（刚刚导入）
        long secondBatchTime = System.currentTimeMillis();
        List<SimpleElectricityDetail> secondBatch = Arrays.asList(
            new SimpleElectricityDetail("技术部", "服务器用电", new BigDecimal("450.80")),
            new SimpleElectricityDetail("技术部", "开发设备", new BigDecimal("120.30")),
            new SimpleElectricityDetail("技术部", "测试环境", new BigDecimal("85.60"))
        );
        
        // 为第二批数据设置ID和创建时间
        for (int i = 0; i < secondBatch.size(); i++) {
            SimpleElectricityDetail item = secondBatch.get(i);
            item.setId((long) (firstBatch.size() + i + 1));
            item.setCreateTime(new Date(secondBatchTime + i)); // 关键：时间递增
            item.setCreateBy("李四");
        }
        allData.addAll(secondBatch);
        
        System.out.println("所有数据（按导入顺序）：");
        printDataWithIndex(allData);
        
        // 应用新的排序逻辑
        List<SimpleElectricityDetail> sortedData = new ArrayList<>(allData);
        sortedData.sort((a, b) -> {
            // 首先按创建时间降序（最新批次在前）
            if (a.getCreateTime() != null && b.getCreateTime() != null) {
                int timeCompare = b.getCreateTime().compareTo(a.getCreateTime());
                if (timeCompare != 0) {
                    return timeCompare;
                }
            }
            // 同一批次内按ID升序（保持Excel原始顺序）
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(a.getId(), b.getId());
            }
            return 0;
        });
        
        System.out.println("\n新排序逻辑的结果（创建时间降序 + ID升序）：");
        printDataWithIndex(sortedData);
        
        // 验证结果
        validateResult(sortedData);
    }
    
    /**
     * 验证排序结果是否符合预期
     */
    private static void validateResult(List<SimpleElectricityDetail> sortedData) {
        System.out.println("\n【结果验证】");
        
        // 检查第二批数据是否在前面
        boolean secondBatchFirst = sortedData.get(0).getCreateBy().equals("李四");
        System.out.println("✓ 最新批次（李四）在前面: " + (secondBatchFirst ? "通过" : "失败"));
        
        // 检查每个批次内部的顺序
        Map<String, List<SimpleElectricityDetail>> batches = new HashMap<>();
        for (SimpleElectricityDetail item : sortedData) {
            batches.computeIfAbsent(item.getCreateBy(), k -> new ArrayList<>()).add(item);
        }
        
        boolean batchOrderCorrect = true;
        for (Map.Entry<String, List<SimpleElectricityDetail>> entry : batches.entrySet()) {
            List<SimpleElectricityDetail> batch = entry.getValue();
            for (int i = 1; i < batch.size(); i++) {
                if (batch.get(i).getId() <= batch.get(i-1).getId()) {
                    batchOrderCorrect = false;
                    break;
                }
            }
        }
        System.out.println("✓ 批次内保持Excel原始顺序: " + (batchOrderCorrect ? "通过" : "失败"));
        
        if (secondBatchFirst && batchOrderCorrect) {
            System.out.println("\n🎉 解决方案测试通过！");
            System.out.println("   ✅ 最新导入的数据显示在前面");
            System.out.println("   ✅ 每批次内部保持Excel的原始顺序");
            System.out.println("   ✅ 用户看到的顺序符合预期");
        } else {
            System.out.println("\n❌ 解决方案需要进一步调整");
        }
    }
    
    /**
     * 打印数据列表（带序号）
     */
    private static void printDataWithIndex(List<SimpleElectricityDetail> dataList) {
        for (int i = 0; i < dataList.size(); i++) {
            System.out.printf("  %d. %s%n", i + 1, dataList.get(i));
        }
    }
}

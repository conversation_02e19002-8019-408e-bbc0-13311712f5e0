package com.ruoyi.cm.electricityDetails.test;

import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 模拟数据库操作的测试类
 * 创建一个内存中的"数据表"来测试批量插入和排序功能
 * 不需要真实的数据库连接
 */
public class MockDatabaseTest {
    
    // 模拟数据库表
    private static final List<CmElectricityDetails> MOCK_TABLE = new ArrayList<>();
    // 模拟自增ID
    private static final AtomicLong ID_GENERATOR = new AtomicLong(1);
    
    public static void main(String[] args) {
        System.out.println("=== 开始模拟数据库批量插入和排序测试 ===\n");
        
        // 清空模拟表
        clearTable();
        
        // 测试场景1：第一批数据导入
        System.out.println("【场景1：导入第一批Excel数据】");
        List<CmElectricityDetails> firstBatch = createFirstBatchData();
        batchInsertWithTimeGap(firstBatch, "张三");
        
        System.out.println("第一批数据导入后的表格内容：");
        displayTable();
        
        // 等待一段时间，模拟不同时间的导入
        try {
            Thread.sleep(100); // 等待100毫秒
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        // 测试场景2：第二批数据导入
        System.out.println("\n【场景2：导入第二批Excel数据】");
        List<CmElectricityDetails> secondBatch = createSecondBatchData();
        batchInsertWithTimeGap(secondBatch, "李四");
        
        System.out.println("第二批数据导入后的表格内容：");
        displayTable();
        
        // 测试场景3：查询并排序
        System.out.println("\n【场景3：查询数据并按新逻辑排序】");
        List<CmElectricityDetails> queryResult = queryWithSort();
        
        System.out.println("查询结果（按创建时间降序，ID升序）：");
        displayDataList(queryResult);
        
        // 验证排序结果
        validateSortResult(queryResult);
        
        System.out.println("\n=== 模拟数据库测试完成 ===");
    }
    
    /**
     * 清空模拟表
     */
    private static void clearTable() {
        MOCK_TABLE.clear();
        ID_GENERATOR.set(1);
        System.out.println("模拟数据表已清空");
    }
    
    /**
     * 批量插入数据（带时间间隔）
     */
    private static void batchInsertWithTimeGap(List<CmElectricityDetails> dataList, String operatorName) {
        long baseTime = System.currentTimeMillis();
        
        System.out.println("开始批量插入 " + dataList.size() + " 条数据...");
        
        for (int i = 0; i < dataList.size(); i++) {
            CmElectricityDetails item = dataList.get(i);
            
            // 设置自增ID
            item.setId(ID_GENERATOR.getAndIncrement());
            
            // 设置创建时间（每条记录间隔1毫秒）
            item.setCreateTime(new Date(baseTime + i));
            item.setUpdateTime(new Date(baseTime + i));
            
            // 设置操作人
            item.setCreateBy(operatorName);
            item.setUpdateBy(operatorName);
            
            // 插入到模拟表中
            MOCK_TABLE.add(item);
            
            System.out.println("  插入数据: ID=" + item.getId() + 
                             ", 部门=" + item.getDeptName() + 
                             ", 项目=" + item.getProjectName() +
                             ", 时间=" + new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(item.getCreateTime()));
        }
        
        System.out.println("批量插入完成，共插入 " + dataList.size() + " 条数据");
    }
    
    /**
     * 查询数据并排序
     */
    private static List<CmElectricityDetails> queryWithSort() {
        // 复制数据（模拟从数据库查询）
        List<CmElectricityDetails> result = new ArrayList<>(MOCK_TABLE);
        
        // 应用排序逻辑
        result.sort((a, b) -> {
            // 首先按创建时间降序排序（最新的批次在前面）
            if (a.getCreateTime() != null && b.getCreateTime() != null) {
                int timeCompare = b.getCreateTime().compareTo(a.getCreateTime());
                if (timeCompare != 0) {
                    return timeCompare;
                }
            }
            
            // 同一批次内按ID升序排序（保证Excel原始顺序）
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(a.getId(), b.getId());
            }
            
            return 0;
        });
        
        return result;
    }
    
    /**
     * 显示整个表格内容
     */
    private static void displayTable() {
        System.out.println("当前表格内容（共 " + MOCK_TABLE.size() + " 条记录）：");
        displayDataList(MOCK_TABLE);
    }
    
    /**
     * 显示数据列表
     */
    private static void displayDataList(List<CmElectricityDetails> dataList) {
        if (dataList.isEmpty()) {
            System.out.println("  （无数据）");
            return;
        }
        
        System.out.println("  序号 | ID | 部门     | 项目         | 金额    | 创建时间      | 操作人");
        System.out.println("  " + "-".repeat(70));
        
        for (int i = 0; i < dataList.size(); i++) {
            CmElectricityDetails item = dataList.get(i);
            System.out.printf("  %-4d | %-2d | %-8s | %-12s | %-7s | %-13s | %s%n",
                i + 1,
                item.getId(),
                item.getDeptName(),
                item.getProjectName(),
                item.getAmount(),
                item.getCreateTime() != null ? 
                    new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(item.getCreateTime()) : "null",
                item.getCreateBy()
            );
        }
    }
    
    /**
     * 验证排序结果
     */
    private static void validateSortResult(List<CmElectricityDetails> sortedData) {
        System.out.println("\n【排序结果验证】");
        
        // 检查是否最新的批次在前面
        boolean isNewestFirst = true;
        Date previousTime = null;
        String currentBatch = null;
        
        for (CmElectricityDetails item : sortedData) {
            if (previousTime != null && item.getCreateTime().after(previousTime)) {
                isNewestFirst = false;
                break;
            }
            
            // 检查同一批次内的ID顺序
            if (currentBatch == null || !currentBatch.equals(item.getCreateBy())) {
                currentBatch = item.getCreateBy();
            }
            
            previousTime = item.getCreateTime();
        }
        
        System.out.println("✓ 最新批次在前面: " + (isNewestFirst ? "通过" : "失败"));
        
        // 检查同一批次内的顺序
        Map<String, List<CmElectricityDetails>> batchGroups = new HashMap<>();
        for (CmElectricityDetails item : sortedData) {
            batchGroups.computeIfAbsent(item.getCreateBy(), k -> new ArrayList<>()).add(item);
        }
        
        boolean batchOrderCorrect = true;
        for (Map.Entry<String, List<CmElectricityDetails>> entry : batchGroups.entrySet()) {
            List<CmElectricityDetails> batchData = entry.getValue();
            for (int i = 1; i < batchData.size(); i++) {
                if (batchData.get(i).getId() < batchData.get(i-1).getId()) {
                    batchOrderCorrect = false;
                    break;
                }
            }
        }
        
        System.out.println("✓ 批次内ID升序: " + (batchOrderCorrect ? "通过" : "失败"));
        
        if (isNewestFirst && batchOrderCorrect) {
            System.out.println("🎉 排序功能测试通过！数据显示顺序符合预期。");
        } else {
            System.out.println("❌ 排序功能测试失败！需要检查排序逻辑。");
        }
    }
    
    /**
     * 创建第一批测试数据
     */
    private static List<CmElectricityDetails> createFirstBatchData() {
        return Arrays.asList(
            createDetail("财务部", "办公照明", new BigDecimal("150.50")),
            createDetail("财务部", "空调制冷", new BigDecimal("280.00")),
            createDetail("财务部", "电脑设备", new BigDecimal("95.30"))
        );
    }
    
    /**
     * 创建第二批测试数据
     */
    private static List<CmElectricityDetails> createSecondBatchData() {
        return Arrays.asList(
            createDetail("技术部", "服务器机房", new BigDecimal("450.80")),
            createDetail("技术部", "开发设备", new BigDecimal("120.30")),
            createDetail("技术部", "测试环境", new BigDecimal("85.60")),
            createDetail("技术部", "网络设备", new BigDecimal("200.40"))
        );
    }
    
    /**
     * 创建电耗明细对象
     */
    private static CmElectricityDetails createDetail(String deptName, String projectName, BigDecimal amount) {
        CmElectricityDetails detail = new CmElectricityDetails();
        detail.setDeptName(deptName);
        detail.setProjectName(projectName);
        detail.setAmount(amount);
        detail.setElectricityConsumption(amount.divide(new BigDecimal("0.8"), 2, BigDecimal.ROUND_HALF_UP));
        detail.setUnitPrice(new BigDecimal("0.8"));
        detail.setAccountPeriodId(202401L);
        return detail;
    }
}

package com.ruoyi.cm.electricityDetails.test;

import com.ruoyi.cm.electricityDetails.domain.CmElectricityDetails;

import java.math.BigDecimal;
import java.util.*;

/**
 * 独立测试排序功能
 * 模拟批量添加数据时的排序问题和解决方案
 * 这个类可以单独运行，不依赖于Spring容器或数据库
 */
public class SortOrderTest {

    public static void main(String[] args) {
        System.out.println("=== 开始测试电耗明细数据排序功能 ===\n");
        
        // 测试场景1：模拟问题场景 - 批量添加时创建时间相同
        testProblemScenario();
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // 测试场景2：模拟解决方案 - 创建时间有微小差异
        testSolutionScenario();
        
        System.out.println("\n=== 排序功能测试完成 ===");
    }

    /**
     * 测试问题场景：批量添加时创建时间相同导致排序不稳定
     */
    private static void testProblemScenario() {
        System.out.println("【测试场景1：问题场景 - 批量添加时创建时间相同】");
        
        // 模拟批量添加的数据（Excel中的顺序）
        List<CmElectricityDetails> batchData = createBatchData();
        
        // 模拟数据库保存后的状态（ID自增，但创建时间相同）
        Date sameCreateTime = new Date();
        for (int i = 0; i < batchData.size(); i++) {
            CmElectricityDetails item = batchData.get(i);
            item.setId((long) (i + 1)); // 模拟自增ID
            item.setCreateTime(sameCreateTime); // 相同的创建时间
        }
        
        System.out.println("原始数据（Excel中的顺序）：");
        printDataList(batchData);
        
        // 模拟原来的排序逻辑（只按ID降序）
        List<CmElectricityDetails> sortedByIdDesc = new ArrayList<>(batchData);
        sortedByIdDesc.sort((a, b) -> {
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(b.getId(), a.getId()); // ID降序
            }
            return 0;
        });
        
        System.out.println("\n按ID降序排序后（原来的逻辑）：");
        printDataList(sortedByIdDesc);
        System.out.println("问题：Excel中的第1行数据变成了最后一行！");
    }

    /**
     * 测试解决方案：创建时间有微小差异，保持Excel原始顺序
     */
    private static void testSolutionScenario() {
        System.out.println("【测试场景2：解决方案 - 创建时间有微小差异】");
        
        // 模拟批量添加的数据
        List<CmElectricityDetails> batchData = createBatchData();
        
        // 模拟新的批量插入逻辑（创建时间有微小差异）
        long baseTime = System.currentTimeMillis();
        for (int i = 0; i < batchData.size(); i++) {
            CmElectricityDetails item = batchData.get(i);
            item.setId((long) (i + 1)); // 模拟自增ID
            item.setCreateTime(new Date(baseTime + i)); // 每条记录间隔1毫秒
        }
        
        System.out.println("原始数据（Excel中的顺序）：");
        printDataList(batchData);
        
        // 模拟新的排序逻辑（先按创建时间降序，再按ID升序）
        List<CmElectricityDetails> sortedByNewLogic = new ArrayList<>(batchData);
        sortedByNewLogic.sort((a, b) -> {
            // 首先按创建时间降序排序（最新的在前面）
            if (a.getCreateTime() != null && b.getCreateTime() != null) {
                int timeCompare = b.getCreateTime().compareTo(a.getCreateTime());
                if (timeCompare != 0) {
                    return timeCompare;
                }
            }
            // 如果创建时间相同，按ID升序排序（保证Excel原始顺序）
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(a.getId(), b.getId());
            }
            return 0;
        });
        
        System.out.println("\n按新逻辑排序后（创建时间降序 + ID升序）：");
        printDataList(sortedByNewLogic);
        System.out.println("解决方案：Excel中的第1行数据仍然是第1行！");
        
        // 测试多批次数据的排序
        testMultipleBatchScenario();
    }

    /**
     * 测试多批次数据的排序场景
     */
    private static void testMultipleBatchScenario() {
        System.out.println("\n【测试场景3：多批次数据排序】");
        
        List<CmElectricityDetails> allData = new ArrayList<>();
        
        // 第一批数据（较早时间）
        long firstBatchTime = System.currentTimeMillis() - 10000; // 10秒前
        List<CmElectricityDetails> firstBatch = Arrays.asList(
            createDetail(1L, "财务部", "项目A", new BigDecimal("100"), firstBatchTime),
            createDetail(2L, "财务部", "项目B", new BigDecimal("200"), firstBatchTime + 1),
            createDetail(3L, "财务部", "项目C", new BigDecimal("300"), firstBatchTime + 2)
        );
        
        // 第二批数据（较晚时间）
        long secondBatchTime = System.currentTimeMillis();
        List<CmElectricityDetails> secondBatch = Arrays.asList(
            createDetail(4L, "技术部", "项目D", new BigDecimal("400"), secondBatchTime),
            createDetail(5L, "技术部", "项目E", new BigDecimal("500"), secondBatchTime + 1),
            createDetail(6L, "技术部", "项目F", new BigDecimal("600"), secondBatchTime + 2)
        );
        
        allData.addAll(firstBatch);
        allData.addAll(secondBatch);
        
        System.out.println("所有数据（按添加顺序）：");
        printDataList(allData);
        
        // 按新逻辑排序
        allData.sort((a, b) -> {
            // 首先按创建时间降序排序（最新批次在前面）
            if (a.getCreateTime() != null && b.getCreateTime() != null) {
                int timeCompare = b.getCreateTime().compareTo(a.getCreateTime());
                if (timeCompare != 0) {
                    return timeCompare;
                }
            }
            // 同一批次内按ID升序排序（保证Excel原始顺序）
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(a.getId(), b.getId());
            }
            return 0;
        });
        
        System.out.println("\n排序后（最新批次在前，批次内保持原始顺序）：");
        printDataList(allData);
        System.out.println("效果：第二批数据显示在前面，但每批内部保持Excel原始顺序！");
    }

    /**
     * 创建测试用的批量数据（模拟Excel中的数据）
     */
    private static List<CmElectricityDetails> createBatchData() {
        return Arrays.asList(
            createDetail(null, "财务部", "办公用电", new BigDecimal("150.5")),
            createDetail(null, "财务部", "空调用电", new BigDecimal("280.0")),
            createDetail(null, "技术部", "服务器用电", new BigDecimal("450.8")),
            createDetail(null, "技术部", "办公用电", new BigDecimal("120.3")),
            createDetail(null, "人事部", "办公用电", new BigDecimal("95.2"))
        );
    }

    /**
     * 创建单个电耗明细对象
     */
    private static CmElectricityDetails createDetail(Long id, String deptName, String projectName, BigDecimal amount) {
        CmElectricityDetails detail = new CmElectricityDetails();
        detail.setId(id);
        detail.setDeptName(deptName);
        detail.setProjectName(projectName);
        detail.setAmount(amount);
        return detail;
    }

    /**
     * 创建带时间的电耗明细对象
     */
    private static CmElectricityDetails createDetail(Long id, String deptName, String projectName, BigDecimal amount, long createTime) {
        CmElectricityDetails detail = createDetail(id, deptName, projectName, amount);
        detail.setCreateTime(new Date(createTime));
        return detail;
    }

    /**
     * 打印数据列表
     */
    private static void printDataList(List<CmElectricityDetails> dataList) {
        for (int i = 0; i < dataList.size(); i++) {
            CmElectricityDetails item = dataList.get(i);
            System.out.printf("  %d. ID=%d, 部门=%s, 项目=%s, 金额=%s, 创建时间=%s%n",
                i + 1,
                item.getId(),
                item.getDeptName(),
                item.getProjectName(),
                item.getAmount(),
                item.getCreateTime() != null ? 
                    new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(item.getCreateTime()) : "null"
            );
        }
    }
}

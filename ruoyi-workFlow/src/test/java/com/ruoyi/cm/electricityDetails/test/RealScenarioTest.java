package com.ruoyi.cm.electricityDetails.test;

import java.math.BigDecimal;
import java.util.*;

/**
 * 真实场景测试 - 重现您遇到的具体问题
 * 模拟Excel中"三甲矿区"在前面，但显示时却在后面的问题
 */
public class RealScenarioTest {
    
    /**
     * 简化的电耗明细数据类
     */
    static class ElectricityDetail {
        private Long id;
        private String deptName;
        private String projectName;
        private BigDecimal amount;
        private Date createTime;
        
        public ElectricityDetail(String deptName, String projectName, BigDecimal amount) {
            this.deptName = deptName;
            this.projectName = projectName;
            this.amount = amount;
        }
        
        // Getters and Setters
        public Long getId() { return id; }
        public void setId(Long id) { this.id = id; }
        public String getDeptName() { return deptName; }
        public String getProjectName() { return projectName; }
        public BigDecimal getAmount() { return amount; }
        public Date getCreateTime() { return createTime; }
        public void setCreateTime(Date createTime) { this.createTime = createTime; }
        
        @Override
        public String toString() {
            return String.format("ID=%-2d | %-8s | %-15s | 金额=%-6s | 时间=%s",
                id, deptName, projectName, amount,
                createTime != null ? new java.text.SimpleDateFormat("HH:mm:ss.SSS").format(createTime) : "null");
        }
    }
    
    public static void main(String[] args) {
        System.out.println("=== 真实场景测试：重现Excel排序问题 ===\n");
        
        // 重现问题场景
        demonstrateProblem();
        
        System.out.println("\n" + "=".repeat(80) + "\n");
        
        // 展示解决方案
        demonstrateSolution();
        
        System.out.println("\n=== 测试完成 ===");
    }
    
    /**
     * 重现问题：Excel中三甲矿区在前面，但显示时在后面
     */
    private static void demonstrateProblem() {
        System.out.println("【问题重现】Excel中的数据顺序 vs 实际显示顺序");
        
        // 创建Excel中的原始数据顺序（根据您的截图）
        List<ElectricityDetail> excelOrder = Arrays.asList(
            // Excel第5-11行：三甲矿区（应该在前面显示）
            new ElectricityDetail("三甲矿区", "试验其他用电", new BigDecimal("2.00")),
            new ElectricityDetail("三甲矿区", "维护用", new BigDecimal("3.00")),
            new ElectricityDetail("三甲矿区", "保洁类", new BigDecimal("4.00")),
            new ElectricityDetail("三甲矿区", "段修", new BigDecimal("5.00")),
            new ElectricityDetail("三甲矿区", "运输作业", new BigDecimal("6.00")),
            new ElectricityDetail("三甲矿区", "房外运输", new BigDecimal("7.00")),
            new ElectricityDetail("三甲矿区", "提升作业", new BigDecimal("8.00")),
            
            // Excel第16-25行：含羞园矿区
            new ElectricityDetail("含羞园矿区", "房内运输", new BigDecimal("11.00")),
            new ElectricityDetail("含羞园矿区", "通风作业", new BigDecimal("12.00")),
            new ElectricityDetail("含羞园矿区", "亮化作业", new BigDecimal("13.00")),
            
            // Excel第27-30行：运营管理部
            new ElectricityDetail("运营管理部", "金属公司总网域管理", new BigDecimal("25.00")),
            new ElectricityDetail("运营管理部", "宋家井矿区", new BigDecimal("26.00")),
            new ElectricityDetail("运营管理部", "石坑系统优化", new BigDecimal("27.00")),
            
            // Excel第32-37行：运营车间
            new ElectricityDetail("运营车间", "全铜矿处理作业", new BigDecimal("30.00")),
            new ElectricityDetail("运营车间", "车间后勤托管", new BigDecimal("31.00")),
            
            // Excel第35-38行：技术部
            new ElectricityDetail("技术部", "化验室", new BigDecimal("34.00")),
            new ElectricityDetail("技术部", "音响托管", new BigDecimal("35.00")),
            
            // Excel第38-39行：本部其他
            new ElectricityDetail("本部其他", "家用民用电", new BigDecimal("36.00")),
            new ElectricityDetail("本部其他", "行政用电", new BigDecimal("37.00"))
        );
        
        System.out.println("Excel中的原始顺序（用户期望的显示顺序）：");
        printWithIndex(excelOrder);
        
        // 模拟批量插入：ID自增，创建时间相同
        Date sameCreateTime = new Date();
        for (int i = 0; i < excelOrder.size(); i++) {
            ElectricityDetail item = excelOrder.get(i);
            item.setId((long) (i + 1)); // ID: 1,2,3,4,5...
            item.setCreateTime(sameCreateTime); // 相同的创建时间！
        }
        
        // 使用原来的排序逻辑（ID降序）
        List<ElectricityDetail> problemResult = new ArrayList<>(excelOrder);
        problemResult.sort((a, b) -> Long.compare(b.getId(), a.getId()));
        
        System.out.println("\n❌ 原来的排序结果（ID降序）：");
        printWithIndex(problemResult);
        
        System.out.println("\n🔍 问题分析：");
        System.out.println("   - Excel第5行的'三甲矿区-试验其他用电'变成了第" + 
                         (findPosition(problemResult, "三甲矿区", "试验其他用电") + 1) + "行");
        System.out.println("   - Excel第38行的'本部其他-行政用电'变成了第1行");
        System.out.println("   - 完全颠倒了用户的预期顺序！");
    }
    
    /**
     * 展示解决方案
     */
    private static void demonstrateSolution() {
        System.out.println("【解决方案】创建时间微调 + 新排序逻辑");
        
        // 重新创建数据
        List<ElectricityDetail> excelOrder = Arrays.asList(
            new ElectricityDetail("三甲矿区", "试验其他用电", new BigDecimal("2.00")),
            new ElectricityDetail("三甲矿区", "维护用", new BigDecimal("3.00")),
            new ElectricityDetail("三甲矿区", "保洁类", new BigDecimal("4.00")),
            new ElectricityDetail("三甲矿区", "段修", new BigDecimal("5.00")),
            new ElectricityDetail("三甲矿区", "运输作业", new BigDecimal("6.00")),
            new ElectricityDetail("含羞园矿区", "房内运输", new BigDecimal("11.00")),
            new ElectricityDetail("含羞园矿区", "通风作业", new BigDecimal("12.00")),
            new ElectricityDetail("运营管理部", "金属公司总网域管理", new BigDecimal("25.00")),
            new ElectricityDetail("运营管理部", "宋家井矿区", new BigDecimal("26.00")),
            new ElectricityDetail("运营车间", "全铜矿处理作业", new BigDecimal("30.00")),
            new ElectricityDetail("运营车间", "车间后勤托管", new BigDecimal("31.00")),
            new ElectricityDetail("技术部", "化验室", new BigDecimal("34.00")),
            new ElectricityDetail("技术部", "音响托管", new BigDecimal("35.00")),
            new ElectricityDetail("本部其他", "家用民用电", new BigDecimal("36.00")),
            new ElectricityDetail("本部其他", "行政用电", new BigDecimal("37.00"))
        );
        
        // 应用解决方案：创建时间微调
        long baseTime = System.currentTimeMillis();
        for (int i = 0; i < excelOrder.size(); i++) {
            ElectricityDetail item = excelOrder.get(i);
            item.setId((long) (i + 1));
            // 关键改进：每条记录的创建时间递增1毫秒
            item.setCreateTime(new Date(baseTime + i));
        }
        
        System.out.println("批量插入后的数据（带时间差异）：");
        printWithIndex(excelOrder);
        
        // 应用新的排序逻辑
        List<ElectricityDetail> solutionResult = new ArrayList<>(excelOrder);
        solutionResult.sort((a, b) -> {
            // 首先按创建时间降序（最新批次在前）
            if (a.getCreateTime() != null && b.getCreateTime() != null) {
                int timeCompare = b.getCreateTime().compareTo(a.getCreateTime());
                if (timeCompare != 0) {
                    return timeCompare;
                }
            }
            // 同一批次内按ID升序（保持Excel原始顺序）
            if (a.getId() != null && b.getId() != null) {
                return Long.compare(a.getId(), b.getId());
            }
            return 0;
        });
        
        System.out.println("\n✅ 新排序逻辑的结果：");
        printWithIndex(solutionResult);
        
        System.out.println("\n🎉 解决方案验证：");
        System.out.println("   - '三甲矿区-试验其他用电'现在是第" + 
                         (findPosition(solutionResult, "三甲矿区", "试验其他用电") + 1) + "行 ✓");
        System.out.println("   - '本部其他-行政用电'现在是第" + 
                         (findPosition(solutionResult, "本部其他", "行政用电") + 1) + "行 ✓");
        System.out.println("   - 完全保持了Excel中的原始顺序！");
    }
    
    /**
     * 查找指定数据在列表中的位置
     */
    private static int findPosition(List<ElectricityDetail> list, String deptName, String projectName) {
        for (int i = 0; i < list.size(); i++) {
            ElectricityDetail item = list.get(i);
            if (deptName.equals(item.getDeptName()) && projectName.equals(item.getProjectName())) {
                return i;
            }
        }
        return -1;
    }
    
    /**
     * 打印数据列表（带序号）
     */
    private static void printWithIndex(List<ElectricityDetail> dataList) {
        for (int i = 0; i < dataList.size(); i++) {
            System.out.printf("  %2d. %s%n", i + 1, dataList.get(i));
        }
    }
}

@echo off
chcp 65001 >nul
echo ========================================
echo 电耗明细排序修改 - 部署检查
echo ========================================
echo.

cd /d "%~dp0"

echo 【1. 检查关键文件修改】
echo.

echo 检查 Service 层修改...
findstr /C:"baseTime + i" src\main\java\com\ruoyi\cm\electricityDetails\service\impl\ICmElectricityDetailsServiceImpl.java >nul
if %errorlevel%==0 (
    echo ✅ Service层修改已应用：批量插入时间微调
) else (
    echo ❌ Service层修改未应用
)

echo.
echo 检查 Mapper 层修改...
findstr /C:"create_time desc, t.id asc" src\main\resources\mapper\workflow\cm\CmElectricityDetailsMapper.xml >nul
if %errorlevel%==0 (
    echo ✅ Mapper层修改已应用：数据库排序逻辑
) else (
    echo ❌ Mapper层修改未应用
)

echo.
echo 检查 Controller 层修改...
findstr /C:"getCreateTime().compareTo" src\main\java\com\ruoyi\cm\electricityDetails\controller\CmElectricityDetailsController.java >nul
if %errorlevel%==0 (
    echo ✅ Controller层修改已应用：应用层排序逻辑
) else (
    echo ❌ Controller层修改未应用
)

echo.
echo ========================================
echo 【2. 部署建议】
echo ========================================
echo.

echo 📋 部署步骤：
echo   1. 确认上述所有修改都已应用 ✅
echo   2. 清理并重新编译项目
echo      mvn clean compile
echo   3. 重新打包项目
echo      mvn package -DskipTests
echo   4. 重启应用服务
echo   5. 清理浏览器缓存
echo   6. 使用测试Excel文件验证功能
echo.

echo 🧪 测试验证：
echo   1. 准备包含"三甲矿区"的Excel文件
echo   2. 清空现有的电耗明细数据（可选）
echo   3. 通过接口导入Excel文件
echo   4. 检查表格显示顺序是否与Excel一致
echo   5. 验证"三甲矿区"是否显示在正确位置
echo.

echo 🔍 预期结果：
echo   - Excel第1行的数据应该显示在表格第1行
echo   - Excel第2行的数据应该显示在表格第2行
echo   - 以此类推，完全保持Excel原始顺序
echo   - 最新导入的批次显示在最前面
echo.

echo ⚠️  注意事项：
echo   - 修改会影响所有新导入的数据
echo   - 已存在的数据不会自动修复
echo   - 如需修复历史数据，建议重新导入
echo.

echo ========================================
echo 检查完成
echo ========================================
pause

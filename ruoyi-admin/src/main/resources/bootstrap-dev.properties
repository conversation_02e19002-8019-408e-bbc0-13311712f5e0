server.port=8080
redis.host=**************
redis.port=6379
redis.password=123456
redis.database=6
ds.master.driver-class-name=com.mysql.cj.jdbc.Driver
ds.master.url=****************************************
#ds.master.url=****************************************-xianchang
#ds.master.url=**************************************
ds.master.username=root
ds.master.password=Root@123456
#ds.master.password=jinzhou@123!
rocketmq.url=**************:9876
rocketmq.consumer.topic=${spring.application.name}~*; dingTalk~*
ruoyi.profile=/Volumes/T7/ruoyi/uploads
minio.url=http://**************:8000
minio.access-key=minio
minio.secret-key=minio@123
minio.bucket-name=dfvideo
gc.file.base-path=/Volumes/T7/ruoyi/dashboard
gc.file.url-prefix=http://127.0.0.1:${server.port}/${server.servlet.context-path}/static/
ding-talk.app-key=ding3po4qme5tldrkdwi
ding-talk.app-secret=GNNQxmHaExJx9EITna7ZX3WN8zZPHrZMP7mLVKFpDQK5KC4yxdCrmG0UlIBrzYBm
logging.level=debug
getToken.dataMetricsBaseUrl= *************:8082
getToken.dataMetricsTokenPath=/data-service/api/token/generate?
getToken.appKey=a6VxEbcWEskDG1J3
getToken.appSecret=RPOqQTsG9I5SwS0XgF9qUKeRqpZdwyDZ
assessToken.dataMetricsListPath=/data-service/api/metricsFolder
dailyProductionVolumeIndicatorsToken.dataMetricsListPath=/data-service/api/dws_biz_rec_mining
dailyProductionVolumeIndicatorsToken.dataMetricsListMetaPath=/data-service/api/meta_dws_biz_rec_mining
deptMineToken.dataMetricsListPath=/data-service/api/dws_biz_dept_mine
deptMineToken.dataMetricsListMetaPath=/data-service/api/meta_dws_biz_rec_mining

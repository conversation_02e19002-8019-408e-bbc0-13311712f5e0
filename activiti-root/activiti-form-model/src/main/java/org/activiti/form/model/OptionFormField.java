/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.form.model;

import java.util.List;

/**
 * <AUTHOR>
 */
public class OptionFormField extends FormField {

    private static final long serialVersionUID = 1L;

    protected String optionType;
    protected Boolean hasEmptyValue;
    protected List<Option> options;

    public String getOptionType() {
        return optionType;
    }

    public void setOptionType(String optionType) {
        this.optionType = optionType;
    }

    public Boolean getHasEmptyValue() {
        return hasEmptyValue;
    }

    public void setHasEmptyValue(Boolean hasEmptyValue) {
        this.hasEmptyValue = hasEmptyValue;
    }

    public List<Option> getOptions() {
        return options;
    }

    public void setOptions(List<Option> options) {
        this.options = options;
    }
}

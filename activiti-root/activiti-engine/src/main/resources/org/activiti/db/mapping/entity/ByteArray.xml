<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">

    <!-- BYTE ARRAY INSERT -->

    <insert id="insertByteArray" parameterType="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">
        insert into ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_)
        values (#{id, jdbcType=VARCHAR},
                1,
                #{name, jdbcType=VARCHAR},
                #{bytes, jdbcType = ${blobType}},
                #{deploymentId, jdbcType=VARCHAR})
    </insert>

    <insert id="bulkInsertByteArray" parameterType="java.util.List">
        insert into ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_)
        values
        <foreach collection="list" item="byteArray" index="index" separator=",">
            (#{byteArray.id, jdbcType=VARCHAR},
            1,
            #{byteArray.name, jdbcType=VARCHAR},
            #{byteArray.bytes, jdbcType=${blobType}},
            #{byteArray.deploymentId, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <insert id="bulkInsertByteArray" databaseId="oracle" parameterType="java.util.List">
        INSERT ALL
        <foreach collection="list" item="byteArray" index="index">
            into ${prefix}ACT_GE_BYTEARRAY(ID_, REV_, NAME_, BYTES_, DEPLOYMENT_ID_) VALUES
            (#{byteArray.id, jdbcType=VARCHAR},
            1,
            #{byteArray.name, jdbcType=VARCHAR},
            #{byteArray.bytes, jdbcType=${blobType}},
            #{byteArray.deploymentId, jdbcType=VARCHAR})
        </foreach>
        SELECT * FROM dual
    </insert>

    <!-- BYTE ARRAY UPDATE -->

    <update id="updateByteArray" parameterType="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">
        update ${prefix}ACT_GE_BYTEARRAY
        set REV_   = #{revisionNext, jdbcType=INTEGER},
            BYTES_ = #{bytes, jdbcType=${blobType}}
        where ID_ = #{id}
          and REV_ = #{revision, jdbcType=INTEGER}
    </update>

    <!-- BYTE ARRAY DELETE -->

    <select id="selectBytesOfByteArray" parameterType="string" resultType="hashmap">
        select BYTES_
        from ${prefix}ACT_GE_BYTEARRAY
        where ID_ = #{id}
          and REV_ = #{revision}
    </select>

    <delete id="deleteByteArraysForDeployment"
            parameterType="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">
        delete
        from ${prefix}ACT_GE_BYTEARRAY
        where DEPLOYMENT_ID_ = #{id}
          and REV_ = #{revision}
    </delete>

    <delete id="deleteByteArray" parameterType="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">
        delete
        from ${prefix}ACT_GE_BYTEARRAY
        where ID_ = #{id}
          and REV_ = #{revision}
    </delete>

    <delete id="deleteByteArrayNoRevisionCheck" parameterType="string">
        delete
        from ${prefix}ACT_GE_BYTEARRAY
        where ID_ = #{id}
    </delete>

    <!-- BYTE ARRAY RESULTMAP -->

    <resultMap id="byteArrayResultMap" type="org.activiti.engine.impl.persistence.entity.ByteArrayEntityImpl">
        <id property="id" column="ID_" jdbcType="VARCHAR"/>
        <result property="revision" column="REV_" jdbcType="INTEGER"/>
        <result property="name" column="NAME_" jdbcType="VARCHAR"/>
        <result property="bytes" column="BYTES_" jdbcType="${blobType}"/>
        <result property="deploymentId" column="DEPLOYMENT_ID_" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- BYTE ARRAY SELECT -->

    <select id="selectByteArrays" resultMap="byteArrayResultMap">
        select *
        from ${prefix}ACT_GE_BYTEARRAY
    </select>

    <select id="selectByteArray" parameterType="string" resultMap="byteArrayResultMap">
        select *
        from ${prefix}ACT_GE_BYTEARRAY
        where ID_ = #{id, jdbcType=VARCHAR}
    </select>


</mapper>

message.unknown=Unknown message
error.identifier.property.notfound=Cannot resolve identifier ''{0}''
error.identifier.method.notfound=Cannot find method expression for identifier ''{0}'' (null)
error.identifier.method.notamethod=Cannot find method expression for identifier ''{0}'' (found {1} instead)
error.identifier.method.access=Cannot access method ''{0}''
error.identifier.method.invocation=Error invoking method ''{0}'': {1}
error.property.base.null=Target unreachable, base expression ''{0}'' resolved to null
error.property.property.notfound=Cannot resolve property ''{0}'' in ''{1}''
error.property.method.notfound=Cannot find method ''{0}'' in ''{1}''
error.property.method.resolve=Cannot resolve method ''{0}'' in ''{1}''
error.property.method.access=Cannot access method ''{0}'' in ''{1}''
error.property.method.invocation=Error invoking method ''{0}'' in ''{1}''
error.function.invocation=Error invoking function ''{0}''
error.function.access=Cannot access function ''{0}''
error.function.nomapper=Expression uses functions, but no function mapper was provided
error.function.notfound=Could not resolve function ''{0}''
error.function.params=Parameters for function ''{0}'' do not match
error.method.literal.void=Expected type ''void'' is not allowed for literal method expression ''{0}'' 
error.method.invalid=Expression ''{0}'' is not a valid method expression
error.method.notypes=Parameter types must not be null
error.value.set.rvalue=Cannot set value of a non-lvalue expression ''{0}''
error.value.notype=Expected type must not be null
error.compare.types=Cannot compare ''{0}'' and ''{1}''
error.coerce.type=Cannot coerce from {0} to {1}
error.coerce.value=Cannot coerce ''{0}'' to {1}
error.negate=Cannot negate ''{0}''
error.null=Expression cannot be null
error.scan=lexical error at position {0}, encountered {1}, expected {2}
error.parse=syntax error at position {0}, encountered {1}, expected {2}
error.build=Error parsing ''{0}'': {1}
error.config.builder=Error creating builder: {1}

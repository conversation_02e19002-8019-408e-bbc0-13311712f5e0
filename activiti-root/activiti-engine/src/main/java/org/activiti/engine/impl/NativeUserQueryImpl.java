package org.activiti.engine.impl;

import org.activiti.engine.identity.NativeUserQuery;
import org.activiti.engine.identity.User;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.interceptor.CommandExecutor;

import java.util.List;
import java.util.Map;

public class NativeUserQueryImpl extends AbstractNativeQuery<NativeUserQuery, User>
        implements NativeUserQuery {

    private static final long serialVersionUID = 1L;

    public NativeUserQueryImpl(CommandContext commandContext) {
        super(commandContext);
    }

    public NativeUserQueryImpl(CommandExecutor commandExecutor) {
        super(commandExecutor);
    }

    // results ////////////////////////////////////////////////////////////////

    public List<User> executeList(
            CommandContext commandContext,
            Map<String, Object> parameterMap,
            int firstResult,
            int maxResults) {
        return commandContext
                .getUserEntityManager()
                .findUsersByNativeQuery(parameterMap, firstResult, maxResults);
    }

    public long executeCount(CommandContext commandContext, Map<String, Object> parameterMap) {
        return commandContext.getUserEntityManager().findUserCountByNativeQuery(parameterMap);
    }
}

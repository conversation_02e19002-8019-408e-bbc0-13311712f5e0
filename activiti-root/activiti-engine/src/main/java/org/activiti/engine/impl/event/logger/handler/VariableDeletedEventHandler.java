package org.activiti.engine.impl.event.logger.handler;

import org.activiti.engine.delegate.event.ActivitiVariableEvent;
import org.activiti.engine.impl.interceptor.CommandContext;
import org.activiti.engine.impl.persistence.entity.EventLogEntryEntity;

import java.util.Map;

/**
 * <AUTHOR>
 */
public class VariableDeletedEventHandler extends VariableEventHandler {

    @Override
    public EventLogEntryEntity generateEventLogEntry(CommandContext commandContext) {
        ActivitiVariableEvent variableEvent = (ActivitiVariableEvent) event;
        Map<String, Object> data = createData(variableEvent);

        data.put(Fields.END_TIME, timeStamp);

        return createEventLogEntry(
                variableEvent.getProcessDefinitionId(),
                variableEvent.getProcessInstanceId(),
                variableEvent.getExecutionId(),
                variableEvent.getTaskId(),
                data);
    }
}

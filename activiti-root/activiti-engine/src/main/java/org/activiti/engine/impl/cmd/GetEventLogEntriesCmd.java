package org.activiti.engine.impl.cmd;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.activiti.engine.event.EventLogEntry;
import org.activiti.engine.impl.interceptor.Command;
import org.activiti.engine.impl.interceptor.CommandContext;

import java.util.List;

/**
 * <AUTHOR>
 */
public class GetEventLogEntriesCmd implements Command<List<EventLogEntry>> {

    protected String processInstanceId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    protected Long startLogNr;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    protected Long pageSize;

    public GetEventLogEntriesCmd() {
    }

    public GetEventLogEntriesCmd(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public GetEventLogEntriesCmd(Long startLogNr, Long pageSize) {
        this.startLogNr = startLogNr;
        this.pageSize = pageSize;
    }

    @Override
    public List<EventLogEntry> execute(CommandContext commandContext) {
        if (processInstanceId != null) {
            return commandContext
                    .getEventLogEntryEntityManager()
                    .findEventLogEntriesByProcessInstanceId(processInstanceId);

        } else if (startLogNr != null) {
            return commandContext
                    .getEventLogEntryEntityManager()
                    .findEventLogEntries(startLogNr, pageSize != null ? pageSize : -1);

        } else {
            return commandContext.getEventLogEntryEntityManager().findAllEventLogEntries();
        }
    }
}

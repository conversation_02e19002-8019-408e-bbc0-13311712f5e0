/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.activiti.engine.impl.persistence.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.activiti.engine.impl.context.Context;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public abstract class HistoricScopeInstanceEntityImpl extends AbstractEntityNoRevision
        implements HistoricScopeInstanceEntity, Serializable {

    private static final long serialVersionUID = 1L;

    protected String processInstanceId;
    protected String processDefinitionId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    protected Date endTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    protected Long durationInMillis;

    protected String deleteReason;

    public void markEnded(String deleteReason) {
        if (this.endTime == null) {
            this.deleteReason = deleteReason;
            this.endTime = Context.getProcessEngineConfiguration().getClock().getCurrentTime();
            this.durationInMillis = endTime.getTime() - startTime.getTime();
        }
    }

    // getters and setters ////////////////////////////////////////////////////////

    public String getProcessInstanceId() {
        return processInstanceId;
    }

    public void setProcessInstanceId(String processInstanceId) {
        this.processInstanceId = processInstanceId;
    }

    public String getProcessDefinitionId() {
        return processDefinitionId;
    }

    public void setProcessDefinitionId(String processDefinitionId) {
        this.processDefinitionId = processDefinitionId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Long getDurationInMillis() {
        return durationInMillis;
    }

    public void setDurationInMillis(Long durationInMillis) {
        this.durationInMillis = durationInMillis;
    }

    public String getDeleteReason() {
        return deleteReason;
    }

    public void setDeleteReason(String deleteReason) {
        this.deleteReason = deleteReason;
    }
}

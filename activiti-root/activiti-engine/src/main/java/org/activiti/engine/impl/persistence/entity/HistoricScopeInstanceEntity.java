/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.activiti.engine.impl.persistence.entity;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface HistoricScopeInstanceEntity extends Entity {

    void markEnded(String deleteReason);

    String getProcessInstanceId();

    void setProcessInstanceId(String processInstanceId);

    String getProcessDefinitionId();

    void setProcessDefinitionId(String processDefinitionId);

    Date getStartTime();

    void setStartTime(Date startTime);

    Date getEndTime();

    void setEndTime(Date endTime);

    Long getDurationInMillis();

    void setDurationInMillis(Long durationInMillis);

    String getDeleteReason();

    void setDeleteReason(String deleteReason);
}

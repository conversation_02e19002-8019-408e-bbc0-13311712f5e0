/* Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.activiti.engine.impl.persistence;

/**
 * <AUTHOR>
 */
public interface CountingExecutionEntity {

    boolean isCountEnabled();

    void setCountEnabled(boolean isCountEnabled);

    int getEventSubscriptionCount();

    void setEventSubscriptionCount(int eventSubscriptionCount);

    int getTaskCount();

    void setTaskCount(int taskcount);

    int getJobCount();

    void setJobCount(int jobCount);

    int getTimerJobCount();

    void setTimerJobCount(int timerJobCount);

    int getSuspendedJobCount();

    void setSuspendedJobCount(int suspendedJobCount);

    int getDeadLetterJobCount();

    void setDeadLetterJobCount(int deadLetterJobCount);

    int getVariableCount();

    void setVariableCount(int variableCount);

    int getIdentityLinkCount();

    void setIdentityLinkCount(int identityLinkCount);
}

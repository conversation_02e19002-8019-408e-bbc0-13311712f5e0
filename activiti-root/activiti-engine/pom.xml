<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <name>Activiti - Engine</name>
    <artifactId>activiti-engine</artifactId>

    <parent>
        <groupId>org.activiti</groupId>
        <artifactId>activiti-root</artifactId>
        <version>dfe-zh-ruoyi-lowcode-boot2-1.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <activiti.artifact>
            org.activiti.engine
        </activiti.artifact>
        <activiti.osgi.export.additional>
            org.activiti.db.mapping.entity
        </activiti.osgi.export.additional>
        <activiti.osgi.import.additional>
            junit*;resolution:=optional,
            org.junit*;resolution:=optional,
            com.sun*;resolution:=optional,
            javax.activation*;resolution:=optional,
            javax.persistence*;resolution:=optional,
            org.apache.commons.mail*;resolution:=optional,
            org.activiti.camel;resolution:=optional,
            org.activiti.camel.impl;resolution:=optional,
            org.springframework*;resolution:=optional,
            org.drools*;resolution:=optional,
            com.fasterxml*;resolution:=optional,
            javax.transaction;resolution:=optional,
            javax.enterprise.concurrent;resolution:=optional,
        </activiti.osgi.import.additional>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-bpmn-converter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-process-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-image-generator</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-dmn-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-form-model</artifactId>
        </dependency>
        <dependency>
            <groupId>org.activiti</groupId>
            <artifactId>activiti-form-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-email</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-beans</artifactId>
        </dependency>
        <dependency>
            <groupId>de.odysseus.juel</groupId>
            <artifactId>juel-api</artifactId>
        </dependency>
        <dependency>
            <groupId>de.odysseus.juel</groupId>
            <artifactId>juel-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>de.odysseus.juel</groupId>
            <artifactId>juel-spi</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.codehaus.groovy</groupId>
            <artifactId>groovy-all</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-core</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>drools-compiler</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.drools</groupId>
            <artifactId>knowledge-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.geronimo.specs</groupId>
            <artifactId>geronimo-jta_1.1_spec</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>javax.enterprise.concurrent</groupId>
            <artifactId>javax.enterprise.concurrent-api</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 达梦数据库驱动 -->
        <!-- https://mvnrepository.com/artifact/com.dameng/DmJdbcDriver18 -->
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmJdbcDriver18</artifactId>
            <version>8.1.3.140</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.apache.openjpa</groupId>
            <artifactId>openjpa</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.subethamail</groupId>
            <artifactId>subethasmtp-wiser</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.sourceforge.jtds</groupId>
            <artifactId>jtds</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.uuid</groupId>
            <artifactId>java-uuid-generator</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Required for testing JTA -->
        <dependency>
            <groupId>org.codehaus.btm</groupId>
            <artifactId>btm</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>**/*TestCase.java</exclude>
                        <exclude>**/RepeatingServiceTaskTest.java</exclude>
                    </excludes>
                    <runOrder>alphabetical</runOrder>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestFile>${project.build.outputDirectory}/META-INF/MANIFEST.MF</manifestFile>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.felix</groupId>
                <artifactId>maven-bundle-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>cleanVersions</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>bundle-manifest</id>
                        <phase>process-classes</phase>
                        <goals>
                            <goal>manifest</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!--This plugin's configuration is used to store Eclipse m2e settings
                  only. It has no influence on the Maven build itself. -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>org.apache.felix</groupId>
                                        <artifactId>
                                            maven-bundle-plugin
                                        </artifactId>
                                        <versionRange>
                                            [2.1.0,)
                                        </versionRange>
                                        <goals>
                                            <goal>cleanVersions</goal>
                                            <goal>manifest</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore></ignore>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <!--  <profiles>-->

    <!--    <profile>-->
    <!--      <id>checkspring</id>-->
    <!--      <properties>-->
    <!--        <skipTests>true</skipTests>-->
    <!--      </properties>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>create-test-jar</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <groupId>org.apache.maven.plugins</groupId>-->
    <!--            <artifactId>maven-jar-plugin</artifactId>-->
    <!--            <version>2.2</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <goals>-->
    <!--                  <goal>test-jar</goal>-->
    <!--                </goals>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->
    <!--    <profile>-->
    <!--      <id>upgradeDatabase</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>upgradeDatabase</name>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <properties>-->
    <!--        <skipTests>true</skipTests>-->
    <!--      </properties>-->
    <!--    </profile>-->
    <!--    <profile>-->
    <!--      <id>distro</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <groupId>org.apache.maven.plugins</groupId>-->
    <!--            <artifactId>maven-source-plugin</artifactId>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>attach-sources</id>-->
    <!--                <phase>package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>jar-no-fork</goal>-->
    <!--                </goals>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; database qa profile &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>database</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>database</name>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="updating activiti configuration to db ${database}" />-->
    <!--                    <copy file="${basedir}/../../qa/db/activiti.cfg.xml"-->
    <!--                          todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.activiti6.${database}.properties" />-->
    <!--                    </copy>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${database}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>databasewithschema</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>databasewithschema</name>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to db ${databasewithschema}" />-->
    <!--                    <copy file="${basedir}/../../qa/db/schema/activiti.cfg.xml"-->
    <!--                          todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.activiti6.${databasewithschema}.properties" />-->
    <!--                    </copy>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${databasewithschema}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>databasemssql</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>databasemssql</name>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="updating activiti configuration to db ${databasemssql}" />-->
    <!--                    <copy file="${basedir}/../../qa/db/mssql/activiti.cfg.xml"-->
    <!--                          todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.activiti6.${databasemssql}.properties" />-->
    <!--                    </copy>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; Config qa profile &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>configSpring</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>configExecution</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration config to db ${database}" />-->
    <!--                    <copy-->
    <!--                        file="${basedir}/../../qa/spring/${database}/activiti.cfg.xml"-->
    <!--                        todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.${database}.properties" />-->
    <!--                    </copy>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${database}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <excludes>-->
    <!--                <exclude>**/*TestCase.java</exclude>-->

    <!--                &lt;!&ndash; Disabling tests that run exclusively using standalone config &ndash;&gt;-->
    <!--                <exclude>**/ProcessDiagramRetrievalTest.java</exclude>-->
    <!--                <exclude>org/activiti/standalone/**</exclude>-->
    <!--              </excludes>-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>org.activiti</groupId>-->
    <!--          <artifactId>activiti-spring</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>configJta</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>configExecution</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="Copying jta enabled Activiti configuration file to target/test-classes" />-->
    <!--                    <echo-->
    <!--                        message="Updating activiti configuration config to db ${database}" />-->
    <!--                    <copy file="${basedir}/../../qa/jta/${database}/activiti.cfg.xml"-->
    <!--                          todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.${database}.properties" />-->
    <!--                    </copy>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${database}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <excludes>-->
    <!--                <exclude>**/*TestCase.java</exclude>-->

    <!--                &lt;!&ndash; Disabling tests that run exclusively using standalone config &ndash;&gt;-->
    <!--                <exclude>**/ProcessDiagramRetrievalTest.java</exclude>-->
    <!--                <exclude>org/activiti/standalone/**</exclude>-->
    <!--              </excludes>-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>org.slf4j</groupId>-->
    <!--          <artifactId>slf4j-jdk14</artifactId>-->
    <!--          <version>1.7.6</version>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; check history full &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>cfghistoryfull</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to qa/cfg/historyfull.activiti.cfg.xml" />-->
    <!--                    <copy file="${basedir}/../../qa/cfg/historyfull.activiti.cfg.xml"-->
    <!--                          tofile="target/test-classes/activiti.cfg.xml" overwrite="true" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <excludes>-->
    <!--                <exclude>**/*TestCase.java</exclude>-->
    <!--                <exclude>**/CompetingJobAcquisitionTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-234 &ndash;&gt;-->
    <!--                <exclude>**/WSDLImporterTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-315 &ndash;&gt;-->
    <!--                <exclude>**/JobExecutorTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-427 &ndash;&gt;-->
    <!--                <exclude>**/HistoricTaskInstanceUpdateTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-485 &ndash;&gt;-->
    <!--                <exclude>**/RepeatingServiceTaskTest.java</exclude>-->
    <!--                <exclude>org/activiti/standalone/**</exclude>-->
    <!--              </excludes>-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>databasehistoryfull</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to db ${database}, using FULL history" />-->
    <!--                    <copy file="${basedir}/../../qa/db/historyfull.activiti.cfg.xml"-->
    <!--                          todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.${database}.properties" />-->
    <!--                    </copy>-->
    <!--                    <rename src="target/test-classes/historyfull.activiti.cfg.xml"-->
    <!--                            dest="target/test-classes/activiti.cfg.xml" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema-before</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${database}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema-after</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${database}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; Same as databasehistoryfull, but for databases that need a schema-->
    <!--      in the config &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>databasehistoryfullwithschema</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            &lt;!&ndash; only worked with this version, there might be a bug with antrun-->
    <!--              see http://jira.codehaus.org/browse/MANTRUN-109 &ndash;&gt;-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to db ${database}, using FULL history" />-->
    <!--                    <copy-->
    <!--                        file="${basedir}/../../qa/db/historyfull-with-schema.activiti.cfg.xml"-->
    <!--                        todir="target/test-classes" overwrite="true">-->
    <!--                      <filterset-->
    <!--                          filtersfile="${user.home}/.activiti/jdbc/build.${databasewithschema}.properties" />-->
    <!--                    </copy>-->
    <!--                    <rename-->
    <!--                        src="target/test-classes/historyfull-with-schema.activiti.cfg.xml"-->
    <!--                        dest="target/test-classes/activiti.cfg.xml" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema-before</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${databasewithschema}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema-after</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${databasewithschema}" />-->
    <!--                    <java classname="org.activiti.engine.impl.db.DbSchemaDrop"-->
    <!--                          classpathref="maven.test.classpath" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; check history full &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>cfghistoryaudit</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to qa/cfg/historyaudit.activiti.cfg.xml" />-->
    <!--                    <copy file="${basedir}/../../qa/cfg/historyaudit.activiti.cfg.xml"-->
    <!--                          tofile="target/test-classes/activiti.cfg.xml" overwrite="true" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <excludes>-->
    <!--                <exclude>**/*TestCase.java</exclude>-->
    <!--                <exclude>**/CompetingJobAcquisitionTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-234 &ndash;&gt;-->
    <!--                <exclude>**/WSDLImporterTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-315 &ndash;&gt;-->
    <!--                <exclude>**/JobExecutorTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-427 &ndash;&gt;-->
    <!--                <exclude>**/HistoricTaskInstanceUpdateTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-485 &ndash;&gt;-->
    <!--                <exclude>**/RepeatingServiceTaskTest.java</exclude>-->
    <!--                <exclude>org/activiti/standalone/**</exclude>-->
    <!--              </excludes>-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash; check history none &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>cfghistorynone</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo-->
    <!--                        message="updating activiti configuration to qa/cfg/historynone.activiti.cfg.xml" />-->
    <!--                    <copy file="${basedir}/../../qa/cfg/historynone.activiti.cfg.xml"-->
    <!--                          tofile="target/test-classes/activiti.cfg.xml" overwrite="true" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <excludes>-->
    <!--                <exclude>**/*TestCase.java</exclude>-->
    <!--                <exclude>**/CompetingJobAcquisitionTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-234 &ndash;&gt;-->
    <!--                <exclude>**/WSDLImporterTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-315 &ndash;&gt;-->
    <!--                <exclude>**/JobExecutorTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-427 &ndash;&gt;-->
    <!--                <exclude>**/HistoricTaskInstanceUpdateTest.java</exclude> &lt;!&ndash; http://jira.codehaus.org/browse/ACT-485 &ndash;&gt;-->
    <!--                <exclude>**/Historic*InstanceTest.java</exclude>-->
    <!--                <exclude>**/HistoryServiceTest.java</exclude>-->
    <!--                <exclude>**/RepeatingServiceTaskTest.java</exclude>-->
    <!--                <exclude>**/ProcessInstanceLogQueryTest.java</exclude>-->
    <!--                <exclude>org/activiti/standalone/**</exclude>-->
    <!--                <exclude>**/HistoricProcessInstanceQueryVersionTest.java</exclude>-->
    <!--                <exclude>**/NonCascadeDeleteTest.java</exclude>-->
    <!--                <exclude>**/HistoricProcessInstanceQueryAndWithExceptionTest.java</exclude>-->
    <!--                <exclude>**/HistoricJPAVariableTest.java</exclude>-->
    <!--                <exclude>**/VerifyDatabaseOperationsTest.java</exclude>-->
    <!--              </excludes>-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>testSchemaMetaData</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>metadatabase</name>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-surefire-plugin</artifactId>-->
    <!--            <configuration>-->
    <!--              <includes>-->
    <!--                <include>**/JdbcMetaDataTest.java</include>-->
    <!--              </includes>-->
    <!--              <excludes />-->
    <!--              <runOrder>alphabetical</runOrder>-->
    <!--            </configuration>-->
    <!--          </plugin>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="creating db schema in ${metadatabase}" />-->
    <!--                    <ant antfile="${basedir}/../../qa/db/build.xml" target="create.db.schema">-->
    <!--                      <property name="test_classpath" refid="maven.test.classpath" />-->
    <!--                      <property name="database" value="${metadatabase}" />-->
    <!--                    </ant>-->
    <!--                    <echo message="updating activiti cfg to ${metadatabase}" />-->
    <!--                    <ant antfile="${basedir}/../../qa/db/build.xml" target="create.activiti.cfg">-->
    <!--                      <property name="database" value="${metadatabase}" />-->
    <!--                    </ant>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--              <execution>-->
    <!--                <id>database-test-drop-schema</id>-->
    <!--                <phase>prepare-package</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="dropping schema in ${metadatabase}" />-->
    <!--                    <ant antfile="${basedir}/../../qa/db/build.xml" target="drop.db.schema"-->
    <!--                         inheritAll="false">-->
    <!--                      <property name="test_classpath" refid="maven.test.classpath" />-->
    <!--                      <property name="database" value="${metadatabase}" />-->
    <!--                    </ant>-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    &lt;!&ndash;  check with optimization flags set &ndash;&gt;-->
    <!--    <profile>-->
    <!--      <id>cfgOptimizations</id>-->
    <!--      <build>-->
    <!--        <plugins>-->
    <!--          <plugin>-->
    <!--            <artifactId>maven-antrun-plugin</artifactId>-->
    <!--            <version>1.4</version>-->
    <!--            <executions>-->
    <!--              <execution>-->
    <!--                <id>database-test-create-schema</id>-->
    <!--                <phase>process-test-classes</phase>-->
    <!--                <goals>-->
    <!--                  <goal>run</goal>-->
    <!--                </goals>-->
    <!--                <configuration>-->
    <!--                  <tasks>-->
    <!--                    <echo message="updating activiti configuration to qa/cfg/optimizations.activiti.cfg.xml" />-->
    <!--                    <copy file="${basedir}/../../qa/cfg/optimizations.activiti.cfg.xml" tofile="target/test-classes/activiti.cfg.xml" overwrite="true" />-->
    <!--                  </tasks>-->
    <!--                </configuration>-->
    <!--              </execution>-->
    <!--            </executions>-->
    <!--          </plugin>-->
    <!--        </plugins>-->
    <!--      </build>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>oracle</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>database</name>-->
    <!--          <value>oracle</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>com.oracle.jdbc</groupId>-->
    <!--          <artifactId>ojdbc7</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>oraclewithschema</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>databasewithschema</name>-->
    <!--          <value>oracle</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>com.oracle.jdbc</groupId>-->
    <!--          <artifactId>ojdbc7</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>db2</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>database</name>-->
    <!--          <value>db2</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>com.ibm.db2.jcc</groupId>-->
    <!--          <artifactId>db2jcc4</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>mssql</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>databasemssql</name>-->
    <!--          <value>mssql</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>net.sourceforge.jtds</groupId>-->
    <!--          <artifactId>jtds</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>mysql</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>database</name>-->
    <!--          <value>mysql</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>mysql</groupId>-->
    <!--          <artifactId>mysql-connector-java</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->

    <!--    <profile>-->
    <!--      <id>hsql</id>-->
    <!--      <activation>-->
    <!--        <property>-->
    <!--          <name>database</name>-->
    <!--          <value>hsql</value>-->
    <!--        </property>-->
    <!--      </activation>-->
    <!--      <dependencies>-->
    <!--        <dependency>-->
    <!--          <groupId>org.hsqldb</groupId>-->
    <!--          <artifactId>hsqldb</artifactId>-->
    <!--          <scope>test</scope>-->
    <!--        </dependency>-->
    <!--      </dependencies>-->
    <!--    </profile>-->
    <!--  </profiles>-->

</project>

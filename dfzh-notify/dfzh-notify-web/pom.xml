<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
        <artifactId>dfzh-notify</artifactId>
        <version>dfe-zh-ruoyi-lowcode-boot2-1.0.0-SNAPSHOT</version>
    </parent>

    <description>通知发送配置维护模块</description>
    <artifactId>dfzh-notify-web</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
            <artifactId>ruoyi-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dfe.dfzh.ruoyi.lowcode</groupId>
            <artifactId>dingTalk-spring-boot2-ruoyi-starter</artifactId>
        </dependency>
    </dependencies>

</project>

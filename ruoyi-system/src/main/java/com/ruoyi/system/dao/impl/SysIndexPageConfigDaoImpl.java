package com.ruoyi.system.dao.impl;

import cn.hutool.json.JSONObject;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.system.dao.ISysIndexPageConfigDao;
import com.ruoyi.system.domain.SysIndexPageConfig;
import com.ruoyi.system.mapper.SysIndexPageConfigMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysIndexPageConfigDaoImpl extends MPJBaseServiceImpl<SysIndexPageConfigMapper, SysIndexPageConfig> implements ISysIndexPageConfigDao {

    private final SysIndexPageConfigMapper sysIndexPageConfigMapper;

    @Override
    public Boolean saveIndexPageConfigs(JSONObject jsonObject) {
        // 数据库删除所有行（后续拓展可判断是否全局生效，如果不是全局生效的，仅覆盖当前登录用户的所有配置）
        sysIndexPageConfigMapper.delete(null);

        // 循环json键值对，生成实体类集合
        List<SysIndexPageConfig> sysIndexPageConfigs =
                jsonObject.keySet()
                        .stream()
                        .map(key -> SysIndexPageConfig.builder()
                                .configKey(key)
                                .configValue(jsonObject.getStr(key))
                                .build())
                        .collect(Collectors.toList());

        // 重新插入所有行
        return super.saveBatch(sysIndexPageConfigs);
    }

    @Override
    public JSONObject getIndexPageConfigs() {
        JSONObject jsonObject = new JSONObject();
        List<SysIndexPageConfig> sysIndexPageConfigs = sysIndexPageConfigMapper.selectList(null);
        sysIndexPageConfigs.forEach(sysIndexPageConfig -> {
            jsonObject.set(sysIndexPageConfig.getConfigKey(), Boolean.parseBoolean(sysIndexPageConfig.getConfigValue()));
        });
        return jsonObject;
    }

}

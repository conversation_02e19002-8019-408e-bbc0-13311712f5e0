package com.ruoyi.system.dao.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.dao.impl.BaseDaoImpl;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.Threads;
import com.ruoyi.system.dao.ISysMassageDao;
import com.ruoyi.system.domain.SysMassage;
import com.ruoyi.system.mapper.SysMassageMapper;
import com.ruoyi.websocket.WebSocketUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.concurrent.CompletableFuture;

/**
 * 消息推送Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-02-22
 */
@Repository
@Slf4j
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysMassageDaoImpl extends BaseDaoImpl<SysMassageMapper, SysMassage> implements ISysMassageDao {
    private final DefaultMQProducer transactionMQProducer;

    /**
     * @param sysMassage
     */
    @Override
    public void sendMsg(SysMassage sysMassage) {
        boolean rows = save(sysMassage);
        if (rows) {
            CompletableFuture.runAsync(() -> {
                //发送pc消息
                boolean b;
                int count = 0;
                do {
                    b = WebSocketUtil.sendByKey(sysMassage, sysMassage.getRecipientUserName());
                    count++;
                    Threads.sleep(1000);
                } while (!b && count < 2);
                if (!b) {
                    //发送失败，则先发一条half消息给mq
                    String topics = environment().getProperty("rocketmq.consumer.topic");
                    if (StringUtil.isNotBlank(topics)) {
                        log.error("发送WebSocket消息失败!");
                        //topic~tag;topic~tag||tag;topic~* 可以被;隔开
                        String[] topic = topics.split(";");
                        try {
                            for (String s : topic) {
                                String[] split = s.split("~");
                                /* 作为生产者发送一条事务消息给MQ 相当于创建了一个锁 */
                                transactionMQProducer.sendMessageInTransaction(new Message(split[0], "SysMassage", JSON.toJSONBytes(sysMassage)), null);
                                log.debug("已经改用RocketMQ，等待WebSocket消息发送!");
                                log.debug("等待生产者确认half投递！");
                            }
                        } catch (MQClientException e) {
                            throw new RuntimeException(e);
                        }

                    }
                }
            }, threadPoolTaskExecutor());
        }
    }


}

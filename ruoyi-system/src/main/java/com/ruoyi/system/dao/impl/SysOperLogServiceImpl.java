package com.ruoyi.system.dao.impl;

import com.github.yulichang.base.MPJBaseServiceImpl;
import com.ruoyi.system.dao.ISysOperLogService;
import com.ruoyi.system.domain.SysOperLog;
import com.ruoyi.system.mapper.SysOperLogMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 操作日志 服务层处理
 *
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysOperLogServiceImpl extends MPJBaseServiceImpl<SysOperLogMapper, SysOperLog> implements ISysOperLogService {

    /**
     * 查询系统操作日志集合
     *
     * @param operLog 操作日志对象
     * @return 操作日志集合
     */
    @Override
    public List<SysOperLog> list(SysOperLog operLog) {
        return baseMapper.list(operLog);
    }

    /**
     * 清空操作日志
     */
    @Override
    public boolean cleanOperLog() {
        baseMapper.cleanOperLog();
        return true;
    }
}

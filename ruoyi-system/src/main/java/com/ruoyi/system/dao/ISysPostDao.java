package com.ruoyi.system.dao;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.dao.IBaseDao;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.system.domain.SysPost;

import java.util.List;

/**
 * 岗位信息 服务层
 *
 * <AUTHOR>
 */
public interface ISysPostDao extends IBaseDao<SysPost> {
    int deletePostByUserIdAndPostId(Long userId, Long postId);

    int deletePostByUserIdsAndPostId(Long[] userIds, Long postId);

    /**
     * 根据用户ID获取岗位选择框列表
     *
     * @param userId 用户ID
     * @return 选中岗位ID列表
     */
    List<Long> postIdListByUserId(Long userId);

    /**
     * 校验岗位名称
     *
     * @param post 岗位信息
     * @return 结果
     */
    default String checkPostNameUnique(SysPost post) {
        long postId = StringUtil.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = lambdaQuery().eq(SysPost::getPostName, post.getPostName()).one();
        if (StringUtil.isNotNull(info) && info.getPostId() != postId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验岗位编码
     *
     * @param post 岗位信息
     * @return 结果
     */
    default String checkPostCodeUnique(SysPost post) {
        long postId = StringUtil.isNull(post.getPostId()) ? -1L : post.getPostId();
        SysPost info = lambdaQuery().eq(SysPost::getPostCode, post.getPostCode()).one();
        if (StringUtil.isNotNull(info) && info.getPostId() != postId) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    int savePostAndUsers(Long[] userIds, Long postId);
}

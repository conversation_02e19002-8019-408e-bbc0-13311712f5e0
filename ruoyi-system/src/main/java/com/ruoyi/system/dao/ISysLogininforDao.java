package com.ruoyi.system.dao;

import com.github.yulichang.base.MPJBaseService;
import com.github.yulichang.extension.mapping.base.MPJDeepService;
import com.github.yulichang.extension.mapping.base.MPJRelationService;
import com.ruoyi.system.domain.SysLogininfor;

import java.util.List;

/**
 * 系统访问日志情况信息 服务层
 *
 * <AUTHOR>
 */
public interface ISysLogininforDao extends MPJBaseService<SysLogininfor>, MPJDeepService<SysLogininfor>, MPJRelationService<SysLogininfor> {
    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    List<SysLogininfor> selectLogininforList(SysLogininfor logininfor);


    /**
     * 清空系统登录日志
     */
    boolean cleanLogininfor();
}

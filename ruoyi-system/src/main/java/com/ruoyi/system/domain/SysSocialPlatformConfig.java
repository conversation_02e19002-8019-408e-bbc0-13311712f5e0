package com.ruoyi.system.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * SysSocialPlatformConfig对象 sys_social_platform_config
 * <p>
 * 目前 表基础字段（公共都有的字段）'id','delFlag','searchValue','createBy','createById','createTime','updateBy','updateById','updateTime','remark','selected','mobileParams','params'
 * 工作流类表单除去上述公共字段 还有的其他公共字段 'status','applyUserId','applyUserName','applyTime','deptId','processKey','taskId','taskName','suspendState','suspendStateName','instanceId','notify'
 * 建表时，请根据情况添加上述字段
 * <p>
 * BaseEntity继承了 MybatisPlus 的 Model<T> 可以使用 ActiveRecord 模式 CRUD (前提是继承了BaseMapper<T>接口)
 * 如使用 MybatisPlus 方法，请把非表字段 加上 @TableField(exist = false)；若存在重写 主键 的情况 请 重写 @TableField(exist = false)private Long id; 并在表id字段上加 @TableId
 * 若父类中存在 非表 字段 还是用 重写后 用 @TableField(exist = false) 标记
 * 本框架 @TableId 默认id策略配置在yml文件中（auto数据库自增，MP默认为 type = IdType.ASSIGN_ID ），若需要雪花算法请重写 @TableId(type = IdType.ASSIGN_ID)private Long id;
 * 推荐使用 长整型 的雪花算法id
 *
 * <AUTHOR>
 * @Builder(toBuilder = true) 目前建造者模式只能 build 本类，无法 build 父类；set方法可以链式拼写；（后续优化）
 * @Accessors(chain = true) 目前 POJO 支持基于 Lombok 的链式写法；
 * 关于 Lombok 公共配置已经抽象到 lombok.confg 文件中，请参见相关文档；
 * @EntityMapping 主子表；
 * @date 2024-06-04
 */

@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SysSocialPlatformConfig extends BaseEntity<SysSocialPlatformConfig> {
    /**
     * 第三方登录平台
     */
    @Excel(name = "第三方登录平台")
    private String platform;

    /**
     * 0:启用 ,1:禁用
     */
    @Excel(name = " 0:启用 ,1:禁用")
    private String status;

    /**
     * 第三方平台申请Id
     */
    @Excel(name = "第三方平台申请Id")
    private String clientId;

    /**
     * 第三方平台密钥
     */
    @Excel(name = "第三方平台密钥")
    private String secretKey;

    /**
     * 用户认证后跳转地址
     */
    @Excel(name = "用户认证后跳转地址")
    private String redirectUri;

    /**
     * 绑定注册登录uri,http://localhost/login?bindId=
     */
    @Excel(name = "绑定注册登录uri,http://localhost/login?bindId=")
    private String bindUri;

    /**
     * 跳转登录uri,http://localhost/login?loginId=
     */
    @Excel(name = "跳转登录uri,http://localhost/login?loginId=")
    private String redirectLoginUri;

    /**
     * 错误提示uri,http://localhost/login?errorId=
     */
    @Excel(name = "错误提示uri,http://localhost/login?errorId=")
    private String errorMsgUri;

    /**
     * $column.columnComment
     */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private Long version;

}

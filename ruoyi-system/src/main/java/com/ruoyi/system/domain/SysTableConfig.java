package com.ruoyi.system.domain;

import cn.hutool.json.JSONArray;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruoyi.common.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName(value = "sys_table_config", autoResultMap = true)
public class SysTableConfig extends BaseEntity<SysTableConfig> {

    // 用户id
    private Long userId;

    // 表名
    private String tableName;

    // 配置内容
    @TableField(typeHandler = JacksonTypeHandler.class)
    private JSONArray tableConfigJson;

}

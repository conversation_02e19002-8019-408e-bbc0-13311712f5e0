package com.ruoyi.system.mapper;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.SysRoleDept;

import java.io.Serializable;
import java.util.Arrays;

/**
 * 角色与部门关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysRoleDeptMapper extends MPJBaseMapper<SysRoleDept> {
    /**
     * 通过角色ID删除角色和部门关联
     *
     * @param roleId 角色ID
     * @return 结果
     */
    default int deleteRoleDeptByRoleId(Serializable... roleId) {
        LambdaUpdateWrapper<SysRoleDept> wrapper = Wrappers.lambdaUpdate(SysRoleDept.class).in(SysRoleDept::getRoleId, Arrays.asList(roleId));
        return delete(wrapper);
    }

    /**
     * 批量新增角色部门信息
     *
     * @param roleDeptList 角色部门列表
     * @return 结果
     */
    default int insertRoleDept(SysRoleDept... roleDeptList) {
        boolean allMatch = Arrays.stream(roleDeptList).allMatch(x -> SqlHelper.retBool(insert(x)));
        if (!allMatch) {
            throw new RuntimeException("新增角色部门失败！");
        }
        return 1;
    }
}

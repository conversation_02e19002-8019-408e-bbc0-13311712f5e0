package com.ruoyi.system.mapper;

import com.ruoyi.common.mapper.IBaseMapper;
import com.ruoyi.system.domain.SysVideo;

/**
 * 视频管理Mapper接口
 * 请尽量在sql.xml中重写覆盖 MybatisPlus BaseMapper<T> 中的原生方法
 * 本 mapper 默认继承了 MybatisPlus 的 BaseMapper<T> 接口，已经具备单表CRUD能力，但保留之前xml.sql；若调用 MybatisPlus 方法报错，请检查对应domainEntity里是否存在非表列字段，或是否严格按照驼峰命名，或是否指定id
 * 已将函数方法抽象，以确保不必多写，只在需要的时候使用自定义函数替代自定义接口。请大家多用 MybatisPlus ，将有限的精力投入到有必要的复杂 SQL 中；
 *
 * <AUTHOR>
 * @date 2023-11-15
 */
public interface SysVideoMapper extends IBaseMapper<SysVideo> {

}

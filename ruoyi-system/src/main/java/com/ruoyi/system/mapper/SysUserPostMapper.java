package com.ruoyi.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.github.yulichang.base.MPJBaseMapper;
import com.github.yulichang.extension.mapping.config.DeepConfig;
import com.github.yulichang.extension.mapping.relation.Relation;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.ruoyi.system.domain.SysUserPost;
import org.apache.ibatis.annotations.Param;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

/**
 * 用户与岗位关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserPostMapper extends MPJBaseMapper<SysUserPost> {

    default List<SysUserPost> selectByUserId(Serializable id) {
        MPJLambdaWrapper<SysUserPost> wrapper = MPJWrappers.lambdaJoin(SysUserPost.class).eq(SysUserPost::getUserId, id);
        return Relation.mpjGetRelation(selectList(wrapper), DeepConfig.defaultConfig());
    }

    /**
     * 通过用户ID删除用户和岗位关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    default int deleteUserPostByUserId(Long userId) {
        LambdaUpdateWrapper<SysUserPost> wrapper = Wrappers.lambdaUpdate(SysUserPost.class).eq(SysUserPost::getUserId, userId);
        return delete(wrapper);
    }

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    default boolean existsUserPostByPostId(Serializable postId) {
        LambdaQueryWrapper<SysUserPost> wrapper = Wrappers.lambdaQuery(SysUserPost.class).eq(SysUserPost::getPostId, postId);
        return exists(wrapper);
    }

    /**
     * 批量删除用户和岗位关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    default int deleteUserPost(Serializable... userId) {
        LambdaUpdateWrapper<SysUserPost> wrapper = Wrappers.lambdaUpdate(SysUserPost.class).in(SysUserPost::getUserId, Arrays.asList(userId));
        return delete(wrapper);
    }

    /**
     * 批量新增用户岗位信息
     *
     * @param sysUserPost 用户角色列表
     * @return 结果
     */
    @DSTransactional(rollbackFor = Exception.class)
    default int insertUserPost(SysUserPost... sysUserPost) {
        boolean allMatch = Arrays.stream(sysUserPost).allMatch(x -> SqlHelper.retBool(insert(x)));
        if (!allMatch) {
            throw new IllegalStateException("新增用户角色关联失败！");
        }
        return 1;
    }

    default int deleteByUserIdAndPostId(@Param("userId") Long userId, @Param("postId") Long postId) {
        LambdaUpdateWrapper<SysUserPost> wrapper = Wrappers.lambdaUpdate(SysUserPost.class).eq(SysUserPost::getUserId, userId).eq(SysUserPost::getPostId, postId);
        return delete(wrapper);
    }

    default int deletePostByUserIdsAndPostId(@Param("userIds") Long[] userIds, @Param("postId") Long postId) {
        LambdaUpdateWrapper<SysUserPost> wrapper = Wrappers.lambdaUpdate(SysUserPost.class).in(SysUserPost::getUserId, Arrays.asList(userIds)).eq(SysUserPost::getPostId, postId);
        return delete(wrapper);
    }
}

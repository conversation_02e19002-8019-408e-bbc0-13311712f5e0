package com.ruoyi.system.mapper;

import com.github.yulichang.base.MPJBaseMapper;
import com.ruoyi.system.domain.SysLogininfor;

import java.util.List;

/**
 * 系统访问日志情况信息 数据层
 *
 * <AUTHOR>
 */
public interface SysLogininforMapper extends MPJBaseMapper<SysLogininfor> {

    /**
     * 查询系统登录日志集合
     *
     * @param logininfor 访问日志对象
     * @return 登录记录集合
     */
    List<SysLogininfor> list(SysLogininfor logininfor);

    /**
     * 清空系统登录日志
     *
     * @return 结果
     */
    int cleanLogininfor();
}

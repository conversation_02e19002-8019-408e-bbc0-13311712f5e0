package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.domain.SysDictData;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.page.TableDataInfo;
import com.ruoyi.common.util.StringUtil;
import com.ruoyi.common.util.poi.ExcelUtil;
import com.ruoyi.system.dao.ISysDictDataDao;
import com.ruoyi.system.dao.ISysDictTypeDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 数据字典信息
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/system/dict/data")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysDictDataController extends BaseController implements IBaseController {
    private final ISysDictDataDao dictDataService;
    private final ISysDictTypeDao dictTypeService;


    @GetMapping("/list")
    public TableDataInfo list(SysDictData dictData) {
        startPage();
        List<SysDictData> list = dictDataService.list(dictData);
        return getDataTable(list);
    }


    @Log(title = "字典数据", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDictData dictData) {
        List<SysDictData> list = dictDataService.list(dictData);
        ExcelUtil<SysDictData> util = new ExcelUtil<>(SysDictData.class);
        util.exportExcel(response, list, "字典数据");
    }

    /**
     * 查询字典数据详细
     */

    @GetMapping(value = "/{dictCode}")
    public AjaxResult getInfo(@PathVariable Long dictCode) {
        return success(dictDataService.getByIdDeep(dictCode));
    }

    /**
     * 根据字典类型查询字典数据信息
     */

    @GetMapping(value = "/type/{dictType}")
    public AjaxResult dictType(@PathVariable String dictType) {
        List<SysDictData> data = dictTypeService.selectDictDataByType(dictType);
        if (StringUtil.isNull(data)) {
            data = new ArrayList<>();
        }
        return success(data);
    }

    /**
     * 新增字典类型
     */

    @Log(title = "字典数据", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysDictData dict) {
        dict.setCreateBy(getUsername());
        return toAjax(dictDataService.save(dict));
    }

    /**
     * 修改保存字典类型
     */

    @Log(title = "字典数据", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysDictData dict) {
        dict.setUpdateBy(getUsername());
        return toAjax(dictDataService.updateById(dict));
    }

    /**
     * 删除字典类型
     */

    @Log(title = "字典类型", businessType = BusinessType.DELETE)
    @DeleteMapping("/{dictCodes}")
    public AjaxResult remove(@PathVariable Long[] dictCodes) {
        return success(dictDataService.removeById(dictCodes));
    }


    @GetMapping("/listDictValue")
    public AjaxResult listDictValue(String dictValue) {
        SysDictData dictData = dictDataService.listDictValue(dictValue);
        if (dictData == null){
            SysDictData dictData1 = new SysDictData();
            return success(dictData1.setDictLabel("采掘全支护合计"));
        }
        return success(dictData);
    }

    @GetMapping("/getDictValue")
    public AjaxResult getDictValue(String dictValue, String dictType) {
        SysDictData dictData = dictDataService.getDictValue(dictValue,dictType);
        return success(dictData);
    }

}

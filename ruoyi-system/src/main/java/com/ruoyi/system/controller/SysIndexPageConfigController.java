package com.ruoyi.system.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.system.dao.ISysIndexPageConfigDao;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
@RequestMapping("/system/indexPageConfig")
public class SysIndexPageConfigController {

    private final ISysIndexPageConfigDao iSysIndexPageConfigDao;

    // 获取配置信息
    @GetMapping("/find")
    public AjaxResult getIndexPageConfigs() {
        JSONObject data = iSysIndexPageConfigDao.getIndexPageConfigs();
        return AjaxResult.success(data);
    }

    // 保存配置信息
    @PostMapping("/save")
    public AjaxResult saveIndexPageConfigs(@RequestBody String configsStr) {
        return AjaxResult.success(iSysIndexPageConfigDao.saveIndexPageConfigs(JSONUtil.parseObj(configsStr)));
    }
}

package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.controller.BaseController;
import com.ruoyi.common.controller.IBaseController;
import com.ruoyi.common.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.page.TableDataInfo;
import com.ruoyi.system.dao.ISysNoticeDao;
import com.ruoyi.system.domain.SysNotice;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */

@RestController
@RequestMapping("/system/notice")
@RequiredArgsConstructor(onConstructor_ = {@Lazy})
public class SysNoticeController extends BaseController implements IBaseController {
    private final ISysNoticeDao noticeService;

    /**
     * 获取通知公告列表
     */

    @GetMapping("/list")
    public TableDataInfo<SysNotice> list(SysNotice notice) {
        return getDataTable(() -> noticeService.list(notice));
    }

    /**
     * 根据通知公告编号获取详细信息
     */

    @GetMapping(value = "/{noticeId}")
    public AjaxResult getInfo(@PathVariable Long noticeId) {
        return success(noticeService.getByIdDeep(noticeId));
    }

    /**
     * 新增通知公告
     */

    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysNotice notice) {
        return toAjax(noticeService.save(notice));
    }

    /**
     * 修改通知公告
     */

    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysNotice notice) {
        return toAjax(noticeService.updateById(notice));
    }

    /**
     * 删除通知公告
     */

    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public AjaxResult remove(@PathVariable Long[] noticeIds) {
        return toAjax(noticeService.removeById(noticeIds));
    }
}
